import { app, <PERSON><PERSON>erWindow, Tray, <PERSON>u, screen, nativeImage, ipcMain } from 'electron';
import * as path from 'path';
import { exec } from 'child_process';

interface WindowInfo {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  title: string;
}

let mainWindow: BrowserWindow | null = null;
let tray: Tray | null = null;

// Window detection functions
function getWindowList(): Promise<WindowInfo[]> {
  return new Promise((resolve, reject) => {
    exec('wmctrl -lG', (error, stdout, stderr) => {
      if (error) {
        console.error('Error getting window list:', error);
        resolve([]);
        return;
      }

      const windows: WindowInfo[] = [];
      const lines = stdout.trim().split('\n');

      for (const line of lines) {
        const parts = line.trim().split(/\s+/);
        if (parts.length >= 7) {
          const id = parts[0];
          const x = parseInt(parts[2]);
          const y = parseInt(parts[3]);
          const width = parseInt(parts[4]);
          const height = parseInt(parts[5]);
          const title = parts.slice(7).join(' ');

          // Filter out our own window and system windows
          if (!title.includes('Desktop Pet') &&
              !title.includes('Desktop') &&
              width > 50 && height > 50) {
            windows.push({ id, x, y, width, height, title });
          }
        }
      }

      resolve(windows);
    });
  });
}

function getTaskbarInfo(): Promise<{x: number, y: number, width: number, height: number}> {
  return new Promise((resolve) => {
    const primaryDisplay = screen.getPrimaryDisplay();
    const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize;
    const { width: fullWidth, height: fullHeight } = primaryDisplay.bounds;

    // Linux Mint taskbar is typically at the bottom
    const taskbarHeight = fullHeight - screenHeight;
    const taskbarY = screenHeight;

    resolve({
      x: 0,
      y: taskbarY,
      width: fullWidth,
      height: taskbarHeight
    });
  });
}

function createWindow(): void {
  // Get the primary display dimensions
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width, height } = primaryDisplay.workAreaSize;

  // Create the browser window with full screen transparency
  mainWindow = new BrowserWindow({
    width: width,
    height: height,
    x: 0,
    y: 0,
    transparent: true,
    frame: false,
    alwaysOnTop: true,
    skipTaskbar: true,
    resizable: false,
    movable: false,
    minimizable: false,
    maximizable: false,
    closable: false,
    focusable: false,
    show: false,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      webSecurity: false
    }
  });

  // Initially set window to ignore mouse events (click-through)
  mainWindow.setIgnoreMouseEvents(true, { forward: true });

  // Load the HTML file
  mainWindow.loadFile(path.join(__dirname, 'renderer.html'));

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow?.show();
  });

  // Prevent window from being closed
  mainWindow.on('close', (event) => {
    event.preventDefault();
  });
}

// IPC handlers for window detection and mouse events
function setupIPC(): void {
  // Handle window list requests
  ipcMain.handle('get-windows', async () => {
    return await getWindowList();
  });

  // Handle taskbar info requests
  ipcMain.handle('get-taskbar', async () => {
    return await getTaskbarInfo();
  });

  // Handle mouse event toggling for dragging
  ipcMain.on('set-mouse-events', (event, enabled: boolean) => {
    if (mainWindow) {
      mainWindow.setIgnoreMouseEvents(!enabled, { forward: true });
    }
  });
}

function createTray(): void {
  // Create a simple tray icon (we'll use the new tray icon)
  const trayIcon = nativeImage.createFromPath(path.join(__dirname, 'assets', 'tray-icon.jpg'));
  tray = new Tray(trayIcon.resize({ width: 16, height: 16 }));

  // Create context menu for tray
  const contextMenu = Menu.buildFromTemplate([
    {
      label: 'Desktop Pet',
      enabled: false
    },
    {
      type: 'separator'
    },
    {
      label: 'Quit',
      click: () => {
        app.quit();
      }
    }
  ]);

  tray.setToolTip('Desktop Pet');
  tray.setContextMenu(contextMenu);
}

// This method will be called when Electron has finished initialization
app.whenReady().then(() => {
  createWindow();
  createTray();
  setupIPC();

  app.on('activate', () => {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

// Quit when all windows are closed, except on macOS
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit();
});

// Prevent the app from quitting when all windows are closed
app.on('before-quit', () => {
  if (mainWindow) {
    mainWindow.removeAllListeners('close');
    mainWindow.close();
  }
});

// Hide dock icon on macOS (though this is for Linux, keeping for compatibility)
if (process.platform === 'darwin') {
  app.dock?.hide();
}
