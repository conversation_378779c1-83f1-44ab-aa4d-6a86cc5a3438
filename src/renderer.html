<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Desktop Pet</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: transparent;
            overflow: hidden;
            width: 100vw;
            height: 100vh;
            position: relative;
            cursor: none;
        }

        #pet {
            position: absolute;
            width: 64px;
            height: 64px;
            background-image: url('./assets/pet.gif');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
            z-index: 9999;
            cursor: grab;
            user-select: none;
            image-rendering: pixelated;
        }

        #pet.dragging {
            cursor: grabbing;
            filter: brightness(1.2);
        }

        #pet.falling {
            transition: none;
        }

        #pet.idle {
            transition: all 0.5s ease-in-out;
        }

        #pet.roaming {
            transition: all 2s ease-in-out;
        }

        /* Fallback if no image is available */
        #pet.fallback {
            background: radial-gradient(circle, #ff6b6b, #ee5a24);
            border-radius: 50%;
            border: 2px solid #ffffff;
            box-shadow: 0 0 10px rgba(255, 107, 107, 0.5);
        }

        /* Animation classes */
        .bounce {
            animation: bounce 1s ease-in-out;
        }

        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-20px); }
        }

        .wiggle {
            animation: wiggle 0.5s ease-in-out;
        }

        @keyframes wiggle {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(-5deg); }
            75% { transform: rotate(5deg); }
        }

        .scale-small { transform: scale(0.8); }
        .scale-normal { transform: scale(1.0); }
        .scale-large { transform: scale(1.2); }
    </style>
</head>
<body>
    <div id="pet"></div>

    <script>
        const { ipcRenderer } = require('electron');

        // Pet states
        const PetState = {
            IDLE: 'idle',
            ROAMING: 'roaming',
            DRAGGING: 'dragging',
            FALLING: 'falling'
        };

        class AdvancedDesktopPet {
            constructor() {
                this.pet = document.getElementById('pet');
                this.screenWidth = window.innerWidth;
                this.screenHeight = window.innerHeight;
                this.petSize = 64;
                this.scale = 1.0;

                // Position and physics
                this.x = Math.random() * (this.screenWidth - this.petSize);
                this.y = this.screenHeight - 100; // Start near taskbar
                this.velocityX = 0;
                this.velocityY = 0;
                this.gravity = 0.5;
                this.bounce = 0.3;
                this.friction = 0.8;

                // State management
                this.state = PetState.IDLE;
                this.stateTimer = 0;
                this.lastStateChange = Date.now();

                // Window and floor detection
                this.windows = [];
                this.taskbar = null;
                this.floors = [];
                this.currentFloor = null;

                // Dragging
                this.isDragging = false;
                this.dragOffset = { x: 0, y: 0 };
                this.lastMousePos = { x: 0, y: 0 };

                // Animation frame
                this.animationId = null;

                this.init();
            }

            async init() {
                // Set initial position
                this.updatePosition();

                // Load window and taskbar information
                await this.updateWindowInfo();

                // Set up event listeners
                this.setupEventListeners();

                // Start the main loop
                this.startMainLoop();

                // Update window info periodically
                setInterval(() => this.updateWindowInfo(), 2000);
            }

            async updateWindowInfo() {
                try {
                    this.windows = await ipcRenderer.invoke('get-windows');
                    this.taskbar = await ipcRenderer.invoke('get-taskbar');
                    this.updateFloors();
                } catch (error) {
                    console.error('Error updating window info:', error);
                }
            }

            updateFloors() {
                this.floors = [];

                // Add taskbar as primary floor
                if (this.taskbar) {
                    this.floors.push({
                        x: this.taskbar.x,
                        y: this.taskbar.y,
                        width: this.taskbar.width,
                        height: this.taskbar.height,
                        type: 'taskbar'
                    });
                }

                // Add window tops as floors
                for (const window of this.windows) {
                    this.floors.push({
                        x: window.x,
                        y: window.y,
                        width: window.width,
                        height: 5, // Thin floor on top of window
                        type: 'window',
                        windowId: window.id
                    });
                }
            }

            setupEventListeners() {
                // Mouse events for dragging
                this.pet.addEventListener('mousedown', (e) => this.startDrag(e));
                document.addEventListener('mousemove', (e) => this.drag(e));
                document.addEventListener('mouseup', () => this.endDrag());

                // Window resize
                window.addEventListener('resize', () => {
                    this.screenWidth = window.innerWidth;
                    this.screenHeight = window.innerHeight;
                });

                // Prevent context menu
                document.addEventListener('contextmenu', (e) => e.preventDefault());
            }

            startDrag(e) {
                this.isDragging = true;
                this.state = PetState.DRAGGING;
                this.pet.classList.add('dragging');

                // Enable mouse events for dragging
                ipcRenderer.send('set-mouse-events', true);

                // Calculate drag offset
                const rect = this.pet.getBoundingClientRect();
                this.dragOffset.x = e.clientX - rect.left;
                this.dragOffset.y = e.clientY - rect.top;

                // Stop physics
                this.velocityX = 0;
                this.velocityY = 0;

                e.preventDefault();
            }

            drag(e) {
                if (!this.isDragging) return;

                this.lastMousePos.x = e.clientX;
                this.lastMousePos.y = e.clientY;

                // Update position based on mouse
                this.x = e.clientX - this.dragOffset.x;
                this.y = e.clientY - this.dragOffset.y;

                // Keep within screen bounds
                this.x = Math.max(0, Math.min(this.screenWidth - this.petSize, this.x));
                this.y = Math.max(0, Math.min(this.screenHeight - this.petSize, this.y));

                this.updatePosition();
            }

            endDrag() {
                if (!this.isDragging) return;

                this.isDragging = false;
                this.pet.classList.remove('dragging');

                // Disable mouse events (return to click-through)
                ipcRenderer.send('set-mouse-events', false);

                // Start falling
                this.state = PetState.FALLING;
                this.velocityY = 2; // Initial fall velocity
            }

            checkFloorCollision() {
                const petBottom = this.y + this.petSize;
                const petLeft = this.x;
                const petRight = this.x + this.petSize;

                for (const floor of this.floors) {
                    const floorTop = floor.y;
                    const floorBottom = floor.y + floor.height;
                    const floorLeft = floor.x;
                    const floorRight = floor.x + floor.width;

                    // Check if pet is above and intersecting with floor horizontally
                    if (petBottom >= floorTop && petBottom <= floorBottom + 10 &&
                        petRight > floorLeft && petLeft < floorRight &&
                        this.velocityY >= 0) {

                        // Land on floor
                        this.y = floorTop - this.petSize;
                        this.velocityY *= -this.bounce;
                        this.currentFloor = floor;

                        // If velocity is low enough, stop bouncing
                        if (Math.abs(this.velocityY) < 1) {
                            this.velocityY = 0;
                            this.state = PetState.IDLE;
                            this.lastStateChange = Date.now();
                        }

                        return true;
                    }
                }

                // Check ground (screen bottom)
                if (petBottom >= this.screenHeight) {
                    this.y = this.screenHeight - this.petSize;
                    this.velocityY *= -this.bounce;

                    if (Math.abs(this.velocityY) < 1) {
                        this.velocityY = 0;
                        this.state = PetState.IDLE;
                        this.lastStateChange = Date.now();
                    }
                    return true;
                }

                return false;
            }

            updatePhysics() {
                if (this.state === PetState.DRAGGING) return;

                if (this.state === PetState.FALLING) {
                    // Apply gravity
                    this.velocityY += this.gravity;

                    // Apply friction to horizontal movement
                    this.velocityX *= this.friction;

                    // Update position
                    this.x += this.velocityX;
                    this.y += this.velocityY;

                    // Keep within screen bounds horizontally
                    if (this.x < 0) {
                        this.x = 0;
                        this.velocityX *= -this.bounce;
                    } else if (this.x > this.screenWidth - this.petSize) {
                        this.x = this.screenWidth - this.petSize;
                        this.velocityX *= -this.bounce;
                    }

                    // Check floor collisions
                    this.checkFloorCollision();
                }
            }

            updateState() {
                const now = Date.now();
                const timeSinceStateChange = now - this.lastStateChange;

                switch (this.state) {
                    case PetState.IDLE:
                        if (timeSinceStateChange > 2000 + Math.random() * 3000) {
                            // Randomly decide to roam or perform animation
                            if (Math.random() < 0.7) {
                                this.startRoaming();
                            } else {
                                this.performRandomAnimation();
                            }
                        }
                        break;

                    case PetState.ROAMING:
                        // Roaming is handled by CSS transitions
                        break;

                    case PetState.FALLING:
                        // Physics handles falling state
                        break;
                }
            }

            startRoaming() {
                if (this.state === PetState.DRAGGING || this.state === PetState.FALLING) return;

                this.state = PetState.ROAMING;
                this.pet.className = 'roaming';

                // Find a target position on current floor or nearby floor
                let targetX, targetY;

                if (this.currentFloor) {
                    // Move along current floor
                    const floorLeft = Math.max(0, this.currentFloor.x);
                    const floorRight = Math.min(this.screenWidth - this.petSize,
                                               this.currentFloor.x + this.currentFloor.width - this.petSize);

                    if (floorRight > floorLeft) {
                        targetX = floorLeft + Math.random() * (floorRight - floorLeft);
                        targetY = this.currentFloor.y - this.petSize;
                    } else {
                        // Floor too narrow, fall off
                        this.state = PetState.FALLING;
                        this.velocityY = 1;
                        return;
                    }
                } else {
                    // No current floor, find a random floor
                    if (this.floors.length > 0) {
                        const randomFloor = this.floors[Math.floor(Math.random() * this.floors.length)];
                        targetX = randomFloor.x + Math.random() * Math.max(0, randomFloor.width - this.petSize);
                        targetY = randomFloor.y - this.petSize;
                    } else {
                        // No floors available, just move randomly
                        targetX = Math.random() * (this.screenWidth - this.petSize);
                        targetY = this.screenHeight - this.petSize - 50;
                    }
                }

                // Animate to target position
                this.x = targetX;
                this.y = targetY;
                this.updatePosition();

                // Return to idle after movement
                setTimeout(() => {
                    if (this.state === PetState.ROAMING) {
                        this.state = PetState.IDLE;
                        this.pet.className = 'idle';
                        this.lastStateChange = Date.now();
                    }
                }, 2000);
            }

            performRandomAnimation() {
                const animations = ['bounce', 'wiggle'];
                const randomAnimation = animations[Math.floor(Math.random() * animations.length)];

                this.pet.classList.add(randomAnimation);
                setTimeout(() => {
                    this.pet.classList.remove(randomAnimation);
                    this.lastStateChange = Date.now();
                }, 1000);
            }

            updateScale() {
                // Dynamic scaling based on screen size
                const baseSize = 64;
                const screenArea = this.screenWidth * this.screenHeight;
                const baseArea = 1920 * 1080;

                this.scale = Math.sqrt(screenArea / baseArea);
                this.scale = Math.max(0.5, Math.min(2.0, this.scale)); // Clamp between 0.5x and 2x

                this.petSize = baseSize * this.scale;
                this.pet.style.width = this.petSize + 'px';
                this.pet.style.height = this.petSize + 'px';
            }

            updatePosition() {
                this.pet.style.left = this.x + 'px';
                this.pet.style.top = this.y + 'px';
            }

            startMainLoop() {
                const loop = () => {
                    this.updatePhysics();
                    this.updateState();
                    this.updatePosition();

                    this.animationId = requestAnimationFrame(loop);
                };

                loop();
            }

            destroy() {
                if (this.animationId) {
                    cancelAnimationFrame(this.animationId);
                }
            }
        }

        // Initialize the pet when the page loads
        window.addEventListener('DOMContentLoaded', () => {
            const pet = new AdvancedDesktopPet();

            // Handle window unload
            window.addEventListener('beforeunload', () => {
                pet.destroy();
            });
        });
    </script>
</body>
</html>
</body>
</html>
