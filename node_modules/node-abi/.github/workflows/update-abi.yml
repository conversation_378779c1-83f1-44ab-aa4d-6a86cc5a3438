name: Auto-update ABI JSON file
on:
  schedule:
    - cron: '0 * * * *'
jobs:
  autoupdate:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - uses: actions/setup-node@v1
      with:
        node-version: '12.x'
    - name: Get npm cache directory
      id: npm-cache
      run: |
        echo "::set-output name=dir::$(npm config get cache)"
    - uses: actions/cache@v1
      with:
        path: ${{ steps.npm-cache.outputs.dir }}
        key: ${{ runner.os }}-node-${{ hashFiles('**/package.json') }}
        restore-keys: |
          ${{ runner.os }}-node-
    - run: npm install --no-package-lock
    - name: Update ABI registry
      run: npm run update-abi-registry
    - name: Commit Changes to ABI registry
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      run: |
        echo "machine github.com login $GITHUB_ACTOR password $GITHUB_TOKEN" > ~/.netrc
        chmod 600 ~/.netrc
        git add abi_registry.json
        if test -n "$(git status -s)"; then
          git config user.name "GitHub Actions"
          git config user.email "<EMAIL>"
          git diff --cached
          git commit -m "feat: update ABI registry"
          git push origin HEAD:$GITHUB_REF
        else
          echo No update needed
        fi
