{"name": "x11", "author": "<PERSON><PERSON> <<EMAIL>>", "description": "A pure node.js JavaScript client implementing X Window (X11) protocol and extensions.", "keywords": ["X Window", "ui", "gui", "widgets", "desktop", "XWindow", "X"], "homepage": "https://github.com/sidorares/node-x11", "version": "2.3.0", "maintainers": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Santiago Gimeno", "email": "<EMAIL>"}], "bugs": {"url": "http://github.com/sidorares/node-x11/issues"}, "licenses": [{"type": "MIT"}], "repository": {"type": "git", "url": "http://github.com/sidorares/node-x11.git"}, "main": "./lib", "engines": {"node": "*"}, "devDependencies": {"mocha": "*", "should": "*", "sax": "*", "async": "*", "sinon": "*"}, "scripts": {"test": "node test-runner.js", "prepublish": "npm prune"}, "dependencies": {"os-homedir": "^1.0.1"}}