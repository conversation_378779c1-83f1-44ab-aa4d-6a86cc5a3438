module.exports = {
    VERSION_1_1: 1,
    VERSION_1_2: 1,
    VERSION_1_3: 1,
    FALSE: 0x0,
    TRUE: 0x1,
    BYTE: 0x1400,
    UNSIGNED_BYTE: 0x1401,
    SHORT: 0x1402,
    UNSIGNED_SHORT: 0x1403,
    INT: 0x1404,
    UNSIGNED_INT: 0x1405,
    FLOAT: 0x1406,
    "2_BYTES": 0x1407,
    "3_BYTES": 0x1408,
    "4_BYTES": 0x1409,
    DOUBLE: 0x140A,
    POINTS: 0x0000,
    LINES: 0x0001,
    LINE_LOOP: 0x0002,
    LINE_STRIP: 0x0003,
    TRIANGLES: 0x0004,
    TRIANGLE_STRIP: 0x0005,
    TRIANGLE_FAN: 0x0006,
    QUADS: 0x0007,
    QUAD_STRIP: 0x0008,
    POLYGON: 0x0009,
    VERTEX_ARRAY: 0x8074,
    NORMAL_ARRAY: 0x8075,
    COLOR_ARRAY: 0x8076,
    INDEX_ARRAY: 0x8077,
    TEXTURE_COORD_ARRAY: 0x8078,
    EDGE_FLAG_ARRAY: 0x8079,
    VERTEX_ARRAY_SIZE: 0x807A,
    VERTEX_ARRAY_TYPE: 0x807B,
    VERTEX_ARRAY_STRIDE: 0x807C,
    NORMAL_ARRAY_TYPE: 0x807E,
    NORMAL_ARRAY_STRIDE: 0x807F,
    COLOR_ARRAY_SIZE: 0x8081,
    COLOR_ARRAY_TYPE: 0x8082,
    COLOR_ARRAY_STRIDE: 0x8083,
    INDEX_ARRAY_TYPE: 0x8085,
    INDEX_ARRAY_STRIDE: 0x8086,
    TEXTURE_COORD_ARRAY_SIZE: 0x8088,
    TEXTURE_COORD_ARRAY_TYPE: 0x8089,
    TEXTURE_COORD_ARRAY_STRIDE: 0x808A,
    EDGE_FLAG_ARRAY_STRIDE: 0x808C,
    VERTEX_ARRAY_POINTER: 0x808E,
    NORMAL_ARRAY_POINTER: 0x808F,
    COLOR_ARRAY_POINTER: 0x8090,
    INDEX_ARRAY_POINTER: 0x8091,
    TEXTURE_COORD_ARRAY_POINTER: 0x8092,
    EDGE_FLAG_ARRAY_POINTER: 0x8093,
    V2F: 0x2A20,
    V3F: 0x2A21,
    C4UB_V2F: 0x2A22,
    C4UB_V3F: 0x2A23,
    C3F_V3F: 0x2A24,
    N3F_V3F: 0x2A25,
    C4F_N3F_V3F: 0x2A26,
    T2F_V3F: 0x2A27,
    T4F_V4F: 0x2A28,
    T2F_C4UB_V3F: 0x2A29,
    T2F_C3F_V3F: 0x2A2A,
    T2F_N3F_V3F: 0x2A2B,
    T2F_C4F_N3F_V3F: 0x2A2C,
    T4F_C4F_N3F_V4F: 0x2A2D,
    MATRIX_MODE: 0x0BA0,
    MODELVIEW: 0x1700,
    PROJECTION: 0x1701,
    TEXTURE: 0x1702,
    POINT_SMOOTH: 0x0B10,
    POINT_SIZE: 0x0B11,
    POINT_SIZE_GRANULARITY: 0x0B13,
    POINT_SIZE_RANGE: 0x0B12,
    LINE_SMOOTH: 0x0B20,
    LINE_STIPPLE: 0x0B24,
    LINE_STIPPLE_PATTERN: 0x0B25,
    LINE_STIPPLE_REPEAT: 0x0B26,
    LINE_WIDTH: 0x0B21,
    LINE_WIDTH_GRANULARITY: 0x0B23,
    LINE_WIDTH_RANGE: 0x0B22,
    POINT: 0x1B00,
    LINE: 0x1B01,
    FILL: 0x1B02,
    CW: 0x0900,
    CCW: 0x0901,
    FRONT: 0x0404,
    BACK: 0x0405,
    POLYGON_MODE: 0x0B40,
    POLYGON_SMOOTH: 0x0B41,
    POLYGON_STIPPLE: 0x0B42,
    EDGE_FLAG: 0x0B43,
    CULL_FACE: 0x0B44,
    CULL_FACE_MODE: 0x0B45,
    FRONT_FACE: 0x0B46,
    POLYGON_OFFSET_FACTOR: 0x8038,
    POLYGON_OFFSET_UNITS: 0x2A00,
    POLYGON_OFFSET_POINT: 0x2A01,
    POLYGON_OFFSET_LINE: 0x2A02,
    POLYGON_OFFSET_FILL: 0x8037,
    COMPILE: 0x1300,
    COMPILE_AND_EXECUTE: 0x1301,
    LIST_BASE: 0x0B32,
    LIST_INDEX: 0x0B33,
    LIST_MODE: 0x0B30,
    NEVER: 0x0200,
    LESS: 0x0201,
    EQUAL: 0x0202,
    LEQUAL: 0x0203,
    GREATER: 0x0204,
    NOTEQUAL: 0x0205,
    GEQUAL: 0x0206,
    ALWAYS: 0x0207,
    DEPTH_TEST: 0x0B71,
    DEPTH_BITS: 0x0D56,
    DEPTH_CLEAR_VALUE: 0x0B73,
    DEPTH_FUNC: 0x0B74,
    DEPTH_RANGE: 0x0B70,
    DEPTH_WRITEMASK: 0x0B72,
    DEPTH_COMPONENT: 0x1902,
    LIGHTING: 0x0B50,
    LIGHT0: 0x4000,
    LIGHT1: 0x4001,
    LIGHT2: 0x4002,
    LIGHT3: 0x4003,
    LIGHT4: 0x4004,
    LIGHT5: 0x4005,
    LIGHT6: 0x4006,
    LIGHT7: 0x4007,
    SPOT_EXPONENT: 0x1205,
    SPOT_CUTOFF: 0x1206,
    CONSTANT_ATTENUATION: 0x1207,
    LINEAR_ATTENUATION: 0x1208,
    QUADRATIC_ATTENUATION: 0x1209,
    AMBIENT: 0x1200,
    DIFFUSE: 0x1201,
    SPECULAR: 0x1202,
    SHININESS: 0x1601,
    EMISSION: 0x1600,
    POSITION: 0x1203,
    SPOT_DIRECTION: 0x1204,
    AMBIENT_AND_DIFFUSE: 0x1602,
    COLOR_INDEXES: 0x1603,
    LIGHT_MODEL_TWO_SIDE: 0x0B52,
    LIGHT_MODEL_LOCAL_VIEWER: 0x0B51,
    LIGHT_MODEL_AMBIENT: 0x0B53,
    FRONT_AND_BACK: 0x0408,
    SHADE_MODEL: 0x0B54,
    FLAT: 0x1D00,
    SMOOTH: 0x1D01,
    COLOR_MATERIAL: 0x0B57,
    COLOR_MATERIAL_FACE: 0x0B55,
    COLOR_MATERIAL_PARAMETER: 0x0B56,
    NORMALIZE: 0x0BA1,
    CLIP_PLANE0: 0x3000,
    CLIP_PLANE1: 0x3001,
    CLIP_PLANE2: 0x3002,
    CLIP_PLANE3: 0x3003,
    CLIP_PLANE4: 0x3004,
    CLIP_PLANE5: 0x3005,
    ACCUM_RED_BITS: 0x0D58,
    ACCUM_GREEN_BITS: 0x0D59,
    ACCUM_BLUE_BITS: 0x0D5A,
    ACCUM_ALPHA_BITS: 0x0D5B,
    ACCUM_CLEAR_VALUE: 0x0B80,
    ACCUM: 0x0100,
    ADD: 0x0104,
    LOAD: 0x0101,
    MULT: 0x0103,
    RETURN: 0x0102,
    ALPHA_TEST: 0x0BC0,
    ALPHA_TEST_REF: 0x0BC2,
    ALPHA_TEST_FUNC: 0x0BC1,
    BLEND: 0x0BE2,
    BLEND_SRC: 0x0BE1,
    BLEND_DST: 0x0BE0,
    ZERO: 0x0,
    ONE: 0x1,
    SRC_COLOR: 0x0300,
    ONE_MINUS_SRC_COLOR: 0x0301,
    SRC_ALPHA: 0x0302,
    ONE_MINUS_SRC_ALPHA: 0x0303,
    DST_ALPHA: 0x0304,
    ONE_MINUS_DST_ALPHA: 0x0305,
    DST_COLOR: 0x0306,
    ONE_MINUS_DST_COLOR: 0x0307,
    SRC_ALPHA_SATURATE: 0x0308,
    FEEDBACK: 0x1C01,
    RENDER: 0x1C00,
    SELECT: 0x1C02,
    "2D": 0x0600,
    "3D": 0x0601,
    "3D_COLOR": 0x0602,
    "3D_COLOR_TEXTURE": 0x0603,
    "4D_COLOR_TEXTURE": 0x0604,
    POINT_TOKEN: 0x0701,
    LINE_TOKEN: 0x0702,
    LINE_RESET_TOKEN: 0x0707,
    POLYGON_TOKEN: 0x0703,
    BITMAP_TOKEN: 0x0704,
    DRAW_PIXEL_TOKEN: 0x0705,
    COPY_PIXEL_TOKEN: 0x0706,
    PASS_THROUGH_TOKEN: 0x0700,
    FEEDBACK_BUFFER_POINTER: 0x0DF0,
    FEEDBACK_BUFFER_SIZE: 0x0DF1,
    FEEDBACK_BUFFER_TYPE: 0x0DF2,
    SELECTION_BUFFER_POINTER: 0x0DF3,
    SELECTION_BUFFER_SIZE: 0x0DF4,
    FOG: 0x0B60,
    FOG_MODE: 0x0B65,
    FOG_DENSITY: 0x0B62,
    FOG_COLOR: 0x0B66,
    FOG_INDEX: 0x0B61,
    FOG_START: 0x0B63,
    FOG_END: 0x0B64,
    LINEAR: 0x2601,
    EXP: 0x0800,
    EXP2: 0x0801,
    LOGIC_OP: 0x0BF1,
    INDEX_LOGIC_OP: 0x0BF1,
    COLOR_LOGIC_OP: 0x0BF2,
    LOGIC_OP_MODE: 0x0BF0,
    CLEAR: 0x1500,
    SET: 0x150F,
    COPY: 0x1503,
    COPY_INVERTED: 0x150C,
    NOOP: 0x1505,
    INVERT: 0x150A,
    AND: 0x1501,
    NAND: 0x150E,
    OR: 0x1507,
    NOR: 0x1508,
    XOR: 0x1506,
    EQUIV: 0x1509,
    AND_REVERSE: 0x1502,
    AND_INVERTED: 0x1504,
    OR_REVERSE: 0x150B,
    OR_INVERTED: 0x150D,
    STENCIL_BITS: 0x0D57,
    STENCIL_TEST: 0x0B90,
    STENCIL_CLEAR_VALUE: 0x0B91,
    STENCIL_FUNC: 0x0B92,
    STENCIL_VALUE_MASK: 0x0B93,
    STENCIL_FAIL: 0x0B94,
    STENCIL_PASS_DEPTH_FAIL: 0x0B95,
    STENCIL_PASS_DEPTH_PASS: 0x0B96,
    STENCIL_REF: 0x0B97,
    STENCIL_WRITEMASK: 0x0B98,
    STENCIL_INDEX: 0x1901,
    KEEP: 0x1E00,
    REPLACE: 0x1E01,
    INCR: 0x1E02,
    DECR: 0x1E03,
    NONE: 0x0,
    LEFT: 0x0406,
    RIGHT: 0x0407,
    FRONT: 0x0404,
    BACK: 0x0405,
    FRONT_AND_BACK: 0x0408,
    FRONT_LEFT: 0x0400,
    FRONT_RIGHT: 0x0401,
    BACK_LEFT: 0x0402,
    BACK_RIGHT: 0x0403,
    AUX0: 0x0409,
    AUX1: 0x040A,
    AUX2: 0x040B,
    AUX3: 0x040C,
    COLOR_INDEX: 0x1900,
    RED: 0x1903,
    GREEN: 0x1904,
    BLUE: 0x1905,
    ALPHA: 0x1906,
    LUMINANCE: 0x1909,
    LUMINANCE_ALPHA: 0x190A,
    ALPHA_BITS: 0x0D55,
    RED_BITS: 0x0D52,
    GREEN_BITS: 0x0D53,
    BLUE_BITS: 0x0D54,
    INDEX_BITS: 0x0D51,
    SUBPIXEL_BITS: 0x0D50,
    AUX_BUFFERS: 0x0C00,
    READ_BUFFER: 0x0C02,
    DRAW_BUFFER: 0x0C01,
    DOUBLEBUFFER: 0x0C32,
    STEREO: 0x0C33,
    BITMAP: 0x1A00,
    COLOR: 0x1800,
    DEPTH: 0x1801,
    STENCIL: 0x1802,
    DITHER: 0x0BD0,
    RGB: 0x1907,
    RGBA: 0x1908,
    MAX_LIST_NESTING: 0x0B31,
    MAX_EVAL_ORDER: 0x0D30,
    MAX_LIGHTS: 0x0D31,
    MAX_CLIP_PLANES: 0x0D32,
    MAX_TEXTURE_SIZE: 0x0D33,
    MAX_PIXEL_MAP_TABLE: 0x0D34,
    MAX_ATTRIB_STACK_DEPTH: 0x0D35,
    MAX_MODELVIEW_STACK_DEPTH: 0x0D36,
    MAX_NAME_STACK_DEPTH: 0x0D37,
    MAX_PROJECTION_STACK_DEPTH: 0x0D38,
    MAX_TEXTURE_STACK_DEPTH: 0x0D39,
    MAX_VIEWPORT_DIMS: 0x0D3A,
    MAX_CLIENT_ATTRIB_STACK_DEPTH: 0x0D3B,
    ATTRIB_STACK_DEPTH: 0x0BB0,
    CLIENT_ATTRIB_STACK_DEPTH: 0x0BB1,
    COLOR_CLEAR_VALUE: 0x0C22,
    COLOR_WRITEMASK: 0x0C23,
    CURRENT_INDEX: 0x0B01,
    CURRENT_COLOR: 0x0B00,
    CURRENT_NORMAL: 0x0B02,
    CURRENT_RASTER_COLOR: 0x0B04,
    CURRENT_RASTER_DISTANCE: 0x0B09,
    CURRENT_RASTER_INDEX: 0x0B05,
    CURRENT_RASTER_POSITION: 0x0B07,
    CURRENT_RASTER_TEXTURE_COORDS: 0x0B06,
    CURRENT_RASTER_POSITION_VALID: 0x0B08,
    CURRENT_TEXTURE_COORDS: 0x0B03,
    INDEX_CLEAR_VALUE: 0x0C20,
    INDEX_MODE: 0x0C30,
    INDEX_WRITEMASK: 0x0C21,
    MODELVIEW_MATRIX: 0x0BA6,
    MODELVIEW_STACK_DEPTH: 0x0BA3,
    NAME_STACK_DEPTH: 0x0D70,
    PROJECTION_MATRIX: 0x0BA7,
    PROJECTION_STACK_DEPTH: 0x0BA4,
    RENDER_MODE: 0x0C40,
    RGBA_MODE: 0x0C31,
    TEXTURE_MATRIX: 0x0BA8,
    TEXTURE_STACK_DEPTH: 0x0BA5,
    VIEWPORT: 0x0BA2,
    AUTO_NORMAL: 0x0D80,
    MAP1_COLOR_4: 0x0D90,
    MAP1_INDEX: 0x0D91,
    MAP1_NORMAL: 0x0D92,
    MAP1_TEXTURE_COORD_1: 0x0D93,
    MAP1_TEXTURE_COORD_2: 0x0D94,
    MAP1_TEXTURE_COORD_3: 0x0D95,
    MAP1_TEXTURE_COORD_4: 0x0D96,
    MAP1_VERTEX_3: 0x0D97,
    MAP1_VERTEX_4: 0x0D98,
    MAP2_COLOR_4: 0x0DB0,
    MAP2_INDEX: 0x0DB1,
    MAP2_NORMAL: 0x0DB2,
    MAP2_TEXTURE_COORD_1: 0x0DB3,
    MAP2_TEXTURE_COORD_2: 0x0DB4,
    MAP2_TEXTURE_COORD_3: 0x0DB5,
    MAP2_TEXTURE_COORD_4: 0x0DB6,
    MAP2_VERTEX_3: 0x0DB7,
    MAP2_VERTEX_4: 0x0DB8,
    MAP1_GRID_DOMAIN: 0x0DD0,
    MAP1_GRID_SEGMENTS: 0x0DD1,
    MAP2_GRID_DOMAIN: 0x0DD2,
    MAP2_GRID_SEGMENTS: 0x0DD3,
    COEFF: 0x0A00,
    ORDER: 0x0A01,
    DOMAIN: 0x0A02,
    PERSPECTIVE_CORRECTION_HINT: 0x0C50,
    POINT_SMOOTH_HINT: 0x0C51,
    LINE_SMOOTH_HINT: 0x0C52,
    POLYGON_SMOOTH_HINT: 0x0C53,
    FOG_HINT: 0x0C54,
    DONT_CARE: 0x1100,
    FASTEST: 0x1101,
    NICEST: 0x1102,
    SCISSOR_BOX: 0x0C10,
    SCISSOR_TEST: 0x0C11,
    MAP_COLOR: 0x0D10,
    MAP_STENCIL: 0x0D11,
    INDEX_SHIFT: 0x0D12,
    INDEX_OFFSET: 0x0D13,
    RED_SCALE: 0x0D14,
    RED_BIAS: 0x0D15,
    GREEN_SCALE: 0x0D18,
    GREEN_BIAS: 0x0D19,
    BLUE_SCALE: 0x0D1A,
    BLUE_BIAS: 0x0D1B,
    ALPHA_SCALE: 0x0D1C,
    ALPHA_BIAS: 0x0D1D,
    DEPTH_SCALE: 0x0D1E,
    DEPTH_BIAS: 0x0D1F,
    PIXEL_MAP_S_TO_S_SIZE: 0x0CB1,
    PIXEL_MAP_I_TO_I_SIZE: 0x0CB0,
    PIXEL_MAP_I_TO_R_SIZE: 0x0CB2,
    PIXEL_MAP_I_TO_G_SIZE: 0x0CB3,
    PIXEL_MAP_I_TO_B_SIZE: 0x0CB4,
    PIXEL_MAP_I_TO_A_SIZE: 0x0CB5,
    PIXEL_MAP_R_TO_R_SIZE: 0x0CB6,
    PIXEL_MAP_G_TO_G_SIZE: 0x0CB7,
    PIXEL_MAP_B_TO_B_SIZE: 0x0CB8,
    PIXEL_MAP_A_TO_A_SIZE: 0x0CB9,
    PIXEL_MAP_S_TO_S: 0x0C71,
    PIXEL_MAP_I_TO_I: 0x0C70,
    PIXEL_MAP_I_TO_R: 0x0C72,
    PIXEL_MAP_I_TO_G: 0x0C73,
    PIXEL_MAP_I_TO_B: 0x0C74,
    PIXEL_MAP_I_TO_A: 0x0C75,
    PIXEL_MAP_R_TO_R: 0x0C76,
    PIXEL_MAP_G_TO_G: 0x0C77,
    PIXEL_MAP_B_TO_B: 0x0C78,
    PIXEL_MAP_A_TO_A: 0x0C79,
    PACK_ALIGNMENT: 0x0D05,
    PACK_LSB_FIRST: 0x0D01,
    PACK_ROW_LENGTH: 0x0D02,
    PACK_SKIP_PIXELS: 0x0D04,
    PACK_SKIP_ROWS: 0x0D03,
    PACK_SWAP_BYTES: 0x0D00,
    UNPACK_ALIGNMENT: 0x0CF5,
    UNPACK_LSB_FIRST: 0x0CF1,
    UNPACK_ROW_LENGTH: 0x0CF2,
    UNPACK_SKIP_PIXELS: 0x0CF4,
    UNPACK_SKIP_ROWS: 0x0CF3,
    UNPACK_SWAP_BYTES: 0x0CF0,
    ZOOM_X: 0x0D16,
    ZOOM_Y: 0x0D17,
    TEXTURE_ENV: 0x2300,
    TEXTURE_ENV_MODE: 0x2200,
    TEXTURE_1D: 0x0DE0,
    TEXTURE_2D: 0x0DE1,
    TEXTURE_WRAP_S: 0x2802,
    TEXTURE_WRAP_T: 0x2803,
    TEXTURE_MAG_FILTER: 0x2800,
    TEXTURE_MIN_FILTER: 0x2801,
    TEXTURE_ENV_COLOR: 0x2201,
    TEXTURE_GEN_S: 0x0C60,
    TEXTURE_GEN_T: 0x0C61,
    TEXTURE_GEN_R: 0x0C62,
    TEXTURE_GEN_Q: 0x0C63,
    TEXTURE_GEN_MODE: 0x2500,
    TEXTURE_BORDER_COLOR: 0x1004,
    TEXTURE_WIDTH: 0x1000,
    TEXTURE_HEIGHT: 0x1001,
    TEXTURE_BORDER: 0x1005,
    TEXTURE_COMPONENTS: 0x1003,
    TEXTURE_RED_SIZE: 0x805C,
    TEXTURE_GREEN_SIZE: 0x805D,
    TEXTURE_BLUE_SIZE: 0x805E,
    TEXTURE_ALPHA_SIZE: 0x805F,
    TEXTURE_LUMINANCE_SIZE: 0x8060,
    TEXTURE_INTENSITY_SIZE: 0x8061,
    NEAREST_MIPMAP_NEAREST: 0x2700,
    NEAREST_MIPMAP_LINEAR: 0x2702,
    LINEAR_MIPMAP_NEAREST: 0x2701,
    LINEAR_MIPMAP_LINEAR: 0x2703,
    OBJECT_LINEAR: 0x2401,
    OBJECT_PLANE: 0x2501,
    EYE_LINEAR: 0x2400,
    EYE_PLANE: 0x2502,
    SPHERE_MAP: 0x2402,
    DECAL: 0x2101,
    MODULATE: 0x2100,
    NEAREST: 0x2600,
    REPEAT: 0x2901,
    CLAMP: 0x2900,
    VENDOR: 0x1F00,
    RENDERER: 0x1F01,
    VERSION: 0x1F02,
    EXTENSIONS: 0x1F03,
    NO_ERROR: 0x0,
    INVALID_ENUM: 0x0500,
    INVALID_VALUE: 0x0501,
    INVALID_OPERATION: 0x0502,
    STACK_OVERFLOW: 0x0503,
    STACK_UNDERFLOW: 0x0504,
    OUT_OF_MEMORY: 0x0505,
    CURRENT_BIT: 0x00000001,
    POINT_BIT: 0x00000002,
    LINE_BIT: 0x00000004,
    POLYGON_BIT: 0x00000008,
    POLYGON_STIPPLE_BIT: 0x00000010,
    PIXEL_MODE_BIT: 0x00000020,
    LIGHTING_BIT: 0x00000040,
    FOG_BIT: 0x00000080,
    DEPTH_BUFFER_BIT: 0x00000100,
    ACCUM_BUFFER_BIT: 0x00000200,
    STENCIL_BUFFER_BIT: 0x00000400,
    VIEWPORT_BIT: 0x00000800,
    TRANSFORM_BIT: 0x00001000,
    ENABLE_BIT: 0x00002000,
    COLOR_BUFFER_BIT: 0x00004000,
    HINT_BIT: 0x00008000,
    EVAL_BIT: 0x00010000,
    LIST_BIT: 0x00020000,
    TEXTURE_BIT: 0x00040000,
    SCISSOR_BIT: 0x00080000,
    ALL_ATTRIB_BITS: 0x000FFFFF,
    PROXY_TEXTURE_1D: 0x8063,
    PROXY_TEXTURE_2D: 0x8064,
    TEXTURE_PRIORITY: 0x8066,
    TEXTURE_RESIDENT: 0x8067,
    TEXTURE_BINDING_1D: 0x8068,
    TEXTURE_BINDING_2D: 0x8069,
    TEXTURE_INTERNAL_FORMAT: 0x1003,
    ALPHA4: 0x803B,
    ALPHA8: 0x803C,
    ALPHA12: 0x803D,
    ALPHA16: 0x803E,
    LUMINANCE4: 0x803F,
    LUMINANCE8: 0x8040,
    LUMINANCE12: 0x8041,
    LUMINANCE16: 0x8042,
    LUMINANCE4_ALPHA4: 0x8043,
    LUMINANCE6_ALPHA2: 0x8044,
    LUMINANCE8_ALPHA8: 0x8045,
    LUMINANCE12_ALPHA4: 0x8046,
    LUMINANCE12_ALPHA12: 0x8047,
    LUMINANCE16_ALPHA16: 0x8048,
    INTENSITY: 0x8049,
    INTENSITY4: 0x804A,
    INTENSITY8: 0x804B,
    INTENSITY12: 0x804C,
    INTENSITY16: 0x804D,
    R3_G3_B2: 0x2A10,
    RGB4: 0x804F,
    RGB5: 0x8050,
    RGB8: 0x8051,
    RGB10: 0x8052,
    RGB12: 0x8053,
    RGB16: 0x8054,
    RGBA2: 0x8055,
    RGBA4: 0x8056,
    RGB5_A1: 0x8057,
    RGBA8: 0x8058,
    RGB10_A2: 0x8059,
    RGBA12: 0x805A,
    RGBA16: 0x805B,
    CLIENT_PIXEL_STORE_BIT: 0x00000001,
    CLIENT_VERTEX_ARRAY_BIT: 0x00000002,
    ALL_CLIENT_ATTRIB_BITS: 0xFFFFFFFF,
    CLIENT_ALL_ATTRIB_BITS: 0xFFFFFFFF,
    RESCALE_NORMAL: 0x803A,
    CLAMP_TO_EDGE: 0x812F,
    MAX_ELEMENTS_VERTICES: 0x80E8,
    MAX_ELEMENTS_INDICES: 0x80E9,
    BGR: 0x80E0,
    BGRA: 0x80E1,
    UNSIGNED_BYTE_3_3_2: 0x8032,
    UNSIGNED_BYTE_2_3_3_REV: 0x8362,
    UNSIGNED_SHORT_5_6_5: 0x8363,
    UNSIGNED_SHORT_5_6_5_REV: 0x8364,
    UNSIGNED_SHORT_4_4_4_4: 0x8033,
    UNSIGNED_SHORT_4_4_4_4_REV: 0x8365,
    UNSIGNED_SHORT_5_5_5_1: 0x8034,
    UNSIGNED_SHORT_1_5_5_5_REV: 0x8366,
    UNSIGNED_INT_8_8_8_8: 0x8035,
    UNSIGNED_INT_8_8_8_8_REV: 0x8367,
    UNSIGNED_INT_10_10_10_2: 0x8036,
    UNSIGNED_INT_2_10_10_10_REV: 0x8368,
    LIGHT_MODEL_COLOR_CONTROL: 0x81F8,
    SINGLE_COLOR: 0x81F9,
    SEPARATE_SPECULAR_COLOR: 0x81FA,
    TEXTURE_MIN_LOD: 0x813A,
    TEXTURE_MAX_LOD: 0x813B,
    TEXTURE_BASE_LEVEL: 0x813C,
    TEXTURE_MAX_LEVEL: 0x813D,
    SMOOTH_POINT_SIZE_RANGE: 0x0B12,
    SMOOTH_POINT_SIZE_GRANULARITY: 0x0B13,
    SMOOTH_LINE_WIDTH_RANGE: 0x0B22,
    SMOOTH_LINE_WIDTH_GRANULARITY: 0x0B23,
    ALIASED_POINT_SIZE_RANGE: 0x846D,
    ALIASED_LINE_WIDTH_RANGE: 0x846E,
    PACK_SKIP_IMAGES: 0x806B,
    PACK_IMAGE_HEIGHT: 0x806C,
    UNPACK_SKIP_IMAGES: 0x806D,
    UNPACK_IMAGE_HEIGHT: 0x806E,
    TEXTURE_3D: 0x806F,
    PROXY_TEXTURE_3D: 0x8070,
    TEXTURE_DEPTH: 0x8071,
    TEXTURE_WRAP_R: 0x8072,
    MAX_3D_TEXTURE_SIZE: 0x8073,
    TEXTURE_BINDING_3D: 0x806A,
    CONSTANT_COLOR: 0x8001,
    ONE_MINUS_CONSTANT_COLOR: 0x8002,
    CONSTANT_ALPHA: 0x8003,
    ONE_MINUS_CONSTANT_ALPHA: 0x8004,
    COLOR_TABLE: 0x80D0,
    POST_CONVOLUTION_COLOR_TABLE: 0x80D1,
    POST_COLOR_MATRIX_COLOR_TABLE: 0x80D2,
    PROXY_COLOR_TABLE: 0x80D3,
    PROXY_POST_CONVOLUTION_COLOR_TABLE: 0x80D4,
    PROXY_POST_COLOR_MATRIX_COLOR_TABLE: 0x80D5,
    COLOR_TABLE_SCALE: 0x80D6,
    COLOR_TABLE_BIAS: 0x80D7,
    COLOR_TABLE_FORMAT: 0x80D8,
    COLOR_TABLE_WIDTH: 0x80D9,
    COLOR_TABLE_RED_SIZE: 0x80DA,
    COLOR_TABLE_GREEN_SIZE: 0x80DB,
    COLOR_TABLE_BLUE_SIZE: 0x80DC,
    COLOR_TABLE_ALPHA_SIZE: 0x80DD,
    COLOR_TABLE_LUMINANCE_SIZE: 0x80DE,
    COLOR_TABLE_INTENSITY_SIZE: 0x80DF,
    CONVOLUTION_1D: 0x8010,
    CONVOLUTION_2D: 0x8011,
    SEPARABLE_2D: 0x8012,
    CONVOLUTION_BORDER_MODE: 0x8013,
    CONVOLUTION_FILTER_SCALE: 0x8014,
    CONVOLUTION_FILTER_BIAS: 0x8015,
    REDUCE: 0x8016,
    CONVOLUTION_FORMAT: 0x8017,
    CONVOLUTION_WIDTH: 0x8018,
    CONVOLUTION_HEIGHT: 0x8019,
    MAX_CONVOLUTION_WIDTH: 0x801A,
    MAX_CONVOLUTION_HEIGHT: 0x801B,
    POST_CONVOLUTION_RED_SCALE: 0x801C,
    POST_CONVOLUTION_GREEN_SCALE: 0x801D,
    POST_CONVOLUTION_BLUE_SCALE: 0x801E,
    POST_CONVOLUTION_ALPHA_SCALE: 0x801F,
    POST_CONVOLUTION_RED_BIAS: 0x8020,
    POST_CONVOLUTION_GREEN_BIAS: 0x8021,
    POST_CONVOLUTION_BLUE_BIAS: 0x8022,
    POST_CONVOLUTION_ALPHA_BIAS: 0x8023,
    CONSTANT_BORDER: 0x8151,
    REPLICATE_BORDER: 0x8153,
    CONVOLUTION_BORDER_COLOR: 0x8154,
    COLOR_MATRIX: 0x80B1,
    COLOR_MATRIX_STACK_DEPTH: 0x80B2,
    MAX_COLOR_MATRIX_STACK_DEPTH: 0x80B3,
    POST_COLOR_MATRIX_RED_SCALE: 0x80B4,
    POST_COLOR_MATRIX_GREEN_SCALE: 0x80B5,
    POST_COLOR_MATRIX_BLUE_SCALE: 0x80B6,
    POST_COLOR_MATRIX_ALPHA_SCALE: 0x80B7,
    POST_COLOR_MATRIX_RED_BIAS: 0x80B8,
    POST_COLOR_MATRIX_GREEN_BIAS: 0x80B9,
    POST_COLOR_MATRIX_BLUE_BIAS: 0x80BA,
    POST_COLOR_MATRIX_ALPHA_BIAS: 0x80BB,
    HISTOGRAM: 0x8024,
    PROXY_HISTOGRAM: 0x8025,
    HISTOGRAM_WIDTH: 0x8026,
    HISTOGRAM_FORMAT: 0x8027,
    HISTOGRAM_RED_SIZE: 0x8028,
    HISTOGRAM_GREEN_SIZE: 0x8029,
    HISTOGRAM_BLUE_SIZE: 0x802A,
    HISTOGRAM_ALPHA_SIZE: 0x802B,
    HISTOGRAM_LUMINANCE_SIZE: 0x802C,
    HISTOGRAM_SINK: 0x802D,
    MINMAX: 0x802E,
    MINMAX_FORMAT: 0x802F,
    MINMAX_SINK: 0x8030,
    TABLE_TOO_LARGE: 0x8031,
    BLEND_EQUATION: 0x8009,
    MIN: 0x8007,
    MAX: 0x8008,
    FUNC_ADD: 0x8006,
    FUNC_SUBTRACT: 0x800A,
    FUNC_REVERSE_SUBTRACT: 0x800B,
    BLEND_COLOR: 0x8005,
    TEXTURE0: 0x84C0,
    TEXTURE1: 0x84C1,
    TEXTURE2: 0x84C2,
    TEXTURE3: 0x84C3,
    TEXTURE4: 0x84C4,
    TEXTURE5: 0x84C5,
    TEXTURE6: 0x84C6,
    TEXTURE7: 0x84C7,
    TEXTURE8: 0x84C8,
    TEXTURE9: 0x84C9,
    TEXTURE10: 0x84CA,
    TEXTURE11: 0x84CB,
    TEXTURE12: 0x84CC,
    TEXTURE13: 0x84CD,
    TEXTURE14: 0x84CE,
    TEXTURE15: 0x84CF,
    TEXTURE16: 0x84D0,
    TEXTURE17: 0x84D1,
    TEXTURE18: 0x84D2,
    TEXTURE19: 0x84D3,
    TEXTURE20: 0x84D4,
    TEXTURE21: 0x84D5,
    TEXTURE22: 0x84D6,
    TEXTURE23: 0x84D7,
    TEXTURE24: 0x84D8,
    TEXTURE25: 0x84D9,
    TEXTURE26: 0x84DA,
    TEXTURE27: 0x84DB,
    TEXTURE28: 0x84DC,
    TEXTURE29: 0x84DD,
    TEXTURE30: 0x84DE,
    TEXTURE31: 0x84DF,
    ACTIVE_TEXTURE: 0x84E0,
    CLIENT_ACTIVE_TEXTURE: 0x84E1,
    MAX_TEXTURE_UNITS: 0x84E2,
    NORMAL_MAP: 0x8511,
    REFLECTION_MAP: 0x8512,
    TEXTURE_CUBE_MAP: 0x8513,
    TEXTURE_BINDING_CUBE_MAP: 0x8514,
    TEXTURE_CUBE_MAP_POSITIVE_X: 0x8515,
    TEXTURE_CUBE_MAP_NEGATIVE_X: 0x8516,
    TEXTURE_CUBE_MAP_POSITIVE_Y: 0x8517,
    TEXTURE_CUBE_MAP_NEGATIVE_Y: 0x8518,
    TEXTURE_CUBE_MAP_POSITIVE_Z: 0x8519,
    TEXTURE_CUBE_MAP_NEGATIVE_Z: 0x851A,
    PROXY_TEXTURE_CUBE_MAP: 0x851B,
    MAX_CUBE_MAP_TEXTURE_SIZE: 0x851C,
    COMPRESSED_ALPHA: 0x84E9,
    COMPRESSED_LUMINANCE: 0x84EA,
    COMPRESSED_LUMINANCE_ALPHA: 0x84EB,
    COMPRESSED_INTENSITY: 0x84EC,
    COMPRESSED_RGB: 0x84ED,
    COMPRESSED_RGBA: 0x84EE,
    TEXTURE_COMPRESSION_HINT: 0x84EF,
    TEXTURE_COMPRESSED_IMAGE_SIZE: 0x86A0,
    TEXTURE_COMPRESSED: 0x86A1,
    NUM_COMPRESSED_TEXTURE_FORMATS: 0x86A2,
    COMPRESSED_TEXTURE_FORMATS: 0x86A3,
    MULTISAMPLE: 0x809D,
    SAMPLE_ALPHA_TO_COVERAGE: 0x809E,
    SAMPLE_ALPHA_TO_ONE: 0x809F,
    SAMPLE_COVERAGE: 0x80A0,
    SAMPLE_BUFFERS: 0x80A8,
    SAMPLES: 0x80A9,
    SAMPLE_COVERAGE_VALUE: 0x80AA,
    SAMPLE_COVERAGE_INVERT: 0x80AB,
    MULTISAMPLE_BIT: 0x20000000,
    TRANSPOSE_MODELVIEW_MATRIX: 0x84E3,
    TRANSPOSE_PROJECTION_MATRIX: 0x84E4,
    TRANSPOSE_TEXTURE_MATRIX: 0x84E5,
    TRANSPOSE_COLOR_MATRIX: 0x84E6,
    COMBINE: 0x8570,
    COMBINE_RGB: 0x8571,
    COMBINE_ALPHA: 0x8572,
    SOURCE0_RGB: 0x8580,
    SOURCE1_RGB: 0x8581,
    SOURCE2_RGB: 0x8582,
    SOURCE0_ALPHA: 0x8588,
    SOURCE1_ALPHA: 0x8589,
    SOURCE2_ALPHA: 0x858A,
    OPERAND0_RGB: 0x8590,
    OPERAND1_RGB: 0x8591,
    OPERAND2_RGB: 0x8592,
    OPERAND0_ALPHA: 0x8598,
    OPERAND1_ALPHA: 0x8599,
    OPERAND2_ALPHA: 0x859A,
    RGB_SCALE: 0x8573,
    ADD_SIGNED: 0x8574,
    INTERPOLATE: 0x8575,
    SUBTRACT: 0x84E7,
    CONSTANT: 0x8576,
    PRIMARY_COLOR: 0x8577,
    PREVIOUS: 0x8578,
    DOT3_RGB: 0x86AE,
    DOT3_RGBA: 0x86AF,
    CLAMP_TO_BORDER: 0x812D,
    TEXTURE0_ARB: 0x84C0,
    TEXTURE1_ARB: 0x84C1,
    TEXTURE2_ARB: 0x84C2,
    TEXTURE3_ARB: 0x84C3,
    TEXTURE4_ARB: 0x84C4,
    TEXTURE5_ARB: 0x84C5,
    TEXTURE6_ARB: 0x84C6,
    TEXTURE7_ARB: 0x84C7,
    TEXTURE8_ARB: 0x84C8,
    TEXTURE9_ARB: 0x84C9,
    TEXTURE10_ARB: 0x84CA,
    TEXTURE11_ARB: 0x84CB,
    TEXTURE12_ARB: 0x84CC,
    TEXTURE13_ARB: 0x84CD,
    TEXTURE14_ARB: 0x84CE,
    TEXTURE15_ARB: 0x84CF,
    TEXTURE16_ARB: 0x84D0,
    TEXTURE17_ARB: 0x84D1,
    TEXTURE18_ARB: 0x84D2,
    TEXTURE19_ARB: 0x84D3,
    TEXTURE20_ARB: 0x84D4,
    TEXTURE21_ARB: 0x84D5,
    TEXTURE22_ARB: 0x84D6,
    TEXTURE23_ARB: 0x84D7,
    TEXTURE24_ARB: 0x84D8,
    TEXTURE25_ARB: 0x84D9,
    TEXTURE26_ARB: 0x84DA,
    TEXTURE27_ARB: 0x84DB,
    TEXTURE28_ARB: 0x84DC,
    TEXTURE29_ARB: 0x84DD,
    TEXTURE30_ARB: 0x84DE,
    TEXTURE31_ARB: 0x84DF,
    ACTIVE_TEXTURE_ARB: 0x84E0,
    CLIENT_ACTIVE_TEXTURE_ARB: 0x84E1,
    MAX_TEXTURE_UNITS_ARB: 0x84E2,
    DEBUG_OBJECT_MESA: 0x8759,
    DEBUG_PRINT_MESA: 0x875A,
    DEBUG_ASSERT_MESA: 0x875B,
    DEPTH_STENCIL_MESA: 0x8750,
    UNSIGNED_INT_24_8_MESA: 0x8751,
    UNSIGNED_INT_8_24_REV_MESA: 0x8752,
    UNSIGNED_SHORT_15_1_MESA: 0x8753,
    UNSIGNED_SHORT_1_15_REV_MESA: 0x8754,
    FRAGMENT_PROGRAM_POSITION_MESA: 0x8bb0,
    FRAGMENT_PROGRAM_CALLBACK_MESA: 0x8bb1,
    FRAGMENT_PROGRAM_CALLBACK_FUNC_MESA: 0x8bb2,
    FRAGMENT_PROGRAM_CALLBACK_DATA_MESA: 0x8bb3,
    VERTEX_PROGRAM_POSITION_MESA: 0x8bb4,
    VERTEX_PROGRAM_CALLBACK_MESA: 0x8bb5,
    VERTEX_PROGRAM_CALLBACK_FUNC_MESA: 0x8bb6,
    VERTEX_PROGRAM_CALLBACK_DATA_MESA: 0x8bb7,
    TEXTURE_1D_ARRAY_EXT: 0x8C18,
    PROXY_TEXTURE_1D_ARRAY_EXT: 0x8C19,
    TEXTURE_2D_ARRAY_EXT: 0x8C1A,
    PROXY_TEXTURE_2D_ARRAY_EXT: 0x8C1B,
    TEXTURE_BINDING_1D_ARRAY_EXT: 0x8C1C,
    TEXTURE_BINDING_2D_ARRAY_EXT: 0x8C1D,
    MAX_ARRAY_TEXTURE_LAYERS_EXT: 0x88FF,
    FRAMEBUFFER_ATTACHMENT_TEXTURE_LAYER_EXT: 0x8CD4,
    ALPHA_BLEND_EQUATION_ATI: 0x883D
}
