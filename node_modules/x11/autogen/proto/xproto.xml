<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright (C) 2001-2004 <PERSON>, <PERSON><PERSON>, and <PERSON>.
All Rights Reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

Except as contained in this notice, the names of the authors or their
institutions shall not be used in advertising or otherwise to promote the
sale, use or other dealings in this Software without prior written
authorization from the authors.
-->
<xcb header="xproto">
  <!-- Core protocol types -->
  
  <struct name="CHAR2B">
    <field type="CARD8" name="byte1" />
    <field type="CARD8" name="byte2" />
  </struct>
  
  <xidtype name="WINDOW" />
  
  <xidtype name="PIXMAP" />
  
  <xidtype name="CURSOR" />
  
  <xidtype name="FONT" />
  
  <xidtype name="GCONTEXT" />
  
  <xidtype name="COLORMAP" />
  
  <xidtype name="ATOM" />
  
  <xidunion name="DRAWABLE">
    <type>WINDOW</type>
    <type>PIXMAP</type>
  </xidunion>
  
  <xidunion name="FONTABLE">
    <type>FONT</type>
    <type>GCONTEXT</type>
  </xidunion>
  
  <typedef oldname="CARD32" newname="VISUALID" />

  <typedef oldname="CARD32" newname="TIMESTAMP" />

  <typedef oldname="CARD32" newname="KEYSYM" />

  <typedef oldname="CARD8" newname="KEYCODE" />

  <typedef oldname="CARD8" newname="BUTTON" />

  <struct name="POINT">
    <field type="INT16" name="x" />
    <field type="INT16" name="y" />
  </struct>

  <struct name="RECTANGLE">
    <field type="INT16" name="x" />
    <field type="INT16" name="y" />
    <field type="CARD16" name="width" />
    <field type="CARD16" name="height" />
  </struct>

  <struct name="ARC">
    <field type="INT16" name="x" />
    <field type="INT16" name="y" />
    <field type="CARD16" name="width" />
    <field type="CARD16" name="height" />
    <field type="INT16" name="angle1" />
    <field type="INT16" name="angle2" />
  </struct>

  <!-- Connection setup-related types -->

  <struct name="FORMAT">
    <field type="CARD8" name="depth" />
    <field type="CARD8" name="bits_per_pixel" />
    <field type="CARD8" name="scanline_pad" />
    <pad bytes="5" />
  </struct>

  <enum name="VisualClass">
    <item name="StaticGray"> <value>0</value></item>
    <item name="GrayScale">  <value>1</value></item>
    <item name="StaticColor"><value>2</value></item>
    <item name="PseudoColor"><value>3</value></item>
    <item name="TrueColor">  <value>4</value></item>
    <item name="DirectColor"><value>5</value></item>
  </enum>

  <struct name="VISUALTYPE">
    <field type="VISUALID" name="visual_id" />
    <field type="CARD8" name="class" enum="VisualClass" />
    <field type="CARD8" name="bits_per_rgb_value" />
    <field type="CARD16" name="colormap_entries" />
    <field type="CARD32" name="red_mask" />
    <field type="CARD32" name="green_mask" />
    <field type="CARD32" name="blue_mask" />
    <pad bytes="4" />
  </struct>

  <struct name="DEPTH">
    <field type="CARD8" name="depth" />
    <pad bytes="1" />
    <field type="CARD16" name="visuals_len" />
    <pad bytes="4" />
    <list type="VISUALTYPE" name="visuals">
      <fieldref>visuals_len</fieldref>
    </list>
  </struct>

  <enum name="EventMask">
    <item name="NoEvent">           <value>0</value></item>
    <item name="KeyPress">            <bit>0</bit></item>
    <item name="KeyRelease">          <bit>1</bit></item>
    <item name="ButtonPress">         <bit>2</bit></item>
    <item name="ButtonRelease">       <bit>3</bit></item>
    <item name="EnterWindow">         <bit>4</bit></item>
    <item name="LeaveWindow">         <bit>5</bit></item>
    <item name="PointerMotion">       <bit>6</bit></item>
    <item name="PointerMotionHint">   <bit>7</bit></item>
    <item name="Button1Motion">       <bit>8</bit></item>
    <item name="Button2Motion">       <bit>9</bit></item>
    <item name="Button3Motion">       <bit>10</bit></item>
    <item name="Button4Motion">       <bit>11</bit></item>
    <item name="Button5Motion">       <bit>12</bit></item>
    <item name="ButtonMotion">        <bit>13</bit></item>
    <item name="KeymapState">         <bit>14</bit></item>
    <item name="Exposure">            <bit>15</bit></item>
    <item name="VisibilityChange">    <bit>16</bit></item>
    <item name="StructureNotify">     <bit>17</bit></item>
    <item name="ResizeRedirect">      <bit>18</bit></item>
    <item name="SubstructureNotify">  <bit>19</bit></item>
    <item name="SubstructureRedirect"><bit>20</bit></item>
    <item name="FocusChange">         <bit>21</bit></item>
    <item name="PropertyChange">      <bit>22</bit></item>
    <item name="ColorMapChange">      <bit>23</bit></item>
    <item name="OwnerGrabButton">     <bit>24</bit></item>
  </enum>

  <enum name="BackingStore">
    <item name="NotUseful"> <value>0</value></item>
    <item name="WhenMapped"><value>1</value></item>
    <item name="Always">    <value>2</value></item>
  </enum>

  <struct name="SCREEN">
    <field type="WINDOW" name="root" />
    <field type="COLORMAP" name="default_colormap" />
    <field type="CARD32" name="white_pixel" />
    <field type="CARD32" name="black_pixel" />
    <field type="CARD32" name="current_input_masks" mask="EventMask" />
    <field type="CARD16" name="width_in_pixels" />
    <field type="CARD16" name="height_in_pixels" />
    <field type="CARD16" name="width_in_millimeters" />
    <field type="CARD16" name="height_in_millimeters" />
    <field type="CARD16" name="min_installed_maps" />
    <field type="CARD16" name="max_installed_maps" />
    <field type="VISUALID" name="root_visual" />
    <field type="BYTE" name="backing_stores" enum="BackingStore" />
    <field type="BOOL" name="save_unders" />
    <field type="CARD8" name="root_depth" />
    <field type="CARD8" name="allowed_depths_len" />
    <list type="DEPTH" name="allowed_depths">
      <fieldref>allowed_depths_len</fieldref>
    </list>
  </struct>

  <struct name="SetupRequest">
    <field type="CARD8" name="byte_order" />
    <pad bytes="1" />
    <field type="CARD16" name="protocol_major_version" />
    <field type="CARD16" name="protocol_minor_version" />
    <field type="CARD16" name="authorization_protocol_name_len" />
    <field type="CARD16" name="authorization_protocol_data_len" />
    <pad bytes="2" />
    <list type="char" name="authorization_protocol_name">
      <fieldref>authorization_protocol_name_len</fieldref>
    </list>
    <list type="char" name="authorization_protocol_data">
      <fieldref>authorization_protocol_data_len</fieldref>
    </list>
  </struct>

  <struct name="SetupFailed">
    <field type="CARD8" name="status" /> <!-- always 0 -> Failed -->
    <field type="CARD8" name="reason_len" />
    <field type="CARD16" name="protocol_major_version" />
    <field type="CARD16" name="protocol_minor_version" />
    <field type="CARD16" name="length" />
    <list type="char" name="reason">
      <fieldref>reason_len</fieldref>
    </list>
  </struct>

  <struct name="SetupAuthenticate">
    <field type="CARD8" name="status" /> <!-- always 2 -> Authenticate -->
    <pad bytes="5" />
    <field type="CARD16" name="length" />
    <list type="char" name="reason">
      <op op="*">
        <fieldref>length</fieldref>
        <value>4</value>
      </op>
    </list>
  </struct>

  <enum name="ImageOrder">
    <item name="LSBFirst"><value>0</value></item>
    <item name="MSBFirst"><value>1</value></item>
  </enum>

  <struct name="Setup">
    <field type="CARD8" name="status" /> <!-- always 1 -> Success -->
    <pad bytes="1" />
    <field type="CARD16" name="protocol_major_version" />
    <field type="CARD16" name="protocol_minor_version" />
    <field type="CARD16" name="length" />
    <field type="CARD32" name="release_number" />
    <field type="CARD32" name="resource_id_base" />
    <field type="CARD32" name="resource_id_mask" />
    <field type="CARD32" name="motion_buffer_size" />
    <field type="CARD16" name="vendor_len" />
    <field type="CARD16" name="maximum_request_length" />
    <field type="CARD8" name="roots_len" />
    <field type="CARD8" name="pixmap_formats_len" />
    <field type="CARD8" name="image_byte_order" enum="ImageOrder" />
    <field type="CARD8" name="bitmap_format_bit_order" enum="ImageOrder" />
    <field type="CARD8" name="bitmap_format_scanline_unit" />
    <field type="CARD8" name="bitmap_format_scanline_pad" />
    <field type="KEYCODE" name="min_keycode" />
    <field type="KEYCODE" name="max_keycode" />
    <pad bytes="4" />
    <list type="char" name="vendor">
      <fieldref>vendor_len</fieldref>
    </list>
    <list type="FORMAT" name="pixmap_formats">
      <fieldref>pixmap_formats_len</fieldref>
    </list>
    <list type="SCREEN" name="roots">
      <fieldref>roots_len</fieldref>
    </list>
  </struct>

  <!-- Core event types -->
  
  <enum name="ModMask">
    <item name="Shift">  <bit>0</bit></item>
    <item name="Lock">   <bit>1</bit></item>
    <item name="Control"><bit>2</bit></item>
    <item name="1">      <bit>3</bit></item>
    <item name="2">      <bit>4</bit></item>
    <item name="3">      <bit>5</bit></item>
    <item name="4">      <bit>6</bit></item>
    <item name="5">      <bit>7</bit></item>
    <item name="Any">    <bit>15</bit></item>
  </enum>

  <enum name="KeyButMask">
    <item name="Shift">  <bit>0</bit></item>
    <item name="Lock">   <bit>1</bit></item>
    <item name="Control"><bit>2</bit></item>
    <item name="Mod1">   <bit>3</bit></item>
    <item name="Mod2">   <bit>4</bit></item>
    <item name="Mod3">   <bit>5</bit></item>
    <item name="Mod4">   <bit>6</bit></item>
    <item name="Mod5">   <bit>7</bit></item>
    <item name="Button1"><bit>8</bit></item>
    <item name="Button2"><bit>9</bit></item>
    <item name="Button3"><bit>10</bit></item>
    <item name="Button4"><bit>11</bit></item>
    <item name="Button5"><bit>12</bit></item>
  </enum>

  <enum name="Window">
    <item name="None"> <value>0</value></item>
  </enum>

  <event name="KeyPress" number="2">
    <field type="KEYCODE" name="detail" />
    <field type="TIMESTAMP" name="time" />
    <field type="WINDOW" name="root" />
    <field type="WINDOW" name="event" />
    <field type="WINDOW" name="child" altenum="Window" />
    <field type="INT16" name="root_x" />
    <field type="INT16" name="root_y" />
    <field type="INT16" name="event_x" />
    <field type="INT16" name="event_y" />
    <field type="CARD16" name="state" mask="KeyButMask" />
    <field type="BOOL" name="same_screen" />
    <pad bytes="1" />
    <doc>
      <brief>a key was pressed/released</brief>
      <field name="detail"><![CDATA[
The keycode (a number representing a physical key on the keyboard) of the key
which was pressed.
      ]]></field>
      <field name="time"><![CDATA[
Time when the event was generated (in milliseconds).
      ]]></field>
      <field name="root"><![CDATA[
The root window of `child`.
      ]]></field>
      <field name="same_screen"><![CDATA[
Whether the `event` window is on the same screen as the `root` window.
      ]]></field>
      <field name="event_x"><![CDATA[
If `same_screen` is true, this is the X coordinate relative to the `event`
window's origin. Otherwise, `event_x` will be set to zero.
      ]]></field>
      <field name="event_y"><![CDATA[
If `same_screen` is true, this is the Y coordinate relative to the `event`
window's origin. Otherwise, `event_y` will be set to zero.
      ]]></field>
      <field name="root_x"><![CDATA[
The X coordinate of the pointer relative to the `root` window at the time of
the event.
      ]]></field>
      <field name="root_y"><![CDATA[
The Y coordinate of the pointer relative to the `root` window at the time of
the event.
      ]]></field>
      <field name="state"><![CDATA[
The logical state of the pointer buttons and modifier keys just prior to the
event.
      ]]></field>
      <see type="request" name="GrabKey" />
      <see type="request" name="GrabKeyboard" />
    </doc>
  </event>

  <eventcopy name="KeyRelease" number="3" ref="KeyPress" />

  <enum name="ButtonMask">
    <item name="1">      <bit>8</bit></item>
    <item name="2">      <bit>9</bit></item>
    <item name="3">      <bit>10</bit></item>
    <item name="4">      <bit>11</bit></item>
    <item name="5">      <bit>12</bit></item>
    <item name="Any">    <bit>15</bit></item>
  </enum>

  <event name="ButtonPress" number="4">
    <field type="BUTTON" name="detail" />
    <field type="TIMESTAMP" name="time" />
    <field type="WINDOW" name="root" />
    <field type="WINDOW" name="event" />
    <field type="WINDOW" name="child" altenum="Window" />
    <field type="INT16" name="root_x" />
    <field type="INT16" name="root_y" />
    <field type="INT16" name="event_x" />
    <field type="INT16" name="event_y" />
    <field type="CARD16" name="state" mask="KeyButMask" />
    <field type="BOOL" name="same_screen" />
    <pad bytes="1" />
    <doc>
      <brief>a mouse button was pressed/released</brief>
      <field name="detail"><![CDATA[
The keycode (a number representing a physical key on the keyboard) of the key
which was pressed.
      ]]></field>
      <field name="time"><![CDATA[
Time when the event was generated (in milliseconds).
      ]]></field>
      <field name="root"><![CDATA[
The root window of `child`.
      ]]></field>
      <field name="same_screen"><![CDATA[
Whether the `event` window is on the same screen as the `root` window.
      ]]></field>
      <field name="event_x"><![CDATA[
If `same_screen` is true, this is the X coordinate relative to the `event`
window's origin. Otherwise, `event_x` will be set to zero.
      ]]></field>
      <field name="event_y"><![CDATA[
If `same_screen` is true, this is the Y coordinate relative to the `event`
window's origin. Otherwise, `event_y` will be set to zero.
      ]]></field>
      <field name="root_x"><![CDATA[
The X coordinate of the pointer relative to the `root` window at the time of
the event.
      ]]></field>
      <field name="root_y"><![CDATA[
The Y coordinate of the pointer relative to the `root` window at the time of
the event.
      ]]></field>
      <field name="state"><![CDATA[
The logical state of the pointer buttons and modifier keys just prior to the
event.
      ]]></field>
      <see type="request" name="GrabButton" />
      <see type="request" name="GrabPointer" />
    </doc>
  </event>

  <eventcopy name="ButtonRelease" number="5" ref="ButtonPress" />

  <!-- MotionNotify detail -->
  <enum name="Motion">
    <item name="Normal"><value>0</value></item>
    <item name="Hint">  <value>1</value></item>
  </enum>

  <event name="MotionNotify" number="6">
    <field type="BYTE" name="detail" enum="Motion" />
    <field type="TIMESTAMP" name="time" />
    <field type="WINDOW" name="root" />
    <field type="WINDOW" name="event" />
    <field type="WINDOW" name="child" altenum="Window" />
    <field type="INT16" name="root_x" />
    <field type="INT16" name="root_y" />
    <field type="INT16" name="event_x" />
    <field type="INT16" name="event_y" />
    <field type="CARD16" name="state" mask="KeyButMask" />
    <field type="BOOL" name="same_screen" />
    <pad bytes="1" />
    <doc>
      <brief>a key was pressed</brief>
      <field name="detail"><![CDATA[
The keycode (a number representing a physical key on the keyboard) of the key
which was pressed.
      ]]></field>
      <field name="time"><![CDATA[
Time when the event was generated (in milliseconds).
      ]]></field>
      <field name="root"><![CDATA[
The root window of `child`.
      ]]></field>
      <field name="same_screen"><![CDATA[
Whether the `event` window is on the same screen as the `root` window.
      ]]></field>
      <field name="event_x"><![CDATA[
If `same_screen` is true, this is the X coordinate relative to the `event`
window's origin. Otherwise, `event_x` will be set to zero.
      ]]></field>
      <field name="event_y"><![CDATA[
If `same_screen` is true, this is the Y coordinate relative to the `event`
window's origin. Otherwise, `event_y` will be set to zero.
      ]]></field>
      <field name="root_x"><![CDATA[
The X coordinate of the pointer relative to the `root` window at the time of
the event.
      ]]></field>
      <field name="root_y"><![CDATA[
The Y coordinate of the pointer relative to the `root` window at the time of
the event.
      ]]></field>
      <field name="state"><![CDATA[
The logical state of the pointer buttons and modifier keys just prior to the
event.
      ]]></field>
      <see type="request" name="GrabKey" />
      <see type="request" name="GrabKeyboard" />
    </doc>
  </event>

  <enum name="NotifyDetail">
    <item name="Ancestor">        <value>0</value></item>
    <item name="Virtual">         <value>1</value></item>
    <item name="Inferior">        <value>2</value></item>
    <item name="Nonlinear">       <value>3</value></item>
    <item name="NonlinearVirtual"><value>4</value></item>
    <item name="Pointer">         <value>5</value></item>
    <item name="PointerRoot">     <value>6</value></item>
    <item name="None">            <value>7</value></item>
  </enum>

  <enum name="NotifyMode">
    <item name="Normal">      <value>0</value></item>
    <item name="Grab">        <value>1</value></item>
    <item name="Ungrab">      <value>2</value></item>
    <item name="WhileGrabbed"><value>3</value></item>
  </enum>

  <event name="EnterNotify" number="7">
    <field type="BYTE" name="detail" enum="NotifyDetail" />
    <field type="TIMESTAMP" name="time" />
    <field type="WINDOW" name="root" />
    <field type="WINDOW" name="event" />
    <field type="WINDOW" name="child" altenum="Window" />
    <field type="INT16" name="root_x" />
    <field type="INT16" name="root_y" />
    <field type="INT16" name="event_x" />
    <field type="INT16" name="event_y" />
    <field type="CARD16" name="state" mask="KeyButMask" />
    <field type="BYTE" name="mode" enum="NotifyMode" />
    <field type="BYTE" name="same_screen_focus" />
    <doc>
      <brief>the pointer is in a different window</brief>
      <field name="event"><![CDATA[
The reconfigured window or its parent, depending on whether `StructureNotify`
or `SubstructureNotify` was selected.
      ]]></field>
      <field name="window"><![CDATA[
The window that was unmapped.
      ]]></field>
      <field name="root"><![CDATA[
The root window for the final cursor position.
      ]]></field>
      <field name="root_x"><![CDATA[
The pointer X coordinate relative to `root`'s origin at the time of the event.
      ]]></field>
      <field name="root_y"><![CDATA[
The pointer Y coordinate relative to `root`'s origin at the time of the event.
      ]]></field>
      <field name="event_x"><![CDATA[
If `event` is on the same screen as `root`, this is the pointer X coordinate
relative to the event window's origin.
      ]]></field>
      <field name="event_y"><![CDATA[
If `event` is on the same screen as `root`, this is the pointer Y coordinate
relative to the event window's origin.
      ]]></field>
      <field name="mode" />
    </doc>
  </event>

  <eventcopy name="LeaveNotify" number="8" ref="EnterNotify" />

  <event name="FocusIn" number="9">
    <field type="BYTE" name="detail" enum="NotifyDetail" />
    <field type="WINDOW" name="event" />
    <field type="BYTE" name="mode" enum="NotifyMode" />
    <pad bytes="3" />
    <doc>
      <brief>NOT YET DOCUMENTED</brief>
      <field name="event"><![CDATA[
The window on which the focus event was generated. This is the window used by
the X server to report the event.
      ]]></field>
      <!-- enum documentation is sufficient -->
      <field name="detail" />
      <field name="mode" />
    </doc>
  </event>

  <eventcopy name="FocusOut" number="10" ref="FocusIn" />

  <event name="KeymapNotify" number="11" no-sequence-number="true">
    <list type="CARD8" name="keys"><value>31</value></list>
  </event>

  <event name="Expose" number="12">
    <pad bytes="1" />
    <field type="WINDOW" name="window" />
    <field type="CARD16" name="x" />
    <field type="CARD16" name="y" />
    <field type="CARD16" name="width" />
    <field type="CARD16" name="height" />
    <field type="CARD16" name="count" />
    <pad bytes="2" />
    <doc>
      <brief>NOT YET DOCUMENTED</brief>
      <field name="window"><![CDATA[
The exposed (damaged) window.
      ]]></field>
      <field name="x"><![CDATA[
The X coordinate of the left-upper corner of the exposed rectangle, relative to
the `window`'s origin.
      ]]></field>
      <field name="y"><![CDATA[
The Y coordinate of the left-upper corner of the exposed rectangle, relative to
the `window`'s origin.
      ]]></field>
      <field name="width"><![CDATA[
The width of the exposed rectangle.
      ]]></field>
      <field name="height"><![CDATA[
The height of the exposed rectangle.
      ]]></field>
      <field name="count"><![CDATA[
The amount of `Expose` events following this one. Simple applications that do
not want to optimize redisplay by distinguishing between subareas of its window
can just ignore all Expose events with nonzero counts and perform full
redisplays on events with zero counts.
      ]]></field>
    </doc>
  </event>

  <event name="GraphicsExposure" number="13">
    <pad bytes="1" />
    <field type="DRAWABLE" name="drawable" />
    <field type="CARD16" name="x" />
    <field type="CARD16" name="y" />
    <field type="CARD16" name="width" />
    <field type="CARD16" name="height" />
    <field type="CARD16" name="minor_opcode" />
    <field type="CARD16" name="count" />
    <field type="CARD8" name="major_opcode" />
    <pad bytes="3" />
  </event>

  <event name="NoExposure" number="14">
    <pad bytes="1" />
    <field type="DRAWABLE" name="drawable" />
    <field type="CARD16" name="minor_opcode" />
    <field type="CARD8" name="major_opcode" />
    <pad bytes="1" />
  </event>

  <enum name="Visibility">
    <item name="Unobscured">       <value>0</value></item>
    <item name="PartiallyObscured"><value>1</value></item>
    <item name="FullyObscured">    <value>2</value></item>
  </enum>

  <event name="VisibilityNotify" number="15">
    <pad bytes="1" />
    <field type="WINDOW" name="window" />
    <field type="BYTE" name="state" enum="Visibility" />
    <pad bytes="3" />
  </event>

  <event name="CreateNotify" number="16">
    <pad bytes="1" />
    <field type="WINDOW" name="parent" />
    <field type="WINDOW" name="window" />
    <field type="INT16" name="x" />
    <field type="INT16" name="y" />
    <field type="CARD16" name="width" />
    <field type="CARD16" name="height" />
    <field type="CARD16" name="border_width" />
    <field type="BOOL" name="override_redirect" />
    <pad bytes="1" />
  </event>

  <event name="DestroyNotify" number="17">
    <pad bytes="1" />
    <field type="WINDOW" name="event" />
    <field type="WINDOW" name="window" />
    <doc>
      <brief>a window is destroyed</brief>
      <field name="event"><![CDATA[
The reconfigured window or its parent, depending on whether `StructureNotify`
or `SubstructureNotify` was selected.
      ]]></field>
      <field name="window"><![CDATA[
The window that is destroyed.
      ]]></field>
      <see type="request" name="DestroyWindow" />
    </doc>
  </event>

  <event name="UnmapNotify" number="18">
    <pad bytes="1" />
    <field type="WINDOW" name="event" />
    <field type="WINDOW" name="window" />
    <field type="BOOL" name="from_configure" />
    <pad bytes="3" />
    <doc>
      <brief>a window is unmapped</brief>
      <field name="event"><![CDATA[
The reconfigured window or its parent, depending on whether `StructureNotify`
or `SubstructureNotify` was selected.
      ]]></field>
      <field name="window"><![CDATA[
The window that was unmapped.
      ]]></field>
      <field name="from_configure"><![CDATA[
Set to 1 if the event was generated as a result of a resizing of the window's
parent when `window` had a win_gravity of `UnmapGravity`.
      ]]></field>
      <see type="request" name="UnmapWindow" />
    </doc>
  </event>

  <event name="MapNotify" number="19">
    <pad bytes="1" />
    <field type="WINDOW" name="event" />
    <field type="WINDOW" name="window" />
    <field type="BOOL" name="override_redirect" />
    <pad bytes="3" />
    <doc>
      <brief>a window was mapped</brief>
      <field name="event"><![CDATA[
The window which was mapped or its parent, depending on whether
`StructureNotify` or `SubstructureNotify` was selected.
      ]]></field>
      <field name="window"><![CDATA[
The window that was mapped.
      ]]></field>
      <field name="override_redirect"><![CDATA[
Window managers should ignore this window if `override_redirect` is 1.
      ]]></field>
      <see type="request" name="MapWindow" />
    </doc>
  </event>

  <event name="MapRequest" number="20">
    <pad bytes="1" />
    <field type="WINDOW" name="parent" />
    <field type="WINDOW" name="window" />
    <doc>
      <brief>window wants to be mapped</brief>
      <field name="parent"><![CDATA[
The parent of `window`.
      ]]></field>
      <field name="window"><![CDATA[
The window to be mapped.
      ]]></field>
      <see type="request" name="MapWindow" />
    </doc>
  </event>

  <event name="ReparentNotify" number="21">
    <pad bytes="1" />
    <field type="WINDOW" name="event" />
    <field type="WINDOW" name="window" />
    <field type="WINDOW" name="parent" />
    <field type="INT16" name="x" />
    <field type="INT16" name="y" />
    <field type="BOOL" name="override_redirect" />
    <pad bytes="3" />
  </event>

  <event name="ConfigureNotify" number="22">
    <pad bytes="1" />
    <field type="WINDOW" name="event" />
    <field type="WINDOW" name="window" />
    <field type="WINDOW" name="above_sibling" altenum="Window" />
    <field type="INT16" name="x" />
    <field type="INT16" name="y" />
    <field type="CARD16" name="width" />
    <field type="CARD16" name="height" />
    <field type="CARD16" name="border_width" />
    <field type="BOOL" name="override_redirect" />
    <pad bytes="1" />
    <doc>
      <brief>NOT YET DOCUMENTED</brief>
      <field name="event"><![CDATA[
The reconfigured window or its parent, depending on whether `StructureNotify`
or `SubstructureNotify` was selected.
      ]]></field>
      <field name="window"><![CDATA[
The window whose size, position, border, and/or stacking order was changed.
      ]]></field>
      <field name="above_sibling"><![CDATA[
If `XCB_NONE`, the `window` is on the bottom of the stack with respect to
sibling windows. However, if set to a sibling window, the `window` is placed on
top of this sibling window.
      ]]></field>
      <field name="x"><![CDATA[
The X coordinate of the upper-left outside corner of `window`, relative to the
parent window's origin.
      ]]></field>
      <field name="y"><![CDATA[
The Y coordinate of the upper-left outside corner of `window`, relative to the
parent window's origin.
      ]]></field>
      <field name="width"><![CDATA[
The inside width of `window`, not including the border.
      ]]></field>
      <field name="height"><![CDATA[
The inside height of `window`, not including the border.
      ]]></field>
      <field name="border_width"><![CDATA[
The border width of `window`.
      ]]></field>
      <field name="override_redirect"><![CDATA[
Window managers should ignore this window if `override_redirect` is 1.
      ]]></field>
      <see type="request" name="FreeColormap" />
    </doc>
  </event>

  <event name="ConfigureRequest" number="23">
    <field type="BYTE" name="stack_mode" enum="StackMode" />
    <field type="WINDOW" name="parent" />
    <field type="WINDOW" name="window" />
    <field type="WINDOW" name="sibling" altenum="Window" />
    <field type="INT16" name="x" />
    <field type="INT16" name="y" />
    <field type="CARD16" name="width" />
    <field type="CARD16" name="height" />
    <field type="CARD16" name="border_width" />
    <field type="CARD16" name="value_mask" mask="ConfigWindow" />
  </event>

  <event name="GravityNotify" number="24">
    <pad bytes="1" />
    <field type="WINDOW" name="event" />
    <field type="WINDOW" name="window" />
    <field type="INT16" name="x" />
    <field type="INT16" name="y" />
  </event>

  <event name="ResizeRequest" number="25">
    <pad bytes="1" />
    <field type="WINDOW" name="window" />
    <field type="CARD16" name="width" />
    <field type="CARD16" name="height" />
  </event>

  <enum name="Place">
    <item name="OnTop">   <value>0</value></item>
    <item name="OnBottom"><value>1</value></item>
    <doc>
      <field name="OnTop"><![CDATA[
The window is now on top of all siblings.
      ]]></field>
      <field name="OnBottom"><![CDATA[
The window is now below all siblings.
      ]]></field>
    </doc>
  </enum>

  <event name="CirculateNotify" number="26">
    <pad bytes="1" />
    <field type="WINDOW" name="event" />
    <field type="WINDOW" name="window" />
    <pad bytes="4" />
    <field type="BYTE" name="place" enum="Place" />
    <pad bytes="3" />
    <doc>
      <brief>NOT YET DOCUMENTED</brief>
      <field name="event"><![CDATA[
Either the restacked window or its parent, depending on whether
`StructureNotify` or `SubstructureNotify` was selected.
      ]]></field>
      <field name="window"><![CDATA[
The restacked window.
      ]]></field>
      <!-- the enum doc is sufficient -->
      <field name="place" />
      <see type="request" name="CirculateWindow" />
    </doc>
  </event>

  <eventcopy name="CirculateRequest" number="27" ref="CirculateNotify" />

  <enum name="Property">
    <item name="NewValue"><value>0</value></item>
    <item name="Delete">  <value>1</value></item>
  </enum>

  <event name="PropertyNotify" number="28">
    <pad bytes="1" />
    <field type="WINDOW" name="window" />
    <field type="ATOM" name="atom" />
    <field type="TIMESTAMP" name="time" />
    <field type="BYTE" name="state" enum="Property" />
    <pad bytes="3" />
    <doc>
      <brief>a window property changed</brief>
      <field name="window"><![CDATA[
The window whose associated property was changed.
      ]]></field>
      <field name="atom"><![CDATA[
The property's atom, to indicate which property was changed.
      ]]></field>
      <field name="time"><![CDATA[
A timestamp of the server time when the property was changed.
      ]]></field>
      <!-- enum documentation is sufficient -->
      <field name="state" />
      <see type="request" name="ChangeProperty" />
    </doc>
  </event>

  <event name="SelectionClear" number="29">
    <pad bytes="1" />
    <field type="TIMESTAMP" name="time" />
    <field type="WINDOW" name="owner" />
    <field type="ATOM" name="selection" />
  </event>

  <enum name="Time">
    <item name="CurrentTime"> <value>0</value> </item>
  </enum>

  <enum name="Atom">
    <item name="None"> <value>0</value></item>
    <item name="Any">  <value>0</value></item>
    <item name="PRIMARY" />
    <item name="SECONDARY" />
    <item name="ARC" />
    <item name="ATOM" />
    <item name="BITMAP" />
    <item name="CARDINAL" />
    <item name="COLORMAP" />
    <item name="CURSOR" />
    <item name="CUT_BUFFER0" />
    <item name="CUT_BUFFER1" />
    <item name="CUT_BUFFER2" />
    <item name="CUT_BUFFER3" />
    <item name="CUT_BUFFER4" />
    <item name="CUT_BUFFER5" />
    <item name="CUT_BUFFER6" />
    <item name="CUT_BUFFER7" />
    <item name="DRAWABLE" />
    <item name="FONT" />
    <item name="INTEGER" />
    <item name="PIXMAP" />
    <item name="POINT" />
    <item name="RECTANGLE" />
    <item name="RESOURCE_MANAGER" />
    <item name="RGB_COLOR_MAP" />
    <item name="RGB_BEST_MAP" />
    <item name="RGB_BLUE_MAP" />
    <item name="RGB_DEFAULT_MAP" />
    <item name="RGB_GRAY_MAP" />
    <item name="RGB_GREEN_MAP" />
    <item name="RGB_RED_MAP" />
    <item name="STRING" />
    <item name="VISUALID" />
    <item name="WINDOW" />
    <item name="WM_COMMAND" />
    <item name="WM_HINTS" />
    <item name="WM_CLIENT_MACHINE" />
    <item name="WM_ICON_NAME" />
    <item name="WM_ICON_SIZE" />
    <item name="WM_NAME" />
    <item name="WM_NORMAL_HINTS" />
    <item name="WM_SIZE_HINTS" />
    <item name="WM_ZOOM_HINTS" />
    <item name="MIN_SPACE" />
    <item name="NORM_SPACE" />
    <item name="MAX_SPACE" />
    <item name="END_SPACE" />
    <item name="SUPERSCRIPT_X" />
    <item name="SUPERSCRIPT_Y" />
    <item name="SUBSCRIPT_X" />
    <item name="SUBSCRIPT_Y" />
    <item name="UNDERLINE_POSITION" />
    <item name="UNDERLINE_THICKNESS" />
    <item name="STRIKEOUT_ASCENT" />
    <item name="STRIKEOUT_DESCENT" />
    <item name="ITALIC_ANGLE" />
    <item name="X_HEIGHT" />
    <item name="QUAD_WIDTH" />
    <item name="WEIGHT" />
    <item name="POINT_SIZE" />
    <item name="RESOLUTION" />
    <item name="COPYRIGHT" />
    <item name="NOTICE" />
    <item name="FONT_NAME" />
    <item name="FAMILY_NAME" />
    <item name="FULL_NAME" />
    <item name="CAP_HEIGHT" />
    <item name="WM_CLASS" />
    <item name="WM_TRANSIENT_FOR" />
  </enum>

  <event name="SelectionRequest" number="30">
    <pad bytes="1" />
    <field type="TIMESTAMP" name="time" altenum="Time" />
    <field type="WINDOW" name="owner" />
    <field type="WINDOW" name="requestor" />
    <field type="ATOM" name="selection" />
    <field type="ATOM" name="target" />
    <field type="ATOM" name="property" altenum="Atom" />
  </event>

  <event name="SelectionNotify" number="31">
    <pad bytes="1" />
    <field type="TIMESTAMP" name="time" altenum="Time" />
    <field type="WINDOW" name="requestor" />
    <field type="ATOM" name="selection" />
    <field type="ATOM" name="target" />
    <field type="ATOM" name="property" altenum="Atom" />
  </event>

  <enum name="ColormapState">
    <item name="Uninstalled"><value>0</value></item>
    <item name="Installed">  <value>1</value></item>
    <doc>
      <field name="Uninstalled"><![CDATA[
The colormap was uninstalled.
      ]]></field>
      <field name="Installed"><![CDATA[
The colormap was installed.
      ]]></field>
    </doc>
  </enum>

  <enum name="Colormap">
    <item name="None"> <value>0</value></item>
  </enum>

  <event name="ColormapNotify" number="32">
    <pad bytes="1" />
    <field type="WINDOW" name="window" />
    <field type="COLORMAP" name="colormap" altenum="Colormap" />
    <field type="BOOL" name="new" />
    <field type="BYTE" name="state" enum="ColormapState" />
    <pad bytes="2" />
    <doc>
      <brief>the colormap for some window changed</brief>
      <field name="window"><![CDATA[
The window whose associated colormap is changed, installed or uninstalled.
      ]]></field>
      <field name="colormap"><![CDATA[
The colormap which is changed, installed or uninstalled. This is `XCB_NONE`
when the colormap is changed by a call to `FreeColormap`.
      ]]></field>
      <field name="_new"><![CDATA[
Indicates whether the colormap was changed (1) or installed/uninstalled (0).
      ]]></field>
      <!-- enum doc is sufficient -->
      <field name="state" />
      <see type="request" name="FreeColormap" />
    </doc>
  </event>

  <union name="ClientMessageData">
    <!-- The format member of the ClientMessage event determines which array
         to use. -->
    <list type="CARD8"  name="data8" ><value>20</value></list> <!--  8 -->
    <list type="CARD16" name="data16"><value>10</value></list> <!-- 16 -->
    <list type="CARD32" name="data32"><value>5</value></list>  <!-- 32 -->
  </union>

  <event name="ClientMessage" number="33">
    <field type="CARD8" name="format" /> <!-- 8, 16, or 32. -->
    <field type="WINDOW" name="window" />
    <field type="ATOM" name="type" />
    <field type="ClientMessageData" name="data" />
    <doc>
      <brief>NOT YET DOCUMENTED</brief>
      <description><![CDATA[
This event represents a ClientMessage, sent by another X11 client. An example
is a client sending the `_NET_WM_STATE` ClientMessage to the root window
to indicate the fullscreen window state, effectively requesting that the window
manager puts it into fullscreen mode.
      ]]></description>
      <field name="format"><![CDATA[
Specifies how to interpret `data`. Can be either 8, 16 or 32.
      ]]></field>
      <field name="type"><![CDATA[
An atom which indicates how the data should be interpreted by the receiving
client.
      ]]></field>
      <field name="data"><![CDATA[
The data itself (20 bytes max).
      ]]></field>
      <see type="request" name="SendEvent" />
    </doc>
  </event>

  <enum name="Mapping">
    <item name="Modifier"><value>0</value></item>
    <item name="Keyboard"><value>1</value></item>
    <item name="Pointer"> <value>2</value></item>
  </enum>

  <event name="MappingNotify" number="34">
    <pad bytes="1" />
    <field type="BYTE" name="request" enum="Mapping" />
    <field type="KEYCODE" name="first_keycode" />
    <field type="CARD8" name="count" />
    <pad bytes="1" />
    <doc>
      <brief>keyboard mapping changed</brief>
      <!-- enum documentation is sufficient -->
      <field name="request" />
      <field name="first_keycode"><![CDATA[
The first number in the range of the altered mapping.
      ]]></field>
      <field name="count"><![CDATA[
The number of keycodes altered.
      ]]></field>
    </doc>
  </event>



  <!-- Core error types -->



  <error name="Request" number="1">
    <field type="CARD32" name="bad_value" />
    <field type="CARD16" name="minor_opcode" />
    <field type="CARD8" name="major_opcode" />
    <pad bytes="1" />
  </error>

  <error name="Value" number="2">
    <field type="CARD32" name="bad_value" />
    <field type="CARD16" name="minor_opcode" />
    <field type="CARD8" name="major_opcode" />
    <pad bytes="1" />
  </error>

  <errorcopy name="Window" number="3" ref="Value" />
  <errorcopy name="Pixmap" number="4" ref="Value" />
  <errorcopy name="Atom" number="5" ref="Value" />
  <errorcopy name="Cursor" number="6" ref="Value" />
  <errorcopy name="Font" number="7" ref="Value" />
  <errorcopy name="Match" number="8" ref="Request" />
  <errorcopy name="Drawable" number="9" ref="Value" />
  <errorcopy name="Access" number="10" ref="Request" />
  <errorcopy name="Alloc" number="11" ref="Request" />
  <errorcopy name="Colormap" number="12" ref="Value" />
  <errorcopy name="GContext" number="13" ref="Value" />
  <errorcopy name="IDChoice" number="14" ref="Value" />
  <errorcopy name="Name" number="15" ref="Request" />
  <errorcopy name="Length" number="16" ref="Request" />
  <errorcopy name="Implementation" number="17" ref="Request" />



  <!-- The core requests, in major number order. -->
  <!-- It is the caller's responsibility to free returned XCB*Rep objects. -->



  <enum name="WindowClass">
    <item name="CopyFromParent"><value>0</value></item>
    <item name="InputOutput">   <value>1</value></item>
    <item name="InputOnly">     <value>2</value></item>
  </enum>

  <!-- Window attributes for CreateWindow and ChangeWindowAttributes. -->
  <enum name="CW">
    <item name="BackPixmap">      <bit>0</bit></item>
    <item name="BackPixel">       <bit>1</bit></item>
    <item name="BorderPixmap">    <bit>2</bit></item>
    <item name="BorderPixel">     <bit>3</bit></item>
    <item name="BitGravity">      <bit>4</bit></item>
    <item name="WinGravity">      <bit>5</bit></item>
    <item name="BackingStore">    <bit>6</bit></item>
    <item name="BackingPlanes">   <bit>7</bit></item>
    <item name="BackingPixel">    <bit>8</bit></item>
    <item name="OverrideRedirect"><bit>9</bit></item>
    <item name="SaveUnder">       <bit>10</bit></item>
    <item name="EventMask">       <bit>11</bit></item>
    <item name="DontPropagate">   <bit>12</bit></item>
    <item name="Colormap">        <bit>13</bit></item>
    <item name="Cursor">          <bit>14</bit></item>
    <doc>
      <field name="BackPixmap"><![CDATA[
Overrides the default background-pixmap. The background pixmap and window must
have the same root and same depth. Any size pixmap can be used, although some
sizes may be faster than others.

If `XCB_BACK_PIXMAP_NONE` is specified, the window has no defined background.
The server may fill the contents with the previous screen contents or with
contents of its own choosing.

If `XCB_BACK_PIXMAP_PARENT_RELATIVE` is specified, the parent's background is
used, but the window must have the same depth as the parent (or a Match error
results).   The parent's background is tracked, and the current version is
used each time the window background is required.
      ]]></field>
      <field name="BackPixel"><![CDATA[
Overrides `BackPixmap`. A pixmap of undefined size filled with the specified
background pixel is used for the background. Range-checking is not performed,
the background pixel is truncated to the appropriate number of bits.
      ]]></field>
      <field name="BorderPixmap"><![CDATA[
Overrides the default border-pixmap. The border pixmap and window must have the
same root and the same depth. Any size pixmap can be used, although some sizes
may be faster than others.

The special value `XCB_COPY_FROM_PARENT` means the parent's border pixmap is
copied (subsequent changes to the parent's border attribute do not affect the
child), but the window must have the same depth as the parent.
      ]]></field>
      <field name="BorderPixel"><![CDATA[
Overrides `BorderPixmap`. A pixmap of undefined size filled with the specified
border pixel is used for the border. Range checking is not performed on the
border-pixel value, it is truncated to the appropriate number of bits.
      ]]></field>
      <field name="BitGravity"><![CDATA[
Defines which region of the window should be retained if the window is resized.
      ]]></field>
      <field name="WinGravity"><![CDATA[
Defines how the window should be repositioned if the parent is resized (see
`ConfigureWindow`).
      ]]></field>
      <field name="BackingStore"><![CDATA[
A backing-store of `WhenMapped` advises the server that maintaining contents of
obscured regions when the window is mapped would be beneficial. A backing-store
of `Always` advises the server that maintaining contents even when the window
is unmapped would be beneficial. In this case, the server may generate an
exposure event when the window is created. A value of `NotUseful` advises the
server that maintaining contents is unnecessary, although a server may still
choose to maintain contents while the window is mapped. Note that if the server
maintains contents, then the server should maintain complete contents not just
the region within the parent boundaries, even if the window is larger than its
parent. While the server maintains contents, exposure events will not normally
be generated, but the server may stop maintaining contents at any time.
      ]]></field>
      <field name="BackingPlanes"><![CDATA[
The backing-planes indicates (with bits set to 1) which bit planes of the
window hold dynamic data that must be preserved in backing-stores and during
save-unders.
      ]]></field>
      <field name="BackingPixel"><![CDATA[
The backing-pixel specifies what value to use in planes not covered by
backing-planes. The server is free to save only the specified bit planes in the
backing-store or save-under and regenerate the remaining planes with the
specified pixel value. Any bits beyond the specified depth of the window in
these values are simply ignored.
      ]]></field>
      <field name="OverrideRedirect"><![CDATA[
The override-redirect specifies whether map and configure requests on this
window should override a SubstructureRedirect on the parent, typically to
inform a window manager not to tamper with the window.
      ]]></field>
      <field name="SaveUnder"><![CDATA[
If 1, the server is advised that when this window is mapped, saving the
contents of windows it obscures would be beneficial.
      ]]></field>
      <field name="EventMask"><![CDATA[
The event-mask defines which events the client is interested in for this window
(or for some event types, inferiors of the window).
      ]]></field>
      <field name="DontPropagate"><![CDATA[
The do-not-propagate-mask defines which events should not be propagated to
ancestor windows when no client has the event type selected in this window.
      ]]></field>
      <field name="Colormap"><![CDATA[
The colormap specifies the colormap that best reflects the true colors of the window. Servers
capable of supporting multiple hardware colormaps may use this information, and window man-
agers may use it for InstallColormap requests. The colormap must have the same visual type
and root as the window (or a Match error results). If CopyFromParent is specified, the parent's
colormap is copied (subsequent changes to the parent's colormap attribute do not affect the child).
However, the window must have the same visual type as the parent (or a Match error results),
and the parent must not have a colormap of None (or a Match error results). For an explanation
of None, see FreeColormap request. The colormap is copied by sharing the colormap object
between the child and the parent, not by making a complete copy of the colormap contents.
      ]]></field>
      <field name="Cursor"><![CDATA[
If a cursor is specified, it will be used whenever the pointer is in the window. If None is speci-
fied, the parent's cursor will be used when the pointer is in the window, and any change in the
parent's cursor will cause an immediate change in the displayed cursor.
      ]]></field>
    </doc>
  </enum>
  
  <enum name="BackPixmap">
    <item name="None">          <value>0</value></item>
    <item name="ParentRelative"><value>1</value></item>
  </enum>

  <enum name="Gravity">
    <item name="BitForget"><value>0</value></item>
    <item name="WinUnmap"> <value>0</value></item>
    <item name="NorthWest"><value>1</value></item>
    <item name="North">    <value>2</value></item>
    <item name="NorthEast"><value>3</value></item>
    <item name="West">     <value>4</value></item>
    <item name="Center">   <value>5</value></item>
    <item name="East">     <value>6</value></item>
    <item name="SouthWest"><value>7</value></item>
    <item name="South">    <value>8</value></item>
    <item name="SouthEast"><value>9</value></item>
    <item name="Static">   <value>10</value></item>
  </enum>

  <request name="CreateWindow" opcode="1">
    <field type="CARD8" name="depth" />
    <field type="WINDOW" name="wid" />
    <field type="WINDOW" name="parent" />
    <field type="INT16" name="x" />
    <field type="INT16" name="y" />
    <field type="CARD16" name="width" />
    <field type="CARD16" name="height" />
    <field type="CARD16" name="border_width" />
    <field type="CARD16" name="class" enum="WindowClass" />
    <field type="VISUALID" name="visual" />
    <valueparam value-mask-type="CARD32"
                value-mask-name="value_mask"
                value-list-name="CW" />
    <doc>
      <brief>Creates a window</brief>
      <description><![CDATA[
Creates an unmapped window as child of the specified `parent` window. A
CreateNotify event will be generated. The new window is placed on top in the
stacking order with respect to siblings.

The coordinate system has the X axis horizontal and the Y axis vertical with
the origin [0, 0] at the upper-left corner. Coordinates are integral, in terms
of pixels, and coincide with pixel centers. Each window and pixmap has its own
coordinate system. For a window, the origin is inside the border at the inside,
upper-left corner.

The created window is not yet displayed (mapped), call `xcb_map_window` to
display it.

The created window will initially use the same cursor as its parent.
      ]]></description>
      <field name="wid"><![CDATA[
The ID with which you will refer to the new window, created by
`xcb_generate_id`.
      ]]></field>
      <field name="depth"><![CDATA[
Specifies the new window's depth (TODO: what unit?).

The special value `XCB_COPY_FROM_PARENT` means the depth is taken from the
`parent` window.
      ]]></field>
      <field name="visual"><![CDATA[
Specifies the id for the new window's visual.

The special value `XCB_COPY_FROM_PARENT` means the visual is taken from the
`parent` window.
      ]]></field>
      <field name="class"></field>
      <field name="parent"><![CDATA[
The parent window of the new window.
      ]]></field>
      <field name="border_width"><![CDATA[
      TODO:

Must be zero if the `class` is `InputOnly` or a `xcb_match_error_t` occurs.
      ]]></field>
      <field name="x"><![CDATA[The X coordinate of the new window.]]></field>
      <field name="y"><![CDATA[The Y coordinate of the new window.]]></field>
      <field name="width"><![CDATA[The width of the new window.]]></field>
      <field name="height"><![CDATA[The height of the new window.]]></field>
      <error type="Colormap"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Match"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Cursor"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Pixmap"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Value"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Window"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Alloc"><![CDATA[
The X server could not allocate the requested resources (no memory?).
      ]]></error>
      <see type="function" name="xcb_generate_id" />
      <see type="request" name="MapWindow" />
      <see type="event" name="CreateNotify" />
    </doc>

  </request>

  <request name="ChangeWindowAttributes" opcode="2">
    <pad bytes="1" />
    <field type="WINDOW" name="window" />
    <valueparam value-mask-type="CARD32"
                value-mask-name="value_mask"
                value-list-name="CW" />
    <doc>
      <brief>change window attributes</brief>
      <description><![CDATA[
Changes the attributes specified by `value_mask` for the specified `window`.
      ]]></description>
      <field name="window"><![CDATA[
The window to change.
      ]]></field>
      <!-- the enum documentation is good enough. -->
      <field name="value_mask" />
      <field name="value_list"><![CDATA[
Values for each of the attributes specified in the bitmask `value_mask`. The
order has to correspond to the order of possible `value_mask` bits. See the
example.
      ]]></field>
      <error type="Access"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Colormap"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Cursor"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Match"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Pixmap"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Value"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Window"><![CDATA[
The specified `window` does not exist.
      ]]></error>
    </doc>
  </request>
  
  <enum name="MapState">
    <item name="Unmapped">  <value>0</value></item>
    <item name="Unviewable"><value>1</value></item>
    <item name="Viewable">  <value>2</value></item>
  </enum>

  <request name="GetWindowAttributes" opcode="3">
    <pad bytes="1" />
    <field type="WINDOW" name="window" />
    <reply>
      <field type="CARD8" name="backing_store" enum="BackingStore" />
      <field type="VISUALID" name="visual" />
      <field type="CARD16" name="class" enum="WindowClass" />
      <field type="CARD8" name="bit_gravity" enum="Gravity" />
      <field type="CARD8" name="win_gravity" enum="Gravity" />
      <field type="CARD32" name="backing_planes" />
      <field type="CARD32" name="backing_pixel" />
      <field type="BOOL" name="save_under" />
      <field type="BOOL" name="map_is_installed" />
      <field type="CARD8" name="map_state" enum="MapState" />
      <field type="BOOL" name="override_redirect" />
      <field type="COLORMAP" name="colormap" altenum="Colormap" />
      <field type="CARD32" name="all_event_masks" mask="EventMask" />
      <field type="CARD32" name="your_event_mask" mask="EventMask" />
      <field type="CARD16" name="do_not_propagate_mask" mask="EventMask" />
      <pad bytes="2" />
      <doc>
        <field name="override_redirect"><![CDATA[
Window managers should ignore this window if `override_redirect` is 1.
        ]]></field>
        <field name="visual"><![CDATA[
The associated visual structure of `window`.
        ]]></field>
        <field name="backing_planes"><![CDATA[
Planes to be preserved if possible.
        ]]></field>
        <field name="backing_pixel"><![CDATA[
Value to be used when restoring planes.
        ]]></field>
        <field name="save_under"><![CDATA[
Boolean, should bits under be saved?
        ]]></field>
        <field name="colormap"><![CDATA[
Color map to be associated with window.
        ]]></field>
        <field name="all_event_masks"><![CDATA[
Set of events all people have interest in.
        ]]></field>
        <field name="your_event_mask"><![CDATA[
My event mask.
        ]]></field>
        <field name="do_not_propagate_mask"><![CDATA[
Set of events that should not propagate.
        ]]></field>
        <!-- enum documentation is sufficient for these fields -->
        <field name="backing_store" />
        <field name="class" />
        <field name="bit_gravity" />
        <field name="win_gravity" />
        <field name="map_state" />
      </doc>
    </reply>
    <doc>
      <brief>Gets window attributes</brief>
      <description><![CDATA[
Gets the current attributes for the specified `window`.
      ]]></description>
      <field name="window"><![CDATA[The window to get the attributes from.]]></field>
      <error type="Window"><![CDATA[
The specified `window` does not exist.
      ]]></error>
      <error type="Drawable"><![CDATA[
TODO: reasons?
      ]]></error>
    </doc>

  </request>

  <request name="DestroyWindow" opcode="4">
    <pad bytes="1" />
    <field type="WINDOW" name="window" />
    <doc>
      <brief>Destroys a window</brief>
      <description><![CDATA[
Destroys the specified window and all of its subwindows. A DestroyNotify event
is generated for each destroyed window (a DestroyNotify event is first generated
for any given window's inferiors). If the window was mapped, it will be
automatically unmapped before destroying.

Calling DestroyWindow on the root window will do nothing.
      ]]></description>
      <field name="window"><![CDATA[The window to destroy.]]></field>
      <error type="Window"><![CDATA[
The specified window does not exist.
      ]]></error>
      <see type="event" name="DestroyNotify" />
      <see type="request" name="MapWindow" />
      <see type="request" name="UnmapWindow" />
    </doc>
  </request>

  <request name="DestroySubwindows" opcode="5">
    <pad bytes="1" />
    <field type="WINDOW" name="window" />
  </request>
  
  <enum name="SetMode">
    <item name="Insert"><value>0</value></item>
    <item name="Delete"><value>1</value></item>
  </enum>

  <request name="ChangeSaveSet" opcode="6">
    <field type="BYTE" name="mode" enum="SetMode" />
    <field type="WINDOW" name="window" />
    <doc>
      <brief>Changes a client's save set</brief>
      <description><![CDATA[
TODO: explain what the save set is for.

This function either adds or removes the specified window to the client's (your
application's) save set.
      ]]></description>
      <field name="mode"><![CDATA[Insert to add the specified window to the save set or Delete to delete it from the save set.]]></field>
      <field name="window"><![CDATA[The window to add or delete to/from your save set.]]></field>
      <error type="Match"><![CDATA[
You created the specified window. This does not make sense, you can only add
windows created by other clients to your save set.
      ]]></error>
      <error type="Value"><![CDATA[
You specified an invalid mode.
      ]]></error>
      <error type="Window"><![CDATA[
The specified window does not exist.
      ]]></error>
      <see type="request" name="ReparentWindow" />
    </doc>
  </request>

  <request name="ReparentWindow" opcode="7">
    <pad bytes="1" />
    <field type="WINDOW" name="window" />
    <field type="WINDOW" name="parent" />
    <field type="INT16" name="x" />
    <field type="INT16" name="y" />
    <doc>
      <brief>Reparents a window</brief>
      <description><![CDATA[
Makes the specified window a child of the specified parent window. If the
window is mapped, it will automatically be unmapped before reparenting and
re-mapped after reparenting. The window is placed in the stacking order on top
with respect to sibling windows.

After reparenting, a ReparentNotify event is generated.
      ]]></description>
      <field name="window"><![CDATA[The window to reparent.]]></field>
      <field name="parent"><![CDATA[The new parent of the window.]]></field>
      <field name="x"><![CDATA[
The X position of the window within its new parent.
      ]]></field>
      <field name="y"><![CDATA[
The Y position of the window within its new parent.
      ]]></field>
      <error type="Match"><![CDATA[
The new parent window is not on the same screen as the old parent window.

The new parent window is the specified window or an inferior of the specified window.

The new parent is InputOnly and the window is not.

The specified window has a ParentRelative background and the new parent window is not the same depth as the specified window.
      ]]></error>
      <error type="Window"><![CDATA[
The specified window does not exist.
      ]]></error>
      <see type="event" name="ReparentNotify" />
      <see type="request" name="MapWindow" />
      <see type="request" name="UnmapWindow" />
    </doc>
  </request>

  <request name="MapWindow" opcode="8">
    <pad bytes="1" />
    <field type="WINDOW" name="window" />
    <doc>
      <brief>Makes a window visible</brief>
      <description><![CDATA[
Maps the specified window. This means making the window visible (as long as its
parent is visible).

This MapWindow request will be translated to a MapRequest request if a window
manager is running. The window manager then decides to either map the window or
not. Set the override-redirect window attribute to true if you want to bypass
this mechanism.

If the window manager decides to map the window (or if no window manager is
running), a MapNotify event is generated.

If the window becomes viewable and no earlier contents for it are remembered,
the X server tiles the window with its background. If the window's background
is undefined, the existing screen contents are not altered, and the X server
generates zero or more Expose events.

If the window type is InputOutput, an Expose event will be generated when the
window becomes visible. The normal response to an Expose event should be to
repaint the window.
      ]]></description>
      <field name="window"><![CDATA[
The window to make visible.
]]></field>
      <error type="Match"><![CDATA[
The specified window does not exist.
      ]]></error>
      <see type="event" name="MapNotify" />
      <see type="event" name="Expose" />
      <see type="request" name="UnmapWindow" />
    </doc>
  </request>

  <request name="MapSubwindows" opcode="9">
    <pad bytes="1" />
    <field type="WINDOW" name="window" />
  </request>

  <request name="UnmapWindow" opcode="10">
    <pad bytes="1" />
    <field type="WINDOW" name="window" />
    <doc>
      <brief>Makes a window invisible</brief>
      <description><![CDATA[
Unmaps the specified window. This means making the window invisible (and all
its child windows).

Unmapping a window leads to the `UnmapNotify` event being generated. Also,
`Expose` events are generated for formerly obscured windows.
      ]]></description>
      <field name="window"><![CDATA[
The window to make invisible.
]]></field>
      <error type="Window"><![CDATA[
The specified window does not exist.
      ]]></error>
      <see type="event" name="UnmapNotify" />
      <see type="event" name="Expose" />
      <see type="request" name="MapWindow" />
    </doc>
  </request>

  <request name="UnmapSubwindows" opcode="11">
    <pad bytes="1" />
    <field type="WINDOW" name="window" />
  </request>
  
  <enum name="ConfigWindow">
    <item name="X">          <bit>0</bit></item>
    <item name="Y">          <bit>1</bit></item>
    <item name="Width">      <bit>2</bit></item>
    <item name="Height">     <bit>3</bit></item>
    <item name="BorderWidth"><bit>4</bit></item>
    <item name="Sibling">    <bit>5</bit></item>
    <item name="StackMode">  <bit>6</bit></item>
  </enum>
  
  <enum name="StackMode">
    <item name="Above">   <value>0</value></item>
    <item name="Below">   <value>1</value></item>
    <item name="TopIf">   <value>2</value></item>
    <item name="BottomIf"><value>3</value></item>
    <item name="Opposite"><value>4</value></item>
  </enum>

  <request name="ConfigureWindow" opcode="12">
    <pad bytes="1" />
    <field type="WINDOW" name="window" />
    <field type="CARD16" name="value_mask" />
    <pad bytes="2" />
    <valueparam value-mask-type="CARD16"
                value-mask-name="value_mask"
                value-list-name="ConfigWindow" />
    <doc>
      <brief>Configures window attributes</brief>
      <description><![CDATA[
Configures a window's size, position, border width and stacking order.
      ]]></description>
      <example><![CDATA[
/*
 * Configures the given window to the left upper corner
 * with a size of 1024x768 pixels.
 *
 */
void my_example(xcb_connection *c, xcb_window_t window) {
    uint16_t mask = 0;

    mask |= XCB_CONFIG_WINDOW_X;
    mask |= XCB_CONFIG_WINDOW_Y;
    mask |= XCB_CONFIG_WINDOW_WIDTH;
    mask |= XCB_CONFIG_WINDOW_HEIGHT;

    const uint32_t values[] = {
        0,    /* x */
        0,    /* y */
        1024, /* width */
        768   /* height */
    };

    xcb_configure_window(c, window, mask, values);
    xcb_flush(c);
}
      ]]></example>
      <field name="window"><![CDATA[The window to configure.]]></field>
      <field name="value_mask"><![CDATA[Bitmask of attributes to change.]]></field>
      <field name="value_list"><![CDATA[
New values, corresponding to the attributes in value_mask. The order has to
correspond to the order of possible `value_mask` bits. See the example.
      ]]></field>
      <error type="Match"><![CDATA[
You specified a Sibling without also specifying StackMode or the window is not
actually a Sibling.
      ]]></error>
      <error type="Window"><![CDATA[
The specified window does not exist. TODO: any other reason?
      ]]></error>
      <error type="Value"><![CDATA[
TODO: reasons?
      ]]></error>
      <see type="event" name="MapNotify" />
      <see type="event" name="Expose" />
    </doc>
  </request>

  <enum name="Circulate">
    <item name="RaiseLowest"> <value>0</value></item>
    <item name="LowerHighest"><value>1</value></item>
  </enum>

  <request name="CirculateWindow" opcode="13">
    <field type="CARD8" name="direction" enum="Circulate" />
    <field type="WINDOW" name="window" />
    <doc>
      <brief>Change window stacking order</brief>
      <description><![CDATA[
If `direction` is `XCB_CIRCULATE_RAISE_LOWEST`, the lowest mapped child (if
any) will be raised to the top of the stack.

If `direction` is `XCB_CIRCULATE_LOWER_HIGHEST`, the highest mapped child will
be lowered to the bottom of the stack.
      ]]></description>
      <!-- The enums are documented, we have nothing to add. -->
      <field name="direction" />
      <field name="window"><![CDATA[
The window to raise/lower (depending on `direction`).
      ]]></field>
      <error type="Window"><![CDATA[
The specified `window` does not exist.
      ]]></error>
      <error type="Value"><![CDATA[
The specified `direction` is invalid.
      ]]></error>
    </doc>
  </request>

  <request name="GetGeometry" opcode="14">
    <pad bytes="1" />
    <field type="DRAWABLE" name="drawable" />
    <reply>
      <field type="CARD8" name="depth" />
      <field type="WINDOW" name="root" />
      <field type="INT16" name="x" />
      <field type="INT16" name="y" />
      <field type="CARD16" name="width" />
      <field type="CARD16" name="height" />
      <field type="CARD16" name="border_width" />
      <pad bytes="2" />
      <doc>
      <field name="root"><![CDATA[
Root window of the screen containing `drawable`.
      ]]></field>
      <field name="x"><![CDATA[
The X coordinate of `drawable`. If `drawable` is a window, the coordinate
specifies the upper-left outer corner relative to its parent's origin. If
`drawable` is a pixmap, the X coordinate is always 0.
      ]]></field>
      <field name="y"><![CDATA[
The Y coordinate of `drawable`. If `drawable` is a window, the coordinate
specifies the upper-left outer corner relative to its parent's origin. If
`drawable` is a pixmap, the Y coordinate is always 0.
      ]]></field>
      <field name="width"><![CDATA[
The width of `drawable`.
      ]]></field>
      <field name="height"><![CDATA[
The height of `drawable`.
      ]]></field>
      <field name="border_width"><![CDATA[
The border width (in pixels).
      ]]></field>
      <field name="depth"><![CDATA[
The depth of the drawable (bits per pixel for the object).
      ]]></field>
      </doc>
    </reply>
    <doc>
      <brief>Get current window geometry</brief>
      <description><![CDATA[
Gets the current geometry of the specified drawable (either `Window` or `Pixmap`).
      ]]></description>
      <example><![CDATA[
/*
 * Displays the x and y position of the given window.
 *
 */
void my_example(xcb_connection *c, xcb_window_t window) {
    xcb_get_geometry_cookie_t cookie;
    xcb_get_geometry_reply_t *reply;

    cookie = xcb_get_geometry(c, window);
    /* ... do other work here if possible ... */
    if ((reply = xcb_get_geometry_reply(c, cookie, NULL))) {
        printf("This window is at %d, %d\\n", reply->x, reply->y);
    }
    free(reply);
}
      ]]></example>
      <field name="drawable"><![CDATA[
The drawable (`Window` or `Pixmap`) of which the geometry will be received.
      ]]></field>
      <error type="Drawable"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Window"><![CDATA[
TODO: reasons?
      ]]></error>
      <see type="program" name="xwininfo" />
    </doc>
  </request>

  <request name="QueryTree" opcode="15">
    <pad bytes="1" />
    <field type="WINDOW" name="window" />
    <reply>
      <pad bytes="1" />
      <field type="WINDOW" name="root" />
      <field type="WINDOW" name="parent" altenum="Window" />
      <field type="CARD16" name="children_len" />
      <pad bytes="14" />
      <list type="WINDOW" name="children">
        <fieldref>children_len</fieldref>
      </list>
      <doc>
        <field name="root"><![CDATA[
The root window of `window`.
        ]]></field>
        <field name="parent"><![CDATA[
The parent window of `window`.
        ]]></field>
        <field name="children_len"><![CDATA[
The number of child windows.
        ]]></field>
      </doc>
    </reply>
    <doc>
      <brief>query the window tree</brief>
      <description><![CDATA[
Gets the root window ID, parent window ID and list of children windows for the
specified `window`. The children are listed in bottom-to-top stacking order.
      ]]></description>
      <example><![CDATA[
/*
 * Displays the root, parent and children of the specified window.
 *
 */
void my_example(xcb_connection *conn, xcb_window_t window) {
    xcb_query_tree_cookie_t cookie;
    xcb_query_tree_reply_t *reply;

    cookie = xcb_query_tree(conn, window);
    if ((reply = xcb_query_tree_reply(conn, cookie, NULL))) {
        printf("root = 0x%08x\\n", reply->root);
        printf("parent = 0x%08x\\n", reply->parent);

        xcb_window_t *children = xcb_query_tree_children(reply);
        for (int i = 0; i < xcb_query_tree_children_length(reply); i++)
            printf("child window = 0x%08x\\n", children[i]);

        free(reply);
    }
}
      ]]></example>
      <field name="window"><![CDATA[
The `window` to query.
      ]]></field>
      <see type="program" name="xwininfo" />
    </doc>
  </request>

  <request name="InternAtom" opcode="16">
    <field type="BOOL" name="only_if_exists" />
    <field type="CARD16" name="name_len" expression="args.name.length"/>
    <pad bytes="2" />
    <field type="STRING8" name="name" />
    <reply>
      <pad bytes="1" />
      <field type="ATOM" name="atom" altenum="Atom" />
    </reply>
    <doc>
      <brief>Get atom identifier by name</brief>
      <description><![CDATA[
Retrieves the identifier (xcb_atom_t TODO) for the atom with the specified
name. Atoms are used in protocols like EWMH, for example to store window titles
(`_NET_WM_NAME` atom) as property of a window.

If `only_if_exists` is 0, the atom will be created if it does not already exist.
If `only_if_exists` is 1, `XCB_ATOM_NONE` will be returned if the atom does
not yet exist.
      ]]></description>
      <example><![CDATA[
/*
 * Resolves the _NET_WM_NAME atom.
 *
 */
void my_example(xcb_connection *c) {
    xcb_intern_atom_cookie_t cookie;
    xcb_intern_atom_reply_t *reply;

    cookie = xcb_intern_atom(c, 0, strlen("_NET_WM_NAME"), "_NET_WM_NAME");
    /* ... do other work here if possible ... */
    if ((reply = xcb_intern_atom_reply(c, cookie, NULL))) {
        printf("The _NET_WM_NAME atom has ID %u\n", reply->atom);
        free(reply);
    }
}
      ]]></example>
      <field name="name_len"><![CDATA[
The length of the following `name`.
      ]]></field>
      <field name="name"><![CDATA[
The name of the atom.
      ]]></field>
      <field name="only_if_exists"><![CDATA[
Return a valid atom id only if the atom already exists.
      ]]></field>
      <error type="Alloc"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Value"><![CDATA[
A value other than 0 or 1 was specified for `only_if_exists`.
      ]]></error>
      <see type="program" name="xlsatoms" />
      <see type="request" name="GetAtomName" />
    </doc>

  </request>

  <request name="GetAtomName" opcode="17">
    <pad bytes="1" />
    <field type="ATOM" name="atom" />
    <reply>
      <pad bytes="1" />
      <field type="CARD16" name="name_len" expression="args.name.length"/>
      <pad bytes="22" />
      <field type="STRING8" name="name" />
    </reply>
  </request>
  
  <enum name="PropMode">
    <item name="Replace"><value>0</value></item>
    <item name="Prepend"><value>1</value></item>
    <item name="Append"> <value>2</value></item>
    <doc>
      <field name="Replace"><![CDATA[
Discard the previous property value and store the new data.
      ]]></field>
      <field name="Prepend"><![CDATA[
Insert the new data before the beginning of existing data. The `format` must
match existing property value. If the property is undefined, it is treated as
defined with the correct type and format with zero-length data.
      ]]></field>
      <field name="Append"><![CDATA[
Insert the new data after the beginning of existing data. The `format` must
match existing property value. If the property is undefined, it is treated as
defined with the correct type and format with zero-length data.
      ]]></field>
    </doc>
  </enum>

  <request name="ChangeProperty" opcode="18">
    <field type="CARD8" name="mode" enum="PropMode" />
    <field type="WINDOW" name="window" />
    <field type="ATOM" name="property" />
    <field type="ATOM" name="type" />
    <field type="CARD8" name="format" />
    <pad bytes="3" />
    <field type="CARD32" name="data_len" />
    <list type="void" name="data">
      <op op="/">
        <op op="*">
          <fieldref>data_len</fieldref>
          <fieldref>format</fieldref>
        </op>
        <value>8</value>
      </op>
    </list>
    <doc>
      <brief>Changes a window property</brief>
      <description><![CDATA[
Sets or updates a property on the specified `window`. Properties are for
example the window title (`WM_NAME`) or its minimum size (`WM_NORMAL_HINTS`).
Protocols such as EWMH also use properties - for example EWMH defines the
window title, encoded as UTF-8 string, in the `_NET_WM_NAME` property.
      ]]></description>
      <example><![CDATA[
/*
 * Sets the WM_NAME property of the window to "XCB Example".
 *
 */
void my_example(xcb_connection *conn, xcb_window_t window) {
    xcb_change_property(conn,
        XCB_PROP_MODE_REPLACE,
        window,
        XCB_ATOM_WM_NAME,
        XCB_ATOM_STRING,
        8,
        strlen("XCB Example"),
        "XCB Example");
    xcb_flush(conn);
}
      ]]></example>
      <field name="window"><![CDATA[
The window whose property you want to change.
      ]]></field>
      <!-- the enum doc is sufficient. -->
      <field name="mode" />
      <field name="property"><![CDATA[
The property you want to change (an atom).
      ]]></field>
      <field name="type"><![CDATA[
The type of the property you want to change (an atom).
      ]]></field>
      <field name="format"><![CDATA[
Specifies whether the data should be viewed as a list of 8-bit, 16-bit or
32-bit quantities. Possible values are 8, 16 and 32. This information allows
the X server to correctly perform byte-swap operations as necessary.
      ]]></field>
      <field name="data_len"><![CDATA[
Specifies the number of elements (see `format`).
      ]]></field>
      <field name="data"><![CDATA[
The property data.
      ]]></field>
      <error type="Match"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Value"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Window"><![CDATA[
The specified `window` does not exist.
      ]]></error>
      <error type="Atom"><![CDATA[
`property` or `type` do not refer to a valid atom.
      ]]></error>
      <error type="Alloc"><![CDATA[
The X server could not store the property (no memory?).
      ]]></error>
      <see type="request" name="InternAtom" />
      <see type="program" name="xprop" />
    </doc>
  </request>

  <request name="DeleteProperty" opcode="19">
    <pad bytes="1" />
    <field type="WINDOW" name="window" />
    <field type="ATOM" name="property" />
  </request>
  
  <enum name="GetPropertyType">
    <item name="Any"><value>0</value></item>
  </enum>

  <request name="GetProperty" opcode="20">
    <field type="BOOL" name="delete" />
    <field type="WINDOW" name="window" />
    <field type="ATOM" name="property" />
    <field type="ATOM" name="type" altenum="GetPropertyType" />
    <field type="CARD32" name="long_offset" />
    <field type="CARD32" name="long_length" />
    <reply>
      <field type="CARD8" name="format" />
      <field type="ATOM" name="type" />
      <field type="CARD32" name="bytes_after" />
      <field type="CARD32" name="value_len" />
      <pad bytes="12" />
      <list type="void" name="value">
        <op op="*">
          <fieldref>value_len</fieldref>
          <op op="/">
            <fieldref>format</fieldref>
            <value>8</value>
          </op>
        </op>
      </list>
      <doc>
      <field name="format"><![CDATA[
Specifies whether the data should be viewed as a list of 8-bit, 16-bit, or
32-bit quantities. Possible values are 8, 16, and 32. This information allows
the X server to correctly perform byte-swap operations as necessary.
      ]]></field>
      <field name="type"><![CDATA[
The actual type of the property (an atom).
      ]]></field>
      <field name="bytes_after"><![CDATA[
The number of bytes remaining to be read in the property if a partial read was
performed.
      ]]></field>
      <field name="value_len"><![CDATA[
The length of value. You should use the corresponding accessor instead of this
field.
      ]]></field>
      </doc>
    </reply>
    <doc>
      <brief>Gets a window property</brief>
      <description><![CDATA[
Gets the specified `property` from the specified `window`. Properties are for
example the window title (`WM_NAME`) or its minimum size (`WM_NORMAL_HINTS`).
Protocols such as EWMH also use properties - for example EWMH defines the
window title, encoded as UTF-8 string, in the `_NET_WM_NAME` property.

TODO: talk about `type`

TODO: talk about `delete`

TODO: talk about the offset/length thing. what's a valid use case?
      ]]></description>
      <example><![CDATA[
/*
 * Prints the WM_NAME property of the window.
 *
 */
void my_example(xcb_connection *c, xcb_window_t window) {
    xcb_get_property_cookie_t cookie;
    xcb_get_property_reply_t *reply;

    /* These atoms are predefined in the X11 protocol. */
    xcb_atom_t property = XCB_ATOM_WM_NAME;
    xcb_atom_t type = XCB_ATOM_STRING;

    // TODO: a reasonable long_length for WM_NAME?
    cookie = xcb_get_property(c, 0, window, property, type, 0, 0);
    if ((reply = xcb_get_property_reply(c, cookie, NULL))) {
        int len = xcb_get_property_value_length(reply);
        if (len == 0) {
            printf("TODO\\n");
            free(reply);
            return;
        }
        printf("WM_NAME is %.*s\\n", len,
               (char*)xcb_get_property_value(reply));
    }
    free(reply);
}
      ]]></example>
      <field name="window"><![CDATA[
The window whose property you want to get.
      ]]></field>
      <field name="delete"><![CDATA[
Whether the property should actually be deleted. For deleting a property, the
specified `type` has to match the actual property type.
      ]]></field>
      <field name="property"><![CDATA[
The property you want to get (an atom).
      ]]></field>
      <field name="type"><![CDATA[
The type of the property you want to get (an atom).
      ]]></field>
      <field name="long_offset"><![CDATA[
Specifies the offset (in 32-bit multiples) in the specified property where the
data is to be retrieved.
      ]]></field>
      <field name="long_length"><![CDATA[
Specifies how many 32-bit multiples of data should be retrieved (e.g. if you
set `long_length` to 4, you will receive 16 bytes of data).
      ]]></field>
      <error type="Window"><![CDATA[
The specified `window` does not exist.
      ]]></error>
      <error type="Atom"><![CDATA[
`property` or `type` do not refer to a valid atom.
      ]]></error>
      <error type="Value"><![CDATA[
The specified `long_offset` is beyond the actual property length (e.g. the
property has a length of 3 bytes and you are setting `long_offset` to 1,
resulting in a byte offset of 4).
      ]]></error>
      <see type="request" name="InternAtom" />
      <see type="program" name="xprop" />
    </doc>
  </request>

  <request name="ListProperties" opcode="21">
    <pad bytes="1" />
    <field type="WINDOW" name="window" />
    <reply>
      <pad bytes="1" />
      <field type="CARD16" name="atoms_len" />
      <pad bytes="22" />
      <list type="ATOM" name="atoms">
        <fieldref>atoms_len</fieldref>
      </list>
    </reply>
  </request>

  <request name="SetSelectionOwner" opcode="22">
    <pad bytes="1" />
    <field type="WINDOW" name="owner" altenum="Window" />
    <field type="ATOM" name="selection" />
    <field type="TIMESTAMP" name="time" altenum="Time" />
    <doc>
      <brief>Sets the owner of a selection</brief>
      <description><![CDATA[
Makes `window` the owner of the selection `selection` and updates the
last-change time of the specified selection.

TODO: briefly explain what a selection is.
      ]]></description>
      <field name="selection"><![CDATA[
The selection.
      ]]></field>
      <field name="owner"><![CDATA[
The new owner of the selection.

The special value `XCB_NONE` means that the selection will have no owner.
      ]]></field>
      <field name="time"><![CDATA[
Timestamp to avoid race conditions when running X over the network.

The selection will not be changed if `time` is earlier than the current
last-change time of the `selection` or is later than the current X server time.
Otherwise, the last-change time is set to the specified time.

The special value `XCB_CURRENT_TIME` will be replaced with the current server
time.
      ]]></field>
      <error type="Atom"><![CDATA[
`selection` does not refer to a valid atom.
      ]]></error>
      <see type="request" name="SetSelectionOwner" />
    </doc>

  </request>

  <request name="GetSelectionOwner" opcode="23">
    <pad bytes="1" />
    <field type="ATOM" name="selection" />
    <reply>
      <pad bytes="1" />
      <field type="WINDOW" name="owner" altenum="Window" />
      <doc>
        <field name="owner"><![CDATA[
The current selection owner window.
        ]]></field>
      </doc>
    </reply>
    <doc>
      <brief>Gets the owner of a selection</brief>
      <description><![CDATA[
Gets the owner of the specified selection.

TODO: briefly explain what a selection is.
      ]]></description>
      <field name="selection"><![CDATA[
The selection.
      ]]></field>
      <error type="Atom"><![CDATA[
`selection` does not refer to a valid atom.
      ]]></error>
      <see type="request" name="SetSelectionOwner" />
    </doc>
  </request>

  <request name="ConvertSelection" opcode="24">
    <pad bytes="1" />
    <field type="WINDOW" name="requestor" />
    <field type="ATOM" name="selection" />
    <field type="ATOM" name="target" />
    <field type="ATOM" name="property" altenum="Atom" />
    <field type="TIMESTAMP" name="time" altenum="Time" />
  </request>

  <enum name="SendEventDest">
    <item name="PointerWindow"><value>0</value></item>
    <item name="ItemFocus">    <value>1</value></item>
  </enum>

  <request name="SendEvent" opcode="25">
    <field type="BOOL" name="propagate" />
    <field type="WINDOW" name="destination" altenum="SendEventDest" />
    <field type="CARD32" name="event_mask" mask="EventMask" />
    <list type="char" name="event"><value>32</value></list>
    <doc>
      <brief>send an event</brief>
      <description><![CDATA[
Identifies the `destination` window, determines which clients should receive
the specified event and ignores any active grabs.

The `event` must be one of the core events or an event defined by an extension,
so that the X server can correctly byte-swap the contents as necessary. The
contents of `event` are otherwise unaltered and unchecked except for the
`send_event` field which is forced to 'true'.
      ]]></description>
      <example><![CDATA[
/*
 * Tell the given window that it was configured to a size of 800x600 pixels.
 *
 */
void my_example(xcb_connection_t *conn, xcb_window_t window) {
    /* Every X11 event is 32 bytes long. Therefore, XCB will copy 32 bytes.
     * In order to properly initialize these bytes, we allocate 32 bytes even
     * though we only need less for an xcb_configure_notify_event_t */
    xcb_configure_notify_event_t *event = calloc(32, 1);

    event->event = window;
    event->window = window;
    event->response_type = XCB_CONFIGURE_NOTIFY;

    event->x = 0;
    event->y = 0;
    event->width = 800;
    event->height = 600;

    event->border_width = 0;
    event->above_sibling = XCB_NONE;
    event->override_redirect = false;

    xcb_send_event(conn, false, window, XCB_EVENT_MASK_STRUCTURE_NOTIFY,
                   (char*)event);
    xcb_flush(conn);
    free(event);
}
      ]]></example>
      <field name="destination"><![CDATA[
The window to send this event to. Every client which selects any event within
`event_mask` on `destination` will get the event.

The special value `XCB_SEND_EVENT_DEST_POINTER_WINDOW` refers to the window
that contains the mouse pointer.

The special value `XCB_SEND_EVENT_DEST_ITEM_FOCUS` refers to the window which
has the keyboard focus.
      ]]></field>
      <field name="event_mask"><![CDATA[
Event_mask for determining which clients should receive the specified event.
See `destination` and `propagate`.
      ]]></field>
      <field name="propagate"><![CDATA[
If `propagate` is true and no clients have selected any event on `destination`,
the destination is replaced with the closest ancestor of `destination` for
which some client has selected a type in `event_mask` and for which no
intervening window has that type in its do-not-propagate-mask. If no such
window exists or if the window is an ancestor of the focus window and
`InputFocus` was originally specified as the destination, the event is not sent
to any clients. Otherwise, the event is reported to every client selecting on
the final destination any of the types specified in `event_mask`.
      ]]></field>
      <field name="event"><![CDATA[
The event to send to the specified `destination`.
      ]]></field>
      <error type="Window"><![CDATA[
The specified `destination` window does not exist.
      ]]></error>
      <error type="Value"><![CDATA[
The given `event` is neither a core event nor an event defined by an extension.
      ]]></error>
      <see type="event" name="ConfigureNotify" />
    </doc>
  </request>

  <enum name="GrabMode">
    <item name="Sync"> <value>0</value></item>
    <item name="Async"><value>1</value></item>
    <doc>
      <field name="Sync"><![CDATA[
The state of the keyboard appears to freeze: No further keyboard events are
generated by the server until the grabbing client issues a releasing
`AllowEvents` request or until the keyboard grab is released.
      ]]></field>
      <field name="Async"><![CDATA[
Keyboard event processing continues normally.
      ]]></field>
    </doc>
  </enum>

  <enum name="GrabStatus">
    <item name="Success">       <value>0</value></item>
    <item name="AlreadyGrabbed"><value>1</value></item>
    <item name="InvalidTime">   <value>2</value></item>
    <item name="NotViewable">   <value>3</value></item>
    <item name="Frozen">        <value>4</value></item>
  </enum>

  <enum name="Cursor">
    <item name="None"> <value>0</value></item>
  </enum>

  <request name="GrabPointer" opcode="26">
    <field type="BOOL" name="owner_events" />
    <field type="WINDOW" name="grab_window" />
    <field type="CARD16" name="event_mask" mask="EventMask" />
    <field type="BYTE" name="pointer_mode" enum="GrabMode" />
    <field type="BYTE" name="keyboard_mode" enum="GrabMode" />
    <field type="WINDOW" name="confine_to" altenum="Window" />
    <field type="CURSOR" name="cursor" altenum="Cursor" />
    <field type="TIMESTAMP" name="time" altenum="Time" />
    <reply>
      <field type="BYTE" name="status" enum="GrabStatus" />
    </reply>
    <doc>
      <brief>Grab the pointer</brief>
      <description><![CDATA[
Actively grabs control of the pointer. Further pointer events are reported only to the grabbing client. Overrides any active pointer grab by this client.

      ]]></description>
      <example><![CDATA[
/*
 * Grabs the pointer actively
 *
 */
void my_example(xcb_connection *conn, xcb_screen_t *screen, xcb_cursor_t cursor) {
    xcb_grab_pointer_cookie_t cookie;
    xcb_grab_pointer_reply_t *reply;

    cookie = xcb_grab_pointer(
        conn,
        false,               /* get all pointer events specified by the following mask */
        screen->root,        /* grab the root window */
        XCB_NONE,            /* which events to let through */
        XCB_GRAB_MODE_ASYNC, /* pointer events should continue as normal */
        XCB_GRAB_MODE_ASYNC, /* keyboard mode */
        XCB_NONE,            /* confine_to = in which window should the cursor stay */
        cursor,              /* we change the cursor to whatever the user wanted */
        XCB_CURRENT_TIME
    );

    if ((reply = xcb_grab_pointer_reply(conn, cookie, NULL))) {
        if (reply->status == XCB_GRAB_STATUS_SUCCESS)
            printf("successfully grabbed the pointer\\n");
        free(preply);
    }
}
      ]]></example>
      <field name="event_mask"><![CDATA[
Specifies which pointer events are reported to the client.

TODO: which values?
      ]]></field>
      <field name="confine_to"><![CDATA[
Specifies the window to confine the pointer in (the user will not be able to
move the pointer out of that window).

The special value `XCB_NONE` means don't confine the pointer.
      ]]></field>
      <field name="cursor"><![CDATA[
Specifies the cursor that should be displayed or `XCB_NONE` to not change the
cursor.
      ]]></field>
      <field name="owner_events"><![CDATA[
If 1, the `grab_window` will still get the pointer events. If 0, events are not
reported to the `grab_window`.
      ]]></field>
      <field name="grab_window"><![CDATA[
Specifies the window on which the pointer should be grabbed.
      ]]></field>
      <field name="time"><![CDATA[
The time argument allows you to avoid certain circumstances that come up if
applications take a long time to respond or if there are long network delays.
Consider a situation where you have two applications, both of which normally
grab the pointer when clicked on. If both applications specify the timestamp
from the event, the second application may wake up faster and successfully grab
the pointer before the first application. The first application then will get
an indication that the other application grabbed the pointer before its request
was processed.

The special value `XCB_CURRENT_TIME` will be replaced with the current server
time.
      ]]></field>
      <!-- the enum doc is sufficient. -->
      <field name="pointer_mode" />
      <field name="keyboard_mode" />
      <error type="Value"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Window"><![CDATA[
The specified `window` does not exist.
      ]]></error>

      <see type="request" name="GrabKeyboard" />
    </doc>
  </request>

  <request name="UngrabPointer" opcode="27">
    <pad bytes="1" />
    <field type="TIMESTAMP" name="time" altenum="Time" />
    <doc>
      <brief>release the pointer</brief>
      <description><![CDATA[
Releases the pointer and any queued events if you actively grabbed the pointer
before using `xcb_grab_pointer`, `xcb_grab_button` or within a normal button
press.

EnterNotify and LeaveNotify events are generated.
      ]]></description>
      <field name="time"><![CDATA[
Timestamp to avoid race conditions when running X over the network.

The pointer will not be released if `time` is earlier than the
last-pointer-grab time or later than the current X server time.
      ]]></field>
      <field name="name_len"><![CDATA[
Length (in bytes) of `name`.
      ]]></field>
      <field name="name"><![CDATA[
A pattern describing an X core font.
      ]]></field>
      <see type="request" name="GrabPointer" />
      <see type="request" name="GrabButton" />
      <see type="event" name="EnterNotify" />
      <see type="event" name="LeaveNotify" />
    </doc>
  </request>

  <enum name="ButtonIndex">
     <item name="Any"><value>0</value></item>
     <item name="1">  <value>1</value></item>
     <item name="2">  <value>2</value></item>
     <item name="3">  <value>3</value></item>
     <item name="4">  <value>4</value></item>
     <item name="5">  <value>5</value></item>
    <doc>
      <field name="Any"><![CDATA[
Any of the following (or none):
      ]]></field>
      <field name="1"><![CDATA[
The left mouse button.
      ]]></field>
      <field name="2"><![CDATA[
The right mouse button.
      ]]></field>
      <field name="3"><![CDATA[
The middle mouse button.
      ]]></field>
      <field name="4"><![CDATA[
Scroll wheel. TODO: direction?
      ]]></field>
      <field name="5"><![CDATA[
Scroll wheel. TODO: direction?
      ]]></field>
    </doc>
  </enum>

  <request name="GrabButton" opcode="28">
    <field type="BOOL" name="owner_events" />
    <field type="WINDOW" name="grab_window" />
    <field type="CARD16" name="event_mask" mask="EventMask" />
    <field type="CARD8" name="pointer_mode" enum="GrabMode" />
    <field type="CARD8" name="keyboard_mode" enum="GrabMode" />
    <field type="WINDOW" name="confine_to" altenum="Window" />
    <field type="CURSOR" name="cursor" altenum="Cursor" />
    <field type="CARD8" name="button" enum="ButtonIndex" />
    <pad bytes="1" />
    <field type="CARD16" name="modifiers" mask="ModMask" />
    <doc>
      <brief>Grab pointer button(s)</brief>
      <description><![CDATA[
This request establishes a passive grab. The pointer is actively grabbed as
described in GrabPointer, the last-pointer-grab time is set to the time at
which the button was pressed (as transmitted in the ButtonPress event), and the
ButtonPress event is reported if all of the following conditions are true:

The pointer is not grabbed and the specified button is logically pressed when
the specified modifier keys are logically down, and no other buttons or
modifier keys are logically down.

The grab-window contains the pointer.

The confine-to window (if any) is viewable.

A passive grab on the same button/key combination does not exist on any
ancestor of grab-window.

The interpretation of the remaining arguments is the same as for GrabPointer.
The active grab is terminated automatically when the logical state of the
pointer has all buttons released, independent of the logical state of modifier
keys. Note that the logical state of a device (as seen by means of the
protocol) may lag the physical state if device event processing is frozen. This
request overrides all previous passive grabs by the same client on the same
button/key combinations on the same window. A modifier of AnyModifier is
equivalent to issuing the request for all possible modifier combinations
(including the combination of no modifiers). It is not required that all
specified modifiers have currently assigned keycodes. A button of AnyButton is
equivalent to issuing the request for all possible buttons. Otherwise, it is
not required that the button specified currently be assigned to a physical
button.

An Access error is generated if some other client has already issued a
GrabButton request with the same button/key combination on the same window.
When using AnyModifier or AnyButton, the request fails completely (no grabs are
established), and an Access error is generated if there is a conflicting grab
for any combination. The request has no effect on an active grab.

      ]]></description>
      <field name="owner_events"><![CDATA[
If 1, the `grab_window` will still get the pointer events. If 0, events are not
reported to the `grab_window`.
      ]]></field>
      <field name="grab_window"><![CDATA[
Specifies the window on which the pointer should be grabbed.
      ]]></field>
      <field name="event_mask"><![CDATA[
Specifies which pointer events are reported to the client.

TODO: which values?
      ]]></field>
      <field name="confine_to"><![CDATA[
Specifies the window to confine the pointer in (the user will not be able to
move the pointer out of that window).

The special value `XCB_NONE` means don't confine the pointer.
      ]]></field>
      <field name="cursor"><![CDATA[
Specifies the cursor that should be displayed or `XCB_NONE` to not change the
cursor.
      ]]></field>
      <field name="modifiers"><![CDATA[
The modifiers to grab.

Using the special value `XCB_MOD_MASK_ANY` means grab the pointer with all
possible modifier combinations.
      ]]></field>
      <!-- the enum doc is sufficient. -->
      <field name="pointer_mode" />
      <field name="keyboard_mode" />
      <field name="button" />
      <error type="Access"><![CDATA[
Another client has already issued a GrabButton with the same button/key
combination on the same window.
      ]]></error>
      <error type="Value"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Cursor"><![CDATA[
The specified `cursor` does not exist.
      ]]></error>
      <error type="Window"><![CDATA[
The specified `window` does not exist.
      ]]></error>
    </doc>
  </request>

  <request name="UngrabButton" opcode="29">
    <field type="CARD8" name="button" enum="ButtonIndex" />
    <field type="WINDOW" name="grab_window" />
    <field type="CARD16" name="modifiers" mask="ModMask" />
    <pad bytes="2" />
  </request>

  <request name="ChangeActivePointerGrab" opcode="30">
    <pad bytes="1" />
    <field type="CURSOR" name="cursor" altenum="Cursor" />
    <field type="TIMESTAMP" name="time" altenum="Time" />
    <field type="CARD16" name="event_mask" mask="EventMask" />
    <pad bytes="2" />
  </request>

  <request name="GrabKeyboard" opcode="31">
    <field type="BOOL" name="owner_events" />
    <field type="WINDOW" name="grab_window" />
    <field type="TIMESTAMP" name="time" altenum="Time" />
    <field type="BYTE" name="pointer_mode" enum="GrabMode" />
    <field type="BYTE" name="keyboard_mode" enum="GrabMode" />
    <pad bytes="2" />
    <reply>
      <field type="BYTE" name="status" enum="GrabStatus" />
    </reply>
    <doc>
      <brief>Grab the keyboard</brief>
      <description><![CDATA[
Actively grabs control of the keyboard and generates FocusIn and FocusOut
events. Further key events are reported only to the grabbing client.

Any active keyboard grab by this client is overridden. If the keyboard is
actively grabbed by some other client, `AlreadyGrabbed` is returned. If
`grab_window` is not viewable, `GrabNotViewable` is returned. If the keyboard
is frozen by an active grab of another client, `GrabFrozen` is returned. If the
specified `time` is earlier than the last-keyboard-grab time or later than the
current X server time, `GrabInvalidTime` is returned. Otherwise, the
last-keyboard-grab time is set to the specified time.
      ]]></description>
      <example><![CDATA[
/*
 * Grabs the keyboard actively
 *
 */
void my_example(xcb_connection *conn, xcb_screen_t *screen) {
    xcb_grab_keyboard_cookie_t cookie;
    xcb_grab_keyboard_reply_t *reply;

    cookie = xcb_grab_keyboard(
        conn,
        true,                /* report events */
        screen->root,        /* grab the root window */
        XCB_CURRENT_TIME,
        XCB_GRAB_MODE_ASYNC, /* process events as normal, do not require sync */
        XCB_GRAB_MODE_ASYNC
    );

    if ((reply = xcb_grab_keyboard_reply(conn, cookie, NULL))) {
        if (reply->status == XCB_GRAB_STATUS_SUCCESS)
            printf("successfully grabbed the keyboard\\n");

        free(reply);
    }
}
      ]]></example>
      <field name="owner_events"><![CDATA[
If 1, the `grab_window` will still get the pointer events. If 0, events are not
reported to the `grab_window`.
      ]]></field>
      <field name="grab_window"><![CDATA[
Specifies the window on which the pointer should be grabbed.
      ]]></field>
      <field name="time"><![CDATA[
Timestamp to avoid race conditions when running X over the network.

The special value `XCB_CURRENT_TIME` will be replaced with the current server
time.
      ]]></field>
      <!-- the enum doc is sufficient. -->
      <field name="pointer_mode" />
      <field name="keyboard_mode" />
      <error type="Value"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Window"><![CDATA[
The specified `window` does not exist.
      ]]></error>
      <see type="request" name="GrabPointer" />
    </doc>
  </request>

  <request name="UngrabKeyboard" opcode="32">
    <pad bytes="1" />
    <field type="TIMESTAMP" name="time" altenum="Time" />
  </request>

  <!-- Use KEYCODE any = { XCBGrabAny }; to grab any key -->
  <enum name="Grab">
    <item name="Any"><value>0</value></item>
  </enum>

  <request name="GrabKey" opcode="33">
    <field type="BOOL" name="owner_events" />
    <field type="WINDOW" name="grab_window" />
    <field type="CARD16" name="modifiers" mask="ModMask" />
    <field type="KEYCODE" name="key" altenum="Grab" />
    <field type="CARD8" name="pointer_mode" enum="GrabMode" />
    <field type="CARD8" name="keyboard_mode" enum="GrabMode" />
    <pad bytes="3" />
    <doc>
      <brief>Grab keyboard key(s)</brief>
      <description><![CDATA[
Establishes a passive grab on the keyboard. In the future, the keyboard is
actively grabbed (as for `GrabKeyboard`), the last-keyboard-grab time is set to
the time at which the key was pressed (as transmitted in the KeyPress event),
and the KeyPress event is reported if all of the following conditions are true:

The keyboard is not grabbed and the specified key (which can itself be a
modifier key) is logically pressed when the specified modifier keys are
logically down, and no other modifier keys are logically down.

Either the grab_window is an ancestor of (or is) the focus window, or the
grab_window is a descendant of the focus window and contains the pointer.

A passive grab on the same key combination does not exist on any ancestor of
grab_window.

The interpretation of the remaining arguments is as for XGrabKeyboard.  The active grab is terminated
automatically when the logical state of the keyboard has the specified key released (independent of the
logical state of the modifier keys), at which point a KeyRelease event is reported to the grabbing window.

Note that the logical state of a device (as seen by client applications) may lag the physical state if
device event processing is frozen.

A modifiers argument of AnyModifier is equivalent to issuing the request for all possible modifier combinations (including the combination of no modifiers).  It is not required that all modifiers specified
have currently assigned KeyCodes.  A keycode argument of AnyKey is equivalent to issuing the request for
all possible KeyCodes.  Otherwise, the specified keycode must be in the range specified by min_keycode
and max_keycode in the connection setup, or a BadValue error results.

If some other client has issued a XGrabKey with the same key combination on the same window, a BadAccess
error results.  When using AnyModifier or AnyKey, the request fails completely, and a BadAccess error
results (no grabs are established) if there is a conflicting grab for any combination.

      ]]></description>
      <field name="owner_events"><![CDATA[
If 1, the `grab_window` will still get the pointer events. If 0, events are not
reported to the `grab_window`.
      ]]></field>
      <field name="grab_window"><![CDATA[
Specifies the window on which the pointer should be grabbed.
      ]]></field>
      <field name="key"><![CDATA[
The keycode of the key to grab.

The special value `XCB_GRAB_ANY` means grab any key.
      ]]></field>
      <field name="cursor"><![CDATA[
Specifies the cursor that should be displayed or `XCB_NONE` to not change the
cursor.
      ]]></field>
      <field name="modifiers"><![CDATA[
The modifiers to grab.

Using the special value `XCB_MOD_MASK_ANY` means grab the pointer with all
possible modifier combinations.
      ]]></field>
      <!-- the enum doc is sufficient. -->
      <field name="pointer_mode" />
      <field name="keyboard_mode" />
      <error type="Access"><![CDATA[
Another client has already issued a GrabKey with the same button/key
combination on the same window.
      ]]></error>
      <error type="Value"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Window"><![CDATA[
The specified `window` does not exist.
      ]]></error>
      <see type="request" name="GrabKeyboard" />
    </doc>
  </request>

  <request name="UngrabKey" opcode="34">
    <field type="KEYCODE" name="key" altenum="Grab" />
    <field type="WINDOW" name="grab_window" />
    <field type="CARD16" name="modifiers" mask="ModMask" />
    <pad bytes="2" />
    <doc>
      <brief>release a key combination</brief>
      <description><![CDATA[
Releases the key combination on `grab_window` if you grabbed it using
`xcb_grab_key` before.
      ]]></description>
      <field name="key"><![CDATA[
The keycode of the specified key combination.

Using the special value `XCB_GRAB_ANY` means releasing all possible key codes.
      ]]></field>
      <field name="grab_window"><![CDATA[
The window on which the grabbed key combination will be released.
      ]]></field>
      <field name="modifiers"><![CDATA[
The modifiers of the specified key combination.

Using the special value `XCB_MOD_MASK_ANY` means releasing the key combination
with every possible modifier combination.
      ]]></field>
      <error type="Window"><![CDATA[
The specified `grab_window` does not exist.
      ]]></error>
      <error type="Value"><![CDATA[
TODO: reasons?
      ]]></error>
      <see type="request" name="GrabKey" />
      <see type="program" name="xev" />
    </doc>
  </request>

  <enum name="Allow">
    <item name="AsyncPointer">  <value>0</value></item>
    <item name="SyncPointer">   <value>1</value></item>
    <item name="ReplayPointer"> <value>2</value></item>
    <item name="AsyncKeyboard"> <value>3</value></item>
    <item name="SyncKeyboard">  <value>4</value></item>
    <item name="ReplayKeyboard"><value>5</value></item>
    <item name="AsyncBoth">     <value>6</value></item>
    <item name="SyncBoth">      <value>7</value></item>
    <doc>
      <field name="AsyncPointer"><![CDATA[
For AsyncPointer, if the pointer is frozen by the client, pointer event
processing continues normally. If the pointer is frozen twice by the client on
behalf of two separate grabs, AsyncPointer thaws for both. AsyncPointer has no
effect if the pointer is not frozen by the client, but the pointer need not be
grabbed by the client.

TODO: rewrite this in more understandable terms.
      ]]></field>
      <field name="SyncPointer"><![CDATA[
For SyncPointer, if the pointer is frozen and actively grabbed by the client,
pointer event processing continues normally until the next ButtonPress or
ButtonRelease event is reported to the client, at which time the pointer again
appears to freeze. However, if the reported event causes the pointer grab to be
released, then the pointer does not freeze. SyncPointer has no effect if the
pointer is not frozen by the client or if the pointer is not grabbed by the
client.
      ]]></field>
      <field name="ReplayPointer"><![CDATA[
For ReplayPointer, if the pointer is actively grabbed by the client and is
frozen as the result of an event having been sent to the client (either from
the activation of a GrabButton or from a previous AllowEvents with mode
SyncPointer but not from a GrabPointer), then the pointer grab is released and
that event is completely reprocessed, this time ignoring any passive grabs at
or above (towards the root) the grab-window of the grab just released. The
request has no effect if the pointer is not grabbed by the client or if the
pointer is not frozen as the result of an event.
      ]]></field>
      <field name="AsyncKeyboard"><![CDATA[
For AsyncKeyboard, if the keyboard is frozen by the client, keyboard event
processing continues normally. If the keyboard is frozen twice by the client on
behalf of two separate grabs, AsyncKeyboard thaws for both. AsyncKeyboard has
no effect if the keyboard is not frozen by the client, but the keyboard need
not be grabbed by the client.
      ]]></field>
      <field name="SyncKeyboard"><![CDATA[
For SyncKeyboard, if the keyboard is frozen and actively grabbed by the client,
keyboard event processing continues normally until the next KeyPress or
KeyRelease event is reported to the client, at which time the keyboard again
appears to freeze. However, if the reported event causes the keyboard grab to
be released, then the keyboard does not freeze. SyncKeyboard has no effect if
the keyboard is not frozen by the client or if the keyboard is not grabbed by
the client.
      ]]></field>
      <field name="ReplayKeyboard"><![CDATA[
For ReplayKeyboard, if the keyboard is actively grabbed by the client and is
frozen as the result of an event having been sent to the client (either from
the activation of a GrabKey or from a previous AllowEvents with mode
SyncKeyboard but not from a GrabKeyboard), then the keyboard grab is released
and that event is completely reprocessed, this time ignoring any passive grabs
at or above (towards the root) the grab-window of the grab just released. The
request has no effect if the keyboard is not grabbed by the client or if the
keyboard is not frozen as the result of an event.
      ]]></field>
      <field name="SyncBoth"><![CDATA[
For SyncBoth, if both pointer and keyboard are frozen by the client, event
processing (for both devices) continues normally until the next ButtonPress,
ButtonRelease, KeyPress, or KeyRelease event is reported to the client for a
grabbed device (button event for the pointer, key event for the keyboard), at
which time the devices again appear to freeze. However, if the reported event
causes the grab to be released, then the devices do not freeze (but if the
other device is still grabbed, then a subsequent event for it will still cause
both devices to freeze). SyncBoth has no effect unless both pointer and
keyboard are frozen by the client. If the pointer or keyboard is frozen twice
by the client on behalf of two separate grabs, SyncBoth thaws for both (but a
subsequent freeze for SyncBoth will only freeze each device once).
      ]]></field>
      <field name="AsyncBoth"><![CDATA[
For AsyncBoth, if the pointer and the keyboard are frozen by the client, event
processing for both devices continues normally. If a device is frozen twice by
the client on behalf of two separate grabs, AsyncBoth thaws for both. AsyncBoth
has no effect unless both pointer and keyboard are frozen by the client.
      ]]></field>
    </doc>
  </enum>

  <request name="AllowEvents" opcode="35">
    <field type="CARD8" name="mode" enum="Allow" />
    <field type="TIMESTAMP" name="time" altenum="Time" />
    <doc>
      <brief>release queued events</brief>
      <description><![CDATA[
Releases queued events if the client has caused a device (pointer/keyboard) to
freeze due to grabbing it actively. This request has no effect if `time` is
earlier than the last-grab time of the most recent active grab for this client
or if `time` is later than the current X server time.
      ]]></description>
      <!-- the enum doc is sufficient. -->
      <field name="mode" />
      <field name="time"><![CDATA[
Timestamp to avoid race conditions when running X over the network.

The special value `XCB_CURRENT_TIME` will be replaced with the current server
time.
      ]]></field>
      <error type="Value"><![CDATA[
You specified an invalid `mode`.
      ]]></error>
    </doc>
  </request>

  <request name="GrabServer" opcode="36" />

  <request name="UngrabServer" opcode="37" />

  <request name="QueryPointer" opcode="38">
    <pad bytes="1" />
    <field type="WINDOW" name="window" />
    <reply>
      <field type="BOOL" name="same_screen" />
      <field type="WINDOW" name="root" />
      <field type="WINDOW" name="child" altenum="Window" />
      <field type="INT16" name="root_x" />
      <field type="INT16" name="root_y" />
      <field type="INT16" name="win_x" />
      <field type="INT16" name="win_y" />
      <field type="CARD16" name="mask" mask="KeyButMask" />
      <pad bytes="2" />
      <doc>
        <field name="same_screen"><![CDATA[
If `same_screen` is False, then the pointer is not on the same screen as the
argument window, `child` is None, and `win_x` and `win_y` are zero. If
`same_screen` is True, then `win_x` and `win_y` are the pointer coordinates
relative to the argument window's origin, and child is the child containing the
pointer, if any.
        ]]></field>
        <field name="root"><![CDATA[
The root window the pointer is logically on.
        ]]></field>
        <field name="child"><![CDATA[
The child window containing the pointer, if any, if `same_screen` is true. If
`same_screen` is false, `XCB_NONE` is returned.
        ]]></field>
        <field name="root_x"><![CDATA[
The pointer X position, relative to `root`.
        ]]></field>
        <field name="root_y"><![CDATA[
The pointer Y position, relative to `root`.
        ]]></field>
        <field name="win_x"><![CDATA[
The pointer X coordinate, relative to `child`, if `same_screen` is true. Zero
otherwise.
        ]]></field>
        <field name="win_y"><![CDATA[
The pointer Y coordinate, relative to `child`, if `same_screen` is true. Zero
otherwise.
        ]]></field>
        <field name="mask"><![CDATA[
The current logical state of the modifier keys and the buttons. Note that the
logical state of a device (as seen by means of the protocol) may lag the
physical state if device event processing is frozen.
        ]]></field>
      </doc>
    </reply>
    <doc>
      <brief>get pointer coordinates</brief>
      <description><![CDATA[
Gets the root window the pointer is logically on and the pointer coordinates
relative to the root window's origin.
      ]]></description>
      <field name="window"><![CDATA[
A window to check if the pointer is on the same screen as `window` (see the
`same_screen` field in the reply).
      ]]></field>
      <error type="Window"><![CDATA[
The specified `window` does not exist.
      ]]></error>
    </doc>
  </request>

  <struct name="TIMECOORD">
    <field type="TIMESTAMP" name="time" />
    <field type="INT16" name="x" />
    <field type="INT16" name="y" />
  </struct>

  <request name="GetMotionEvents" opcode="39">
    <pad bytes="1" />
    <field type="WINDOW" name="window" />
    <field type="TIMESTAMP" name="start" altenum="Time" />
    <field type="TIMESTAMP" name="stop" altenum="Time" />
    <reply>
      <pad bytes="1" />
      <field type="CARD32" name="events_len" />
      <pad bytes="20" />
      <list type="TIMECOORD" name="events">
        <fieldref>events_len</fieldref>
      </list>
    </reply>
  </request>

  <request name="TranslateCoordinates" opcode="40">
    <pad bytes="1" />
    <field type="WINDOW" name="src_window" />
    <field type="WINDOW" name="dst_window" />
    <field type="INT16" name="src_x" />
    <field type="INT16" name="src_y" />
    <reply>
      <field type="BOOL" name="same_screen" />
      <field type="WINDOW" name="child" altenum="Window" />
      <field type="INT16" name="dst_x" />
      <field type="INT16" name="dst_y" />
    </reply>
  </request>

  <request name="WarpPointer" opcode="41">
    <pad bytes="1" />
    <field type="WINDOW" name="src_window" altenum="Window" />
    <field type="WINDOW" name="dst_window" altenum="Window" />
    <field type="INT16" name="src_x" />
    <field type="INT16" name="src_y" />
    <field type="CARD16" name="src_width" />
    <field type="CARD16" name="src_height" />
    <field type="INT16" name="dst_x" />
    <field type="INT16" name="dst_y" />
    <doc>
      <brief>move mouse pointer</brief>
      <description><![CDATA[
Moves the mouse pointer to the specified position.

If `src_window` is not `XCB_NONE` (TODO), the move will only take place if the
pointer is inside `src_window` and within the rectangle specified by (`src_x`,
`src_y`, `src_width`, `src_height`). The rectangle coordinates are relative to
`src_window`.

If `dst_window` is not `XCB_NONE` (TODO), the pointer will be moved to the
offsets (`dst_x`, `dst_y`) relative to `dst_window`. If `dst_window` is
`XCB_NONE` (TODO), the pointer will be moved by the offsets (`dst_x`, `dst_y`)
relative to the current position of the pointer.
      ]]></description>
      <field name="src_window"><![CDATA[
If `src_window` is not `XCB_NONE` (TODO), the move will only take place if the
pointer is inside `src_window` and within the rectangle specified by (`src_x`,
`src_y`, `src_width`, `src_height`). The rectangle coordinates are relative to
`src_window`.
      ]]></field>
      <field name="dst_window"><![CDATA[
If `dst_window` is not `XCB_NONE` (TODO), the pointer will be moved to the
offsets (`dst_x`, `dst_y`) relative to `dst_window`. If `dst_window` is
`XCB_NONE` (TODO), the pointer will be moved by the offsets (`dst_x`, `dst_y`)
relative to the current position of the pointer.
      ]]></field>
      <error type="Window"><![CDATA[
TODO: reasons?
      ]]></error>
      <see type="request" name="SetInputFocus" />
    </doc>
  </request>

  <!-- used for revert_to and focus -->
  <enum name="InputFocus">
    <item name="None">       <value>0</value></item>
    <item name="PointerRoot"><value>1</value></item>
    <item name="Parent">     <value>2</value></item>  <!-- revert_to only -->
    <item name="FollowKeyboard"><value>3</value></item>  <!-- xinput extension only -->
    <doc>
      <field name="None"><![CDATA[
The focus reverts to `XCB_NONE`, so no window will have the input focus.
      ]]></field>
      <field name="PointerRoot"><![CDATA[
The focus reverts to `XCB_POINTER_ROOT` respectively. When the focus reverts,
FocusIn and FocusOut events are generated, but the last-focus-change time is
not changed.
      ]]></field>
      <field name="Parent"><![CDATA[
The focus reverts to the parent (or closest viewable ancestor) and the new
revert_to value is `XCB_INPUT_FOCUS_NONE`.
      ]]></field>
      <field name="FollowKeyboard"><![CDATA[
NOT YET DOCUMENTED. Only relevant for the xinput extension.
      ]]></field>
    </doc>
  </enum>

  <request name="SetInputFocus" opcode="42">
    <field type="CARD8" name="revert_to" enum="InputFocus" />
    <field type="WINDOW" name="focus" altenum="InputFocus" />
    <field type="TIMESTAMP" name="time" altenum="Time" />
    <doc>
      <brief>Sets input focus</brief>
      <description><![CDATA[
Changes the input focus and the last-focus-change time. If the specified `time`
is earlier than the current last-focus-change time, the request is ignored (to
avoid race conditions when running X over the network).

A FocusIn and FocusOut event is generated when focus is changed.
      ]]></description>
      <field name="focus"><![CDATA[
The window to focus. All keyboard events will be reported to this window. The
window must be viewable (TODO), or a `xcb_match_error_t` occurs (TODO).

If `focus` is `XCB_NONE` (TODO), all keyboard events are
discarded until a new focus window is set.

If `focus` is `XCB_POINTER_ROOT` (TODO), focus is on the root window of the
screen on which the pointer is on currently.
      ]]></field>
      <field name="time"><![CDATA[
Timestamp to avoid race conditions when running X over the network.

The special value `XCB_CURRENT_TIME` will be replaced with the current server
time.
      ]]></field>
      <field name="revert_to"><![CDATA[
Specifies what happens when the `focus` window becomes unviewable (if `focus`
is neither `XCB_NONE` nor `XCB_POINTER_ROOT`).
      ]]></field>
      <error type="Window"><![CDATA[
The specified `focus` window does not exist.
      ]]></error>
      <error type="Match"><![CDATA[
The specified `focus` window is not viewable.
      ]]></error>
      <error type="Value"><![CDATA[
TODO: Reasons?
      ]]></error>
      <see type="event" name="FocusIn" />
      <see type="event" name="FocusOut" />
    </doc>

  </request>

  <request name="GetInputFocus" opcode="43">
    <reply>
      <field type="CARD8" name="revert_to" enum="InputFocus" />
      <field type="WINDOW" name="focus" altenum="InputFocus" />
    </reply>
  </request>

  <request name="QueryKeymap" opcode="44">
    <reply>
      <pad bytes="1" />
      <list type="CARD8" name="keys"><value>32</value></list>
    </reply>
  </request>

  <request name="OpenFont" opcode="45">
    <pad bytes="1" />
    <field type="FONT" name="fid" />
    <field type="CARD16" name="name_len" />
    <pad bytes="2" />
    <list type="char" name="name">
      <fieldref>name_len</fieldref>
    </list>
    <doc>
      <brief>opens a font</brief>
      <description><![CDATA[
Opens any X core font matching the given `name` (for example "-misc-fixed-*").

Note that X core fonts are deprecated (but still supported) in favor of
client-side rendering using Xft.
      ]]></description>
      <field name="fid"><![CDATA[
The ID with which you will refer to the font, created by `xcb_generate_id`.
      ]]></field>
      <field name="name_len"><![CDATA[
Length (in bytes) of `name`.
      ]]></field>
      <field name="name"><![CDATA[
A pattern describing an X core font.
      ]]></field>
      <error type="Name"><![CDATA[
No font matches the given `name`.
      ]]></error>
      <see type="function" name="xcb_generate_id" />
    </doc>
  </request>

  <request name="CloseFont" opcode="46">
    <pad bytes="1" />
    <field type="FONT" name="font" />
  </request>

  <enum name="FontDraw">
    <item name="LeftToRight"><value>0</value></item>
    <item name="RightToLeft"><value>1</value></item>
  </enum>

  <struct name="FONTPROP">
    <field type="ATOM" name="name" />
    <field type="CARD32" name="value" />
  </struct>

  <struct name="CHARINFO">
    <field type="INT16" name="left_side_bearing" />
    <field type="INT16" name="right_side_bearing" />
    <field type="INT16" name="character_width" />
    <field type="INT16" name="ascent" />
    <field type="INT16" name="descent" />
    <field type="CARD16" name="attributes" />
  </struct>

  <request name="QueryFont" opcode="47">
    <pad bytes="1" />
    <field type="FONTABLE" name="font" />
    <reply>
      <pad bytes="1" />
      <field type="CHARINFO" name="min_bounds" />
      <pad bytes="4" />
      <field type="CHARINFO" name="max_bounds" />
      <pad bytes="4" />
      <field type="CARD16" name="min_char_or_byte2" />
      <field type="CARD16" name="max_char_or_byte2" />
      <field type="CARD16" name="default_char" />
      <field type="CARD16" name="properties_len" />
      <field type="BYTE" name="draw_direction" enum="FontDraw" />
      <field type="CARD8" name="min_byte1" />
      <field type="CARD8" name="max_byte1" />
      <field type="BOOL" name="all_chars_exist" />
      <field type="INT16" name="font_ascent" />
      <field type="INT16" name="font_descent" />
      <field type="CARD32" name="char_infos_len" />
      <list type="FONTPROP" name="properties">
        <fieldref>properties_len</fieldref>
      </list>
      <list type="CHARINFO" name="char_infos">
        <fieldref>char_infos_len</fieldref>
      </list>
      <doc>
        <field name="min_bounds"><![CDATA[
minimum bounds over all existing char
        ]]></field>
        <field name="max_bounds"><![CDATA[
maximum bounds over all existing char
        ]]></field>
        <field name="min_char_or_byte2"><![CDATA[
first character
        ]]></field>
        <field name="max_char_or_byte2"><![CDATA[
last character
        ]]></field>
        <field name="default_char"><![CDATA[
char to print for undefined character
        ]]></field>
        <field name="properties_len"><![CDATA[
how many properties there are
        ]]></field>
        <field name="all_chars_exist"><![CDATA[
flag if all characters have nonzero size
        ]]></field>
        <field name="font_ascent"><![CDATA[
baseline to top edge of raster
        ]]></field>
        <field name="font_descent"><![CDATA[
baseline to bottom edge of raster
        ]]></field>
        <!-- enum doc is sufficient -->
        <field name="draw_direction" />
      </doc>
    </reply>
    <doc>
      <brief>query font metrics</brief>
      <description><![CDATA[
Queries information associated with the font.
      ]]></description>
      <field name="font"><![CDATA[
The fontable (Font or Graphics Context) to query.
      ]]></field>
      <!-- TODO: example -->
    </doc>
  </request>

  <request name="QueryTextExtents" opcode="48">
    <exprfield type="BOOL" name="odd_length">
      <op op="&amp;"><fieldref>string_len</fieldref><value>1</value></op>
    </exprfield>
    <field type="FONTABLE" name="font" />
    <list type="CHAR2B" name="string" />
    <reply>
      <field type="BYTE" name="draw_direction" enum="FontDraw" />
      <field type="INT16" name="font_ascent" />
      <field type="INT16" name="font_descent" />
      <field type="INT16" name="overall_ascent" />
      <field type="INT16" name="overall_descent" />
      <field type="INT32" name="overall_width" />
      <field type="INT32" name="overall_left" />
      <field type="INT32" name="overall_right" />
    </reply>
    <doc>
      <brief>get text extents</brief>
      <description><![CDATA[
Query text extents from the X11 server. This request returns the bounding box
of the specified 16-bit character string in the specified `font` or the font
contained in the specified graphics context.

`font_ascent` is set to the maximum of the ascent metrics of all characters in
the string. `font_descent` is set to the maximum of the descent metrics.
`overall_width` is set to the sum of the character-width metrics of all
characters in the string. For each character in the string, let W be the sum of
the character-width metrics of all characters preceding it in the string. Let L
be the left-side-bearing metric of the character plus W. Let R be the
right-side-bearing metric of the character plus W. The lbearing member is set
to the minimum L of all characters in the string. The rbearing member is set to
the maximum R.

For fonts defined with linear indexing rather than 2-byte matrix indexing, each
`xcb_char2b_t` structure is interpreted as a 16-bit number with byte1 as the
most significant byte. If the font has no defined default character, undefined
characters in the string are taken to have all zero metrics.

Characters with all zero metrics are ignored. If the font has no defined
default_char, the undefined characters in the string are also ignored.
      ]]></description>
      <field name="font"><![CDATA[
The `font` to calculate text extents in. You can also pass a graphics context.
      ]]></field>
      <field name="string_len"><![CDATA[
The number of characters in `string`.
      ]]></field>
      <field name="string"><![CDATA[
The text to get text extents for.
      ]]></field>
      <error type="GC"><![CDATA[
The specified graphics context does not exist.
      ]]></error>
      <error type="Font"><![CDATA[
The specified `font` does not exist.
      ]]></error>
    </doc>
  </request>

  <struct name="STR">
    <field type="CARD8" name="name_len" />
    <list type="char" name="name">
      <fieldref>name_len</fieldref>
    </list>
  </struct>

  <request name="ListFonts" opcode="49">
    <pad bytes="1" />
    <field type="CARD16" name="max_names" />
    <field type="CARD16" name="pattern_len" />
    <list type="char" name="pattern">
      <fieldref>pattern_len</fieldref>
    </list>
    <reply>
      <pad bytes="1" />
      <field type="CARD16" name="names_len" />
      <pad bytes="22" />
      <list type="STR" name="names">
        <fieldref>names_len</fieldref>
      </list>
      <doc>
        <field name="names_len"><![CDATA[
The number of font names.
        ]]></field>
      </doc>
    </reply>
    <doc>
      <brief>get matching font names</brief>
      <description><![CDATA[
Gets a list of available font names which match the given `pattern`.
      ]]></description>
      <field name="pattern_len"><![CDATA[
The length (in bytes) of `pattern`.
      ]]></field>
      <field name="pattern"><![CDATA[
A font pattern, for example "-misc-fixed-*".

The asterisk (*) is a wildcard for any number of characters. The question mark
(?) is a wildcard for a single character. Use of uppercase or lowercase does
not matter.
      ]]></field>
      <field name="max_names"><![CDATA[
The maximum number of fonts to be returned.
      ]]></field>
    </doc>
  </request>

  <request name="ListFontsWithInfo" opcode="50">
    <pad bytes="1" />
    <field type="CARD16" name="max_names" />
    <field type="CARD16" name="pattern_len" />
    <list type="char" name="pattern">
      <fieldref>pattern_len</fieldref>
    </list>
    <reply>
      <field type="CARD8" name="name_len" />
      <field type="CHARINFO" name="min_bounds" />
      <pad bytes="4" />
      <field type="CHARINFO" name="max_bounds" />
      <pad bytes="4" />
      <field type="CARD16" name="min_char_or_byte2" />
      <field type="CARD16" name="max_char_or_byte2" />
      <field type="CARD16" name="default_char" />
      <field type="CARD16" name="properties_len" />
      <field type="BYTE" name="draw_direction" enum="FontDraw" />
      <field type="CARD8" name="min_byte1" />
      <field type="CARD8" name="max_byte1" />
      <field type="BOOL" name="all_chars_exist" />
      <field type="INT16" name="font_ascent" />
      <field type="INT16" name="font_descent" />
      <field type="CARD32" name="replies_hint" />
      <list type="FONTPROP" name="properties">
        <fieldref>properties_len</fieldref>
      </list>
      <list type="char" name="name">
        <fieldref>name_len</fieldref>
      </list>
      <doc>
        <field name="name_len"><![CDATA[
The number of matched font names.
        ]]></field>
        <field name="min_bounds"><![CDATA[
minimum bounds over all existing char
        ]]></field>
        <field name="max_bounds"><![CDATA[
maximum bounds over all existing char
        ]]></field>
        <field name="min_char_or_byte2"><![CDATA[
first character
        ]]></field>
        <field name="max_char_or_byte2"><![CDATA[
last character
        ]]></field>
        <field name="default_char"><![CDATA[
char to print for undefined character
        ]]></field>
        <field name="properties_len"><![CDATA[
how many properties there are
        ]]></field>
        <field name="all_chars_exist"><![CDATA[
flag if all characters have nonzero size
        ]]></field>
        <field name="font_ascent"><![CDATA[
baseline to top edge of raster
        ]]></field>
        <field name="font_descent"><![CDATA[
baseline to bottom edge of raster
        ]]></field>
        <field name="replies_hint"><![CDATA[
An indication of how many more fonts will be returned. This is only a hint and
may be larger or smaller than the number of fonts actually returned. A zero
value does not guarantee that no more fonts will be returned.
        ]]></field>
        <!-- enum doc is sufficient -->
        <field name="draw_direction" />
      </doc>
    </reply>
    <doc>
      <brief>get matching font names and information</brief>
      <description><![CDATA[
Gets a list of available font names which match the given `pattern`.
      ]]></description>
      <field name="pattern_len"><![CDATA[
The length (in bytes) of `pattern`.
      ]]></field>
      <field name="pattern"><![CDATA[
A font pattern, for example "-misc-fixed-*".

The asterisk (*) is a wildcard for any number of characters. The question mark
(?) is a wildcard for a single character. Use of uppercase or lowercase does
not matter.
      ]]></field>
      <field name="max_names"><![CDATA[
The maximum number of fonts to be returned.
      ]]></field>
    </doc>

  </request>

  <request name="SetFontPath" opcode="51">
    <pad bytes="1" />
    <field type="CARD16" name="font_qty" />
    <pad bytes="2" />
    <list type="STR" name="font">
      <fieldref>font_qty</fieldref>
    </list>
  </request>

  <request name="GetFontPath" opcode="52">
    <reply>
      <pad bytes="1" />
      <field type="CARD16" name="path_len" />
      <pad bytes="22" />
      <list type="STR" name="path">
        <fieldref>path_len</fieldref>
      </list>
    </reply>
  </request>

  <request name="CreatePixmap" opcode="53">
    <field type="CARD8" name="depth" />
    <field type="PIXMAP" name="pid" />
    <field type="DRAWABLE" name="drawable" />
    <field type="CARD16" name="width" />
    <field type="CARD16" name="height" />
    <doc>
      <brief>Creates a pixmap</brief>
      <description><![CDATA[
Creates a pixmap. The pixmap can only be used on the same screen as `drawable`
is on and only with drawables of the same `depth`.
      ]]></description>
      <field name="depth"><![CDATA[
TODO
      ]]></field>
      <field name="pid"><![CDATA[
The ID with which you will refer to the new pixmap, created by
`xcb_generate_id`.
      ]]></field>
      <field name="drawable"><![CDATA[
Drawable to get the screen from.
      ]]></field>
      <field name="width"><![CDATA[
The width of the new pixmap.
      ]]></field>
      <field name="height"><![CDATA[
The height of the new pixmap.
      ]]></field>
      <error type="Value"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Drawable"><![CDATA[
The specified `drawable` (Window or Pixmap) does not exist.
      ]]></error>
      <error type="Alloc"><![CDATA[
The X server could not allocate the requested resources (no memory?).
      ]]></error>
      <see type="function" name="xcb_generate_id" />
    </doc>
  </request>

  <request name="FreePixmap" opcode="54">
    <pad bytes="1" />
    <field type="PIXMAP" name="pixmap" />
    <doc>
      <brief>Destroys a pixmap</brief>
      <description><![CDATA[
Deletes the association between the pixmap ID and the pixmap. The pixmap
storage will be freed when there are no more references to it.
      ]]></description>
      <field name="pixmap"><![CDATA[The pixmap to destroy.]]></field>
      <error type="Pixmap"><![CDATA[
The specified pixmap does not exist.
      ]]></error>
    </doc>
  </request>
  
  <enum name="GC">
    <item name="Function">          <bit>0</bit></item>
    <item name="PlaneMask">         <bit>1</bit></item>
    <item name="Foreground">        <bit>2</bit></item>
    <item name="Background">        <bit>3</bit></item>
    <item name="LineWidth">         <bit>4</bit></item>
    <item name="LineStyle">         <bit>5</bit></item>
    <item name="CapStyle">          <bit>6</bit></item>
    <item name="JoinStyle">         <bit>7</bit></item>
    <item name="FillStyle">         <bit>8</bit></item>
    <item name="FillRule">          <bit>9</bit></item>
    <item name="Tile">              <bit>10</bit></item>
    <item name="Stipple">           <bit>11</bit></item>
    <item name="TileStippleOriginX"><bit>12</bit></item>
    <item name="TileStippleOriginY"><bit>13</bit></item>
    <item name="Font">              <bit>14</bit></item>
    <item name="SubwindowMode">     <bit>15</bit></item>
    <item name="GraphicsExposures"> <bit>16</bit></item>
    <item name="ClipOriginX">       <bit>17</bit></item>
    <item name="ClipOriginY">       <bit>18</bit></item>
    <item name="ClipMask">          <bit>19</bit></item>
    <item name="DashOffset">        <bit>20</bit></item>
    <item name="DashList">          <bit>21</bit></item>
    <item name="ArcMode">           <bit>22</bit></item>
    <doc>
      <field name="Function"><![CDATA[
TODO: Refer to GX
      ]]></field>
      <field name="PlaneMask"><![CDATA[
In graphics operations, given a source and destination pixel, the result is
computed bitwise on corresponding bits of the pixels; that is, a Boolean
operation is performed in each bit plane. The plane-mask restricts the
operation to a subset of planes, so the result is:

        ((src FUNC dst) AND plane-mask) OR (dst AND (NOT plane-mask))
      ]]></field>
      <field name="Foreground"><![CDATA[
Foreground colorpixel.
      ]]></field>
      <field name="Background"><![CDATA[
Background colorpixel.
      ]]></field>
      <field name="LineWidth"><![CDATA[
The line-width is measured in pixels and can be greater than or equal to one, a wide line, or the
special value zero, a thin line.
      ]]></field>
      <field name="LineStyle"><![CDATA[
The line-style defines which sections of a line are drawn:
Solid                The full path of the line is drawn.
DoubleDash           The full path of the line is drawn, but the even dashes are filled differently
                     than the odd dashes (see fill-style), with Butt cap-style used where even and
                     odd dashes meet.
OnOffDash            Only the even dashes are drawn, and cap-style applies to all internal ends of
                     the individual dashes (except NotLast is treated as Butt).
      ]]></field>
      <field name="CapStyle"><![CDATA[
The cap-style defines how the endpoints of a path are drawn:
NotLast    The result is equivalent to Butt, except that for a line-width of zero the final
           endpoint is not drawn.
Butt       The result is square at the endpoint (perpendicular to the slope of the line)
           with no projection beyond.
Round      The result is a circular arc with its diameter equal to the line-width, centered
           on the endpoint; it is equivalent to Butt for line-width zero.
Projecting The result is square at the end, but the path continues beyond the endpoint for
           a distance equal to half the line-width; it is equivalent to Butt for line-width
           zero.
      ]]></field>
      <field name="JoinStyle"><![CDATA[
The join-style defines how corners are drawn for wide lines:
Miter               The outer edges of the two lines extend to meet at an angle. However, if the
                    angle is less than 11 degrees, a Bevel join-style is used instead.
Round               The result is a circular arc with a diameter equal to the line-width, centered
                    on the joinpoint.
Bevel               The result is Butt endpoint styles, and then the triangular notch is filled.
      ]]></field>
      <field name="FillStyle"><![CDATA[
The fill-style defines the contents of the source for line, text, and fill requests. For all text and fill
requests (for example, PolyText8, PolyText16, PolyFillRectangle, FillPoly, and PolyFillArc)
as well as for line requests with line-style Solid, (for example, PolyLine, PolySegment,
PolyRectangle, PolyArc) and for the even dashes for line requests with line-style OnOffDash
or DoubleDash:
Solid                     Foreground
Tiled                     Tile
OpaqueStippled            A tile with the same width and height as stipple but with background
                          everywhere stipple has a zero and with foreground everywhere stipple
                          has a one
Stippled                  Foreground masked by stipple
For the odd dashes for line requests with line-style DoubleDash:
Solid                     Background
Tiled                     Same as for even dashes
OpaqueStippled            Same as for even dashes
Stippled                  Background masked by stipple
      ]]></field>
      <field name="FillRule"><![CDATA[
      ]]></field>
      <field name="Tile"><![CDATA[
The tile/stipple represents an infinite two-dimensional plane with the tile/stipple replicated in all
dimensions. When that plane is superimposed on the drawable for use in a graphics operation,
the upper-left corner of some instance of the tile/stipple is at the coordinates within the drawable
specified by the tile/stipple origin. The tile/stipple and clip origins are interpreted relative to the
origin of whatever destination drawable is specified in a graphics request.
The tile pixmap must have the same root and depth as the gcontext (or a Match error results).
The stipple pixmap must have depth one and must have the same root as the gcontext (or a
Match error results). For fill-style Stippled (but not fill-style
OpaqueStippled), the stipple pattern is tiled in a single plane and acts as an
additional clip mask to be ANDed with the clip-mask.
Any size pixmap can be used for tiling or stippling, although some sizes may be faster to use than
others.
      ]]></field>
      <field name="Stipple"><![CDATA[
The tile/stipple represents an infinite two-dimensional plane with the tile/stipple replicated in all
dimensions. When that plane is superimposed on the drawable for use in a graphics operation,
the upper-left corner of some instance of the tile/stipple is at the coordinates within the drawable
specified by the tile/stipple origin. The tile/stipple and clip origins are interpreted relative to the
origin of whatever destination drawable is specified in a graphics request.
The tile pixmap must have the same root and depth as the gcontext (or a Match error results).
The stipple pixmap must have depth one and must have the same root as the gcontext (or a
Match error results). For fill-style Stippled (but not fill-style
OpaqueStippled), the stipple pattern is tiled in a single plane and acts as an
additional clip mask to be ANDed with the clip-mask.
Any size pixmap can be used for tiling or stippling, although some sizes may be faster to use than
others.
      ]]></field>
      <field name="TileStippleOriginX"><![CDATA[
TODO
      ]]></field>
      <field name="TileStippleOriginY"><![CDATA[
TODO
      ]]></field>
      <field name="Font"><![CDATA[
Which font to use for the `ImageText8` and `ImageText16` requests.
      ]]></field>
      <field name="SubwindowMode"><![CDATA[
For ClipByChildren, both source and destination windows are additionally
clipped by all viewable InputOutput children. For IncludeInferiors, neither
source nor destination window is
clipped by inferiors. This will result in including subwindow contents in the source and drawing
through subwindow boundaries of the destination. The use of IncludeInferiors with a source or
destination window of one depth with mapped inferiors of differing depth is not illegal, but the
semantics is undefined by the core protocol.
      ]]></field>
      <field name="GraphicsExposures"><![CDATA[
Whether ExposureEvents should be generated (1) or not (0).

The default is 1.
      ]]></field>
      <field name="ClipOriginX"><![CDATA[
TODO
      ]]></field>
      <field name="ClipOriginY"><![CDATA[
TODO
      ]]></field>
      <field name="ClipMask"><![CDATA[
The clip-mask restricts writes to the destination drawable. Only pixels where the clip-mask has
bits set to 1 are drawn. Pixels are not drawn outside the area covered by the clip-mask or where
the clip-mask has bits set to 0. The clip-mask affects all graphics requests, but it does not clip
sources. The clip-mask origin is interpreted relative to the origin of whatever destination drawable is specified in a graphics request. If a pixmap is specified as the clip-mask, it must have
depth 1 and have the same root as the gcontext (or a Match error results). If clip-mask is None,
then pixels are always drawn, regardless of the clip origin. The clip-mask can also be set with the
SetClipRectangles request.
      ]]></field>
      <field name="DashOffset"><![CDATA[
TODO
      ]]></field>
      <field name="DashList"><![CDATA[
TODO
      ]]></field>
      <field name="ArcMode"><![CDATA[
TODO
      ]]></field>
    </doc>

  </enum>

  <!-- GC Function values -->
  <enum name="GX">
    <item name="clear">       <value>0</value></item>
    <item name="and">         <value>1</value></item>
    <item name="andReverse">  <value>2</value></item>
    <item name="copy">        <value>3</value></item>
    <item name="andInverted"> <value>4</value></item>
    <item name="noop">        <value>5</value></item>
    <item name="xor">         <value>6</value></item>
    <item name="or">          <value>7</value></item>
    <item name="nor">         <value>8</value></item>
    <item name="equiv">       <value>9</value></item>
    <item name="invert">      <value>10</value></item>
    <item name="orReverse">   <value>11</value></item>
    <item name="copyInverted"><value>12</value></item>
    <item name="orInverted">  <value>13</value></item>
    <item name="nand">        <value>14</value></item>
    <item name="set">         <value>15</value></item>
  </enum>

  <enum name="LineStyle">
    <item name="Solid">     <value>0</value></item>
    <item name="OnOffDash"> <value>1</value></item>
    <item name="DoubleDash"><value>2</value></item>
  </enum>

  <enum name="CapStyle">
    <item name="NotLast">   <value>0</value></item>
    <item name="Butt">      <value>1</value></item>
    <item name="Round">     <value>2</value></item>
    <item name="Projecting"><value>3</value></item>
  </enum>

  <enum name="JoinStyle">
    <item name="Miter">     <value>0</value></item>
    <item name="Round">     <value>1</value></item>
    <item name="Bevel">     <value>2</value></item>
  </enum>

  <enum name="FillStyle">
    <item name="Solid">         <value>0</value></item>
    <item name="Tiled">         <value>1</value></item>
    <item name="Stippled">      <value>2</value></item>
    <item name="OpaqueStippled"><value>3</value></item>
  </enum>

  <enum name="FillRule">
    <item name="EvenOdd"><value>0</value></item>
    <item name="Winding"><value>1</value></item>
  </enum>

  <enum name="SubwindowMode">
    <item name="ClipByChildren">  <value>0</value></item>
    <item name="IncludeInferiors"><value>1</value></item>
  </enum>

  <enum name="ArcMode">
    <item name="Chord">   <value>0</value></item>
    <item name="PieSlice"><value>1</value></item>
  </enum>

  <request name="CreateGC" opcode="55">
    <pad bytes="1" />
    <field type="GCONTEXT" name="cid" />
    <field type="DRAWABLE" name="drawable" />
    <valueparam value-mask-type="CARD32"
                value-mask-name="value_mask"
                value-list-name="value_list" />
    <doc>
      <brief>Creates a graphics context</brief>
      <description><![CDATA[
Creates a graphics context. The graphics context can be used with any drawable
that has the same root and depth as the specified drawable.
      ]]></description>
      <field name="cid"><![CDATA[
The ID with which you will refer to the graphics context, created by
`xcb_generate_id`.
      ]]></field>
      <field name="drawable"><![CDATA[
Drawable to get the root/depth from.
      ]]></field>
      <error type="Drawable"><![CDATA[
The specified `drawable` (Window or Pixmap) does not exist.
      ]]></error>
      <error type="Match"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Font"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Pixmap"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Value"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Alloc"><![CDATA[
The X server could not allocate the requested resources (no memory?).
      ]]></error>
      <see type="function" name="xcb_generate_id" />
    </doc>
  </request>

  <request name="ChangeGC" opcode="56">
    <pad bytes="1" />
    <field type="GCONTEXT" name="gc" />
    <valueparam value-mask-type="CARD32"
                value-mask-name="value_mask"
                value-list-name="value_list" />
    <doc>
      <brief>change graphics context components</brief>
      <description><![CDATA[
Changes the components specified by `value_mask` for the specified graphics context.
      ]]></description>
      <example><![CDATA[
/*
 * Changes the foreground color component of the specified graphics context.
 *
 */
void my_example(xcb_connection *conn, xcb_gcontext_t gc, uint32_t fg, uint32_t bg) {
    /* C99 allows us to use a compact way of changing a single component: */
    xcb_change_gc(conn, gc, XCB_GC_FOREGROUND, (uint32_t[]){ fg });

    /* The more explicit way. Beware that the order of values is important! */
    uint32_t mask = 0;
    mask |= XCB_GC_FOREGROUND;
    mask |= XCB_GC_BACKGROUND;

    uint32_t values[] = {
        fg,
        bg
    };
    xcb_change_gc(conn, gc, mask, values);
    xcb_flush(conn);
}
      ]]></example>
      <field name="gc"><![CDATA[
The graphics context to change.
      ]]></field>
      <!-- the enum documentation is good enough. -->
      <field name="value_mask" />
      <field name="value_list"><![CDATA[
Values for each of the components specified in the bitmask `value_mask`. The
order has to correspond to the order of possible `value_mask` bits. See the
example.
      ]]></field>
      <error type="Font"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="GC"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Match"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Pixmap"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Value"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Alloc"><![CDATA[
The X server could not allocate the requested resources (no memory?).
      ]]></error>
    </doc>
  </request>

  <request name="CopyGC" opcode="57">
    <pad bytes="1" />
    <field type="GCONTEXT" name="src_gc" />
    <field type="GCONTEXT" name="dst_gc" />
    <field type="CARD32" name="value_mask" mask="GC" />
  </request>

  <request name="SetDashes" opcode="58">
    <pad bytes="1" />
    <field type="GCONTEXT" name="gc" />
    <field type="CARD16" name="dash_offset" />
    <field type="CARD16" name="dashes_len" />
    <list type="CARD8" name="dashes">
      <fieldref>dashes_len</fieldref>
    </list>
  </request>

  <enum name="ClipOrdering">
    <item name="Unsorted"><value>0</value></item>
    <item name="YSorted"> <value>1</value></item>
    <item name="YXSorted"><value>2</value></item>
    <item name="YXBanded"><value>3</value></item>
  </enum>

  <request name="SetClipRectangles" opcode="59">
    <field type="BYTE" name="ordering" enum="ClipOrdering" />
    <field type="GCONTEXT" name="gc" />
    <field type="INT16" name="clip_x_origin" />
    <field type="INT16" name="clip_y_origin" />
    <list type="RECTANGLE" name="rectangles" />
  </request>

  <request name="FreeGC" opcode="60">
    <pad bytes="1" />
    <field type="GCONTEXT" name="gc" />
    <doc>
      <brief>Destroys a graphics context</brief>
      <description><![CDATA[
Destroys the specified `gc` and all associated storage.
      ]]></description>
      <field name="gc"><![CDATA[The graphics context to destroy.]]></field>
      <error type="GC"><![CDATA[
The specified graphics context does not exist.
      ]]></error>
    </doc>
  </request>

  <request name="ClearArea" opcode="61">
    <field type="BOOL" name="exposures" />
    <field type="WINDOW" name="window" />
    <field type="INT16" name="x" />
    <field type="INT16" name="y" />
    <field type="CARD16" name="width" />
    <field type="CARD16" name="height" />
  </request>

  <request name="CopyArea" opcode="62">
    <pad bytes="1" />
    <field type="DRAWABLE" name="src_drawable" />
    <field type="DRAWABLE" name="dst_drawable" />
    <field type="GCONTEXT" name="gc" />
    <field type="INT16" name="src_x" />
    <field type="INT16" name="src_y" />
    <field type="INT16" name="dst_x" />
    <field type="INT16" name="dst_y" />
    <field type="CARD16" name="width" />
    <field type="CARD16" name="height" />
    <doc>
      <brief>copy areas</brief>
      <description><![CDATA[
Copies the specified rectangle from `src_drawable` to `dst_drawable`.
      ]]></description>
      <field name="dst_drawable"><![CDATA[
The destination drawable (Window or Pixmap).
      ]]></field>
      <field name="src_drawable"><![CDATA[
The source drawable (Window or Pixmap).
      ]]></field>
      <field name="gc"><![CDATA[
The graphics context to use.
      ]]></field>
      <field name="src_x"><![CDATA[
The source X coordinate.
      ]]></field>
      <field name="src_y"><![CDATA[
The source Y coordinate.
      ]]></field>
      <field name="dst_x"><![CDATA[
The destination X coordinate.
      ]]></field>
      <field name="dst_y"><![CDATA[
The destination Y coordinate.
      ]]></field>
      <field name="width"><![CDATA[
The width of the area to copy (in pixels).
      ]]></field>
      <field name="height"><![CDATA[
The height of the area to copy (in pixels).
      ]]></field>
      <error type="Drawable"><![CDATA[
The specified `drawable` (Window or Pixmap) does not exist.
      ]]></error>
      <error type="GC"><![CDATA[
The specified graphics context does not exist.
      ]]></error>
      <error type="Match"><![CDATA[
`src_drawable` has a different root or depth than `dst_drawable`.
      ]]></error>
    </doc>
  </request>

  <request name="CopyPlane" opcode="63">
    <pad bytes="1" />
    <field type="DRAWABLE" name="src_drawable" />
    <field type="DRAWABLE" name="dst_drawable" />
    <field type="GCONTEXT" name="gc" />
    <field type="INT16" name="src_x" />
    <field type="INT16" name="src_y" />
    <field type="INT16" name="dst_x" />
    <field type="INT16" name="dst_y" />
    <field type="CARD16" name="width" />
    <field type="CARD16" name="height" />
    <field type="CARD32" name="bit_plane" />
  </request>
  
  <enum name="CoordMode">
    <item name="Origin">  <value>0</value></item>
    <item name="Previous"><value>1</value></item>
    <doc>
      <field name="Origin"><![CDATA[
Treats all coordinates as relative to the origin.
      ]]></field>
      <field name="Previous"><![CDATA[
Treats all coordinates after the first as relative to the previous coordinate.
      ]]></field>
    </doc>
  </enum>

  <!-- combine-adjacent doesn't work for mode==Relative -->
  <request name="PolyPoint" opcode="64">
    <field type="BYTE" name="coordinate_mode" enum="CoordMode" />
    <field type="DRAWABLE" name="drawable" />
    <field type="GCONTEXT" name="gc" />
    <list type="POINT" name="points" />
  </request>

  <request name="PolyLine" opcode="65" combine-adjacent="true">
    <field type="BYTE" name="coordinate_mode" enum="CoordMode" />
    <field type="DRAWABLE" name="drawable" />
    <field type="GCONTEXT" name="gc" />
    <list type="POINT" name="points" />
    <doc>
      <brief>draw lines</brief>
      <description><![CDATA[
Draws `points_len`-1 lines between each pair of points (point[i], point[i+1])
in the `points` array. The lines are drawn in the order listed in the array.
They join correctly at all intermediate points, and if the first and last
points coincide, the first and last lines also join correctly. For any given
line, a pixel is not drawn more than once. If thin (zero line-width) lines
intersect, the intersecting pixels are drawn multiple times. If wide lines
intersect, the intersecting pixels are drawn only once, as though the entire
request were a single, filled shape.
      ]]></description>
      <example><![CDATA[
/*
 * Draw a straight line.
 *
 */
void my_example(xcb_connection *conn, xcb_drawable_t drawable, xcb_gcontext_t gc) {
    xcb_poly_line(conn, XCB_COORD_MODE_ORIGIN, drawable, gc, 2,
                  (xcb_point_t[]) { {10, 10}, {100, 10} });
    xcb_flush(conn);
}
      ]]></example>
      <field name="drawable"><![CDATA[
The drawable to draw the line(s) on.
      ]]></field>
      <field name="gc"><![CDATA[
The graphics context to use.
      ]]></field>
      <field name="points_len"><![CDATA[
The number of `xcb_point_t` structures in `points`.
      ]]></field>
      <field name="points"><![CDATA[
An array of points.
      ]]></field>
      <!-- the enum doc is sufficient. -->
      <field name="coordinate_mode" />
      <error type="Drawable"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="GC"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Match"><![CDATA[
TODO: reasons?
      ]]></error>
      <error type="Value"><![CDATA[
TODO: reasons?
      ]]></error>
    </doc>
  </request>

  <struct name="SEGMENT">
    <field type="INT16" name="x1" />
    <field type="INT16" name="y1" />
    <field type="INT16" name="x2" />
    <field type="INT16" name="y2" />
  </struct>

  <request name="PolySegment" opcode="66" combine-adjacent="true">
    <pad bytes="1" />
    <field type="DRAWABLE" name="drawable" />
    <field type="GCONTEXT" name="gc" />
    <list type="SEGMENT" name="segments" />
    <doc>
      <brief>draw lines</brief>
      <description><![CDATA[
Draws multiple, unconnected lines. For each segment, a line is drawn between
(x1, y1) and (x2, y2). The lines are drawn in the order listed in the array of
`xcb_segment_t` structures and does not perform joining at coincident
endpoints. For any given line, a pixel is not drawn more than once. If lines
intersect, the intersecting pixels are drawn multiple times.

TODO: include the xcb_segment_t data structure

TODO: an example
      ]]></description>
      <field name="drawable"><![CDATA[
A drawable (Window or Pixmap) to draw on.
      ]]></field>
      <field name="gc"><![CDATA[
The graphics context to use.

TODO: document which attributes of a gc are used
      ]]></field>
      <field name="segments_len"><![CDATA[
The number of `xcb_segment_t` structures in `segments`.
      ]]></field>
      <field name="segments"><![CDATA[
An array of `xcb_segment_t` structures.
      ]]></field>
      <error type="Drawable"><![CDATA[
The specified `drawable` does not exist.
      ]]></error>
      <error type="GC"><![CDATA[
The specified `gc` does not exist.
      ]]></error>
      <error type="Match"><![CDATA[
TODO: reasons?
      ]]></error>
    </doc>
  </request>

  <request name="PolyRectangle" opcode="67" combine-adjacent="true">
    <pad bytes="1" />
    <field type="DRAWABLE" name="drawable" />
    <field type="GCONTEXT" name="gc" />
    <list type="RECTANGLE" name="rectangles" />
  </request>

  <!--
    The semantics of PolyArc change after the first arc: the GC's
    join style may be applied to successive arcs under some circumstances.
    So using combine-adjacent here is bad.
  -->
  <request name="PolyArc" opcode="68">
    <pad bytes="1" />
    <field type="DRAWABLE" name="drawable" />
    <field type="GCONTEXT" name="gc" />
    <list type="ARC" name="arcs" />
  </request>

  <enum name="PolyShape">
    <item name="Complex">  <value>0</value></item>
    <item name="Nonconvex"><value>1</value></item>
    <item name="Convex">   <value>2</value></item>
  </enum>

  <request name="FillPoly" opcode="69">
    <pad bytes="1" />
    <field type="DRAWABLE" name="drawable" />
    <field type="GCONTEXT" name="gc" />
    <field type="CARD8" name="shape" enum="PolyShape" />
    <field type="CARD8" name="coordinate_mode" enum="CoordMode" />
    <pad bytes="2" />
    <list type="POINT" name="points" />
  </request>

  <request name="PolyFillRectangle" opcode="70" combine-adjacent="true">
    <pad bytes="1" />
    <field type="DRAWABLE" name="drawable" />
    <field type="GCONTEXT" name="gc" />
    <list type="RECTANGLE" name="rectangles" />
    <doc>
      <brief>Fills rectangles</brief>
      <description><![CDATA[
Fills the specified rectangle(s) in the order listed in the array. For any
given rectangle, each pixel is not drawn more than once. If rectangles
intersect, the intersecting pixels are drawn multiple times.
      ]]></description>
      <field name="drawable"><![CDATA[
The drawable (Window or Pixmap) to draw on.
      ]]></field>
      <field name="gc"><![CDATA[
The graphics context to use.

The following graphics context components are used: function, plane-mask,
fill-style, subwindow-mode, clip-x-origin, clip-y-origin, and clip-mask.

The following graphics context mode-dependent components are used:
foreground, background, tile, stipple, tile-stipple-x-origin, and
tile-stipple-y-origin.
      ]]></field>
      <field name="rectangles_len"><![CDATA[
The number of `xcb_rectangle_t` structures in `rectangles`.
      ]]></field>
      <field name="rectangles"><![CDATA[
The rectangles to fill.
      ]]></field>
      <error type="Drawable"><![CDATA[
The specified `drawable` (Window or Pixmap) does not exist.
      ]]></error>
      <error type="GC"><![CDATA[
The specified graphics context does not exist.
      ]]></error>
      <error type="Match"><![CDATA[
TODO: reasons?
      ]]></error>
    </doc>
  </request>

  <request name="PolyFillArc" opcode="71" combine-adjacent="true">
    <pad bytes="1" />
    <field type="DRAWABLE" name="drawable" />
    <field type="GCONTEXT" name="gc" />
    <list type="ARC" name="arcs" />
  </request>
  
  <enum name="ImageFormat">
    <item name="XYBitmap"><value>0</value></item>
    <item name="XYPixmap"><value>1</value></item>
    <item name="ZPixmap"> <value>2</value></item>
  </enum>

  <request name="PutImage" opcode="72">
    <field type="CARD8" name="format" enum="ImageFormat" />
    <field type="DRAWABLE" name="drawable" />
    <field type="GCONTEXT" name="gc" />
    <field type="CARD16" name="width" />
    <field type="CARD16" name="height" />
    <field type="INT16" name="dst_x" />
    <field type="INT16" name="dst_y" />
    <field type="CARD8" name="left_pad" />
    <field type="CARD8" name="depth" />
    <pad bytes="2" />
    <list type="BYTE" name="data" />
  </request>

  <!-- FIXME: data array in reply will include padding, but ought not to. -->
  <request name="GetImage" opcode="73">
    <field type="CARD8" name="format" enum="ImageFormat" />
    <field type="DRAWABLE" name="drawable" />
    <field type="INT16" name="x" />
    <field type="INT16" name="y" />
    <field type="CARD16" name="width" />
    <field type="CARD16" name="height" />
    <field type="CARD32" name="plane_mask" />
    <reply>
      <field type="CARD8" name="depth" />
      <field type="VISUALID" name="visual" />
      <pad bytes="20" />
      <list type="BYTE" name="data">
        <op op="*">
          <fieldref>length</fieldref>
          <value>4</value>
        </op>
      </list>
    </reply>
  </request>

  <request name="PolyText8" opcode="74">
    <pad bytes="1" />
    <field type="DRAWABLE" name="drawable" />
    <field type="GCONTEXT" name="gc" />
    <field type="INT16" name="x" />
    <field type="INT16" name="y" />
    <list type="BYTE" name="items" />
  </request>

  <request name="PolyText16" opcode="75">
    <pad bytes="1" />
    <field type="DRAWABLE" name="drawable" />
    <field type="GCONTEXT" name="gc" />
    <field type="INT16" name="x" />
    <field type="INT16" name="y" />
    <list type="BYTE" name="items" />
  </request>

  <request name="ImageText8" opcode="76">
    <field type="BYTE" name="string_len" />
    <field type="DRAWABLE" name="drawable" />
    <field type="GCONTEXT" name="gc" />
    <field type="INT16" name="x" />
    <field type="INT16" name="y" />
    <list type="char" name="string">
      <fieldref>string_len</fieldref>
    </list>
    <doc>
      <brief>Draws text</brief>
      <description><![CDATA[
Fills the destination rectangle with the background pixel from `gc`, then
paints the text with the foreground pixel from `gc`. The upper-left corner of
the filled rectangle is at [x, y - font-ascent]. The width is overall-width,
the height is font-ascent + font-descent. The overall-width, font-ascent and
font-descent are as returned by `xcb_query_text_extents` (TODO).

Note that using X core fonts is deprecated (but still supported) in favor of
client-side rendering using Xft.
      ]]></description>
      <field name="drawable"><![CDATA[
The drawable (Window or Pixmap) to draw text on.
      ]]></field>
      <field name="string_len"><![CDATA[
The length of the `string`. Note that this parameter limited by 255 due to
using 8 bits!
      ]]></field>
      <field name="string"><![CDATA[
The string to draw. Only the first 255 characters are relevant due to the data
type of `string_len`.
      ]]></field>
      <field name="x"><![CDATA[
The x coordinate of the first character, relative to the origin of `drawable`.
      ]]></field>
      <field name="y"><![CDATA[
The y coordinate of the first character, relative to the origin of `drawable`.
      ]]></field>
      <field name="gc"><![CDATA[
The graphics context to use.

The following graphics context components are used: plane-mask, foreground,
background, font, subwindow-mode, clip-x-origin, clip-y-origin, and clip-mask.
      ]]></field>
      <error type="Drawable"><![CDATA[
The specified `drawable` (Window or Pixmap) does not exist.
      ]]></error>
      <error type="GC"><![CDATA[
The specified graphics context does not exist.
      ]]></error>
      <error type="Match"><![CDATA[
TODO: reasons?
      ]]></error>
      <see type="request" name="ImageText16" />
    </doc>
  </request>

  <request name="ImageText16" opcode="77">
    <field type="BYTE" name="string_len" />
    <field type="DRAWABLE" name="drawable" />
    <field type="GCONTEXT" name="gc" />
    <field type="INT16" name="x" />
    <field type="INT16" name="y" />
    <list type="CHAR2B" name="string">
      <fieldref>string_len</fieldref>
    </list>
    <doc>
      <brief>Draws text</brief>
      <description><![CDATA[
Fills the destination rectangle with the background pixel from `gc`, then
paints the text with the foreground pixel from `gc`. The upper-left corner of
the filled rectangle is at [x, y - font-ascent]. The width is overall-width,
the height is font-ascent + font-descent. The overall-width, font-ascent and
font-descent are as returned by `xcb_query_text_extents` (TODO).

Note that using X core fonts is deprecated (but still supported) in favor of
client-side rendering using Xft.
      ]]></description>
      <field name="drawable"><![CDATA[
The drawable (Window or Pixmap) to draw text on.
      ]]></field>
      <field name="string_len"><![CDATA[
The length of the `string` in characters. Note that this parameter limited by
255 due to using 8 bits!
      ]]></field>
      <field name="string"><![CDATA[
The string to draw. Only the first 255 characters are relevant due to the data
type of `string_len`. Every character uses 2 bytes (hence the 16 in this
request's name).
      ]]></field>
      <field name="x"><![CDATA[
The x coordinate of the first character, relative to the origin of `drawable`.
      ]]></field>
      <field name="y"><![CDATA[
The y coordinate of the first character, relative to the origin of `drawable`.
      ]]></field>
      <field name="gc"><![CDATA[
The graphics context to use.

The following graphics context components are used: plane-mask, foreground,
background, font, subwindow-mode, clip-x-origin, clip-y-origin, and clip-mask.
      ]]></field>
      <error type="Drawable"><![CDATA[
The specified `drawable` (Window or Pixmap) does not exist.
      ]]></error>
      <error type="GC"><![CDATA[
The specified graphics context does not exist.
      ]]></error>
      <error type="Match"><![CDATA[
TODO: reasons?
      ]]></error>
      <see type="request" name="ImageText8" />
    </doc>
  </request>

  <enum name= "ColormapAlloc">
    <item name="None"><value>0</value></item>
    <item name="All"> <value>1</value></item>
  </enum>

  <request name="CreateColormap" opcode="78">
    <field type="BYTE" name="alloc" enum="ColormapAlloc" />
    <field type="COLORMAP" name="mid" />
    <field type="WINDOW" name="window" />
    <field type="VISUALID" name="visual" />
  </request>

  <request name="FreeColormap" opcode="79">
    <pad bytes="1" />
    <field type="COLORMAP" name="cmap" />
  </request>

  <request name="CopyColormapAndFree" opcode="80">
    <pad bytes="1" />
    <field type="COLORMAP" name="mid" />
    <field type="COLORMAP" name="src_cmap" />
  </request>

  <request name="InstallColormap" opcode="81">
    <pad bytes="1" />
    <field type="COLORMAP" name="cmap" />
  </request>

  <request name="UninstallColormap" opcode="82">
    <pad bytes="1" />
    <field type="COLORMAP" name="cmap" />
  </request>

  <request name="ListInstalledColormaps" opcode="83">
    <pad bytes="1" />
    <field type="WINDOW" name="window" />
    <reply>
      <pad bytes="1" />
      <field type="CARD16" name="cmaps_len" />
      <pad bytes="22" />
      <list type="COLORMAP" name="cmaps">
        <fieldref>cmaps_len</fieldref>
      </list>
    </reply>
  </request>

  <request name="AllocColor" opcode="84">
    <pad bytes="1" />
    <field type="COLORMAP" name="cmap" />
    <field type="CARD16" name="red" />
    <field type="CARD16" name="green" />
    <field type="CARD16" name="blue" />
    <pad bytes="2" />
    <reply>
      <pad bytes="1" />
      <field type="CARD16" name="red" />
      <field type="CARD16" name="green" />
      <field type="CARD16" name="blue" />
      <pad bytes="2" />
      <field type="CARD32" name="pixel" />
    </reply>
    <doc>
      <brief>Allocate a color</brief>
      <description><![CDATA[
Allocates a read-only colormap entry corresponding to the closest RGB value
supported by the hardware. If you are using TrueColor, you can take a shortcut
and directly calculate the color pixel value to avoid the round trip. But, for
example, on 16-bit color setups (VNC), you can easily get the closest supported
RGB value to the RGB value you are specifying.
      ]]></description>
      <field name="cmap"><![CDATA[
TODO
      ]]></field>
      <field name="red"><![CDATA[
The red value of your color.
      ]]></field>
      <field name="green"><![CDATA[
The green value of your color.
      ]]></field>
      <field name="blue"><![CDATA[
The blue value of your color.
      ]]></field>
      <error type="Colormap"><![CDATA[
The specified colormap `cmap` does not exist.
      ]]></error>
    </doc>
  </request>

  <request name="AllocNamedColor" opcode="85">
    <pad bytes="1" />
    <field type="COLORMAP" name="cmap" />
    <field type="CARD16" name="name_len" />
    <pad bytes="2" />
    <list type="char" name="name">
      <fieldref>name_len</fieldref>
    </list>
    <reply>
      <pad bytes="1" />
      <field type="CARD32" name="pixel" />
      <field type="CARD16" name="exact_red" />
      <field type="CARD16" name="exact_green" />
      <field type="CARD16" name="exact_blue" />
      <field type="CARD16" name="visual_red" />
      <field type="CARD16" name="visual_green" />
      <field type="CARD16" name="visual_blue" />
    </reply>
  </request>

  <request name="AllocColorCells" opcode="86">
    <field type="BOOL" name="contiguous" />
    <field type="COLORMAP" name="cmap" />
    <field type="CARD16" name="colors" />
    <field type="CARD16" name="planes" />
    <reply>
      <pad bytes="1" />
      <field type="CARD16" name="pixels_len" />
      <field type="CARD16" name="masks_len" />
      <pad bytes="20" />
      <list type="CARD32" name="pixels">
        <fieldref>pixels_len</fieldref>
      </list>
      <list type="CARD32" name="masks">
        <fieldref>masks_len</fieldref>
      </list>
    </reply>
  </request>

  <request name="AllocColorPlanes" opcode="87">
    <field type="BOOL" name="contiguous" />
    <field type="COLORMAP" name="cmap" />
    <field type="CARD16" name="colors" />
    <field type="CARD16" name="reds" />
    <field type="CARD16" name="greens" />
    <field type="CARD16" name="blues" />
    <reply>
      <pad bytes="1" />
      <field type="CARD16" name="pixels_len" />
      <pad bytes="2" />
      <field type="CARD32" name="red_mask" />
      <field type="CARD32" name="green_mask" />
      <field type="CARD32" name="blue_mask" />
      <pad bytes="8" />
      <list type="CARD32" name="pixels">
        <fieldref>pixels_len</fieldref>
      </list>
    </reply>
  </request>

  <request name="FreeColors" opcode="88">
    <pad bytes="1" />
    <field type="COLORMAP" name="cmap" />
    <field type="CARD32" name="plane_mask" />
    <list type="CARD32" name="pixels" />
  </request>

  <enum name="ColorFlag">
    <item name="Red">  <bit>0</bit></item>
    <item name="Green"><bit>1</bit></item>
    <item name="Blue"> <bit>2</bit></item>
  </enum>

  <struct name="COLORITEM">
    <field type="CARD32" name="pixel" />
    <field type="CARD16" name="red" />
    <field type="CARD16" name="green" />
    <field type="CARD16" name="blue" />
    <field type="BYTE" name="flags" mask="ColorFlag" />
    <pad bytes="1" />
  </struct>
  
  <request name="StoreColors" opcode="89" combine-adjacent="true">
    <pad bytes="1" />
    <field type="COLORMAP" name="cmap" />
    <list type="COLORITEM" name="items" />
  </request>

  <request name="StoreNamedColor" opcode="90">
    <field type="CARD8" name="flags" mask="ColorFlag" />
    <field type="COLORMAP" name="cmap" />
    <field type="CARD32" name="pixel" />
    <field type="CARD16" name="name_len" />
    <pad bytes="2" />
    <list type="char" name="name">
      <fieldref>name_len</fieldref>
    </list>
  </request>

  <struct name="RGB">
    <field type="CARD16" name="red" />
    <field type="CARD16" name="green" />
    <field type="CARD16" name="blue" />
    <pad bytes="2" />
  </struct>

  <request name="QueryColors" opcode="91">
    <pad bytes="1" />
    <field type="COLORMAP" name="cmap" />
    <list type="CARD32" name="pixels" />
    <reply>
      <pad bytes="1" />
      <field type="CARD16" name="colors_len" />
      <pad bytes="22" />
      <list type="RGB" name="colors">
        <fieldref>colors_len</fieldref>
      </list>
    </reply>
  </request>

  <request name="LookupColor" opcode="92">
    <pad bytes="1" />
    <field type="COLORMAP" name="cmap" />
    <field type="CARD16" name="name_len" />
    <pad bytes="2" />
    <list type="char" name="name">
      <fieldref>name_len</fieldref>
    </list>
    <reply>
      <pad bytes="1" />
      <field type="CARD16" name="exact_red" />
      <field type="CARD16" name="exact_green" />
      <field type="CARD16" name="exact_blue" />
      <field type="CARD16" name="visual_red" />
      <field type="CARD16" name="visual_green" />
      <field type="CARD16" name="visual_blue" />
    </reply>
  </request>

  <enum name="Pixmap">
    <item name="None"> <value>0</value></item>
  </enum>

  <request name="CreateCursor" opcode="93">
    <pad bytes="1" />
    <field type="CURSOR" name="cid" />
    <field type="PIXMAP" name="source" />
    <field type="PIXMAP" name="mask" altenum="Pixmap" />
    <field type="CARD16" name="fore_red" />
    <field type="CARD16" name="fore_green" />
    <field type="CARD16" name="fore_blue" />
    <field type="CARD16" name="back_red" />
    <field type="CARD16" name="back_green" />
    <field type="CARD16" name="back_blue" />
    <field type="CARD16" name="x" />
    <field type="CARD16" name="y" />
  </request>

  <enum name="Font">
    <item name="None"> <value>0</value></item>
  </enum>

  <request name="CreateGlyphCursor" opcode="94">
    <pad bytes="1" />
    <field type="CURSOR" name="cid" />
    <field type="FONT" name="source_font" />
    <field type="FONT" name="mask_font" altenum="Font" />
    <field type="CARD16" name="source_char" />
    <field type="CARD16" name="mask_char" />
    <field type="CARD16" name="fore_red" />
    <field type="CARD16" name="fore_green" />
    <field type="CARD16" name="fore_blue" />
    <field type="CARD16" name="back_red" />
    <field type="CARD16" name="back_green" />
    <field type="CARD16" name="back_blue" />
    <doc>
      <brief>create cursor</brief>
      <description><![CDATA[
Creates a cursor from a font glyph. X provides a set of standard cursor shapes
in a special font named cursor. Applications are encouraged to use this
interface for their cursors because the font can be customized for the
individual display type.

All pixels which are set to 1 in the source will use the foreground color (as
specified by `fore_red`, `fore_green` and `fore_blue`). All pixels set to 0
will use the background color (as specified by `back_red`, `back_green` and
`back_blue`).
      ]]></description>
      <field name="cid"><![CDATA[
The ID with which you will refer to the cursor, created by `xcb_generate_id`.
      ]]></field>
      <field name="source_font"><![CDATA[
In which font to look for the cursor glyph.
      ]]></field>
      <field name="mask_font"><![CDATA[
In which font to look for the mask glyph.
      ]]></field>
      <field name="source_char"><![CDATA[
The glyph of `source_font` to use.
      ]]></field>
      <field name="mask_char"><![CDATA[
The glyph of `mask_font` to use as a mask: Pixels which are set to 1 define
which source pixels are displayed. All pixels which are set to 0 are not
displayed.
      ]]></field>
      <field name="fore_red"><![CDATA[
The red value of the foreground color.
      ]]></field>
      <field name="fore_green"><![CDATA[
The green value of the foreground color.
      ]]></field>
      <field name="fore_blue"><![CDATA[
The blue value of the foreground color.
      ]]></field>
      <field name="back_red"><![CDATA[
The red value of the background color.
      ]]></field>
      <field name="back_green"><![CDATA[
The green value of the background color.
      ]]></field>
      <field name="back_blue"><![CDATA[
The blue value of the background color.
      ]]></field>
      <error type="Alloc"><![CDATA[
The X server could not allocate the requested resources (no memory?).
      ]]></error>
      <error type="Font"><![CDATA[
The specified `source_font` or `mask_font` does not exist.
      ]]></error>
      <error type="Value"><![CDATA[
Either `source_char` or `mask_char` are not defined in `source_font` or `mask_font`, respectively.
      ]]></error>
      <!-- TODO: example -->
    </doc>
  </request>

  <request name="FreeCursor" opcode="95">
    <pad bytes="1" />
    <field type="CURSOR" name="cursor" />
    <doc>
      <brief>Deletes a cursor</brief>
      <description><![CDATA[
Deletes the association between the cursor resource ID and the specified
cursor. The cursor is freed when no other resource references it.
      ]]></description>
      <field name="cursor"><![CDATA[The cursor to destroy.]]></field>
      <error type="Cursor"><![CDATA[
The specified cursor does not exist.
      ]]></error>
    </doc>

  </request>

  <request name="RecolorCursor" opcode="96">
    <pad bytes="1" />
    <field type="CURSOR" name="cursor" />
    <field type="CARD16" name="fore_red" />
    <field type="CARD16" name="fore_green" />
    <field type="CARD16" name="fore_blue" />
    <field type="CARD16" name="back_red" />
    <field type="CARD16" name="back_green" />
    <field type="CARD16" name="back_blue" />
  </request>

  <enum name="QueryShapeOf">
    <item name="LargestCursor"> <value>0</value></item>
    <item name="FastestTile">   <value>1</value></item>
    <item name="FastestStipple"><value>2</value></item>    
  </enum>

  <request name="QueryBestSize" opcode="97">
    <field type="CARD8" name="class" enum="QueryShapeOf" />
    <field type="DRAWABLE" name="drawable" />
    <field type="CARD16" name="width" />
    <field type="CARD16" name="height" />
    <reply>
      <pad bytes="1" />
      <field type="CARD16" name="width" />
      <field type="CARD16" name="height" />
    </reply>
  </request>

  <request name="QueryExtension" opcode="98">
    <pad bytes="1" />
    <field type="CARD16" name="name_len" />
    <pad bytes="2" />
    <list type="char" name="name">
      <fieldref>name_len</fieldref>
    </list>
    <reply>
      <pad bytes="1" />
      <field type="BOOL" name="present" />
      <field type="CARD8" name="major_opcode" />
      <field type="CARD8" name="first_event" />
      <field type="CARD8" name="first_error" />
      <doc>
        <field name="present"><![CDATA[
Whether the extension is present on this X11 server.
        ]]></field>
        <field name="major_opcode"><![CDATA[
The major opcode for requests.
        ]]></field>
        <field name="first_event"><![CDATA[
The first event code, if any.
        ]]></field>
        <field name="first_error"><![CDATA[
The first error code, if any.
        ]]></field>
      </doc>
    </reply>
    <doc>
      <brief>check if extension is present</brief>
      <description><![CDATA[
Determines if the specified extension is present on this X11 server.

Every extension has a unique `major_opcode` to identify requests, the minor
opcodes and request formats are extension-specific. If the extension provides
events and errors, the `first_event` and `first_error` fields in the reply are
set accordingly.

There should rarely be a need to use this request directly, XCB provides the
`xcb_get_extension_data` function instead.
      ]]></description>
      <field name="name_len"><![CDATA[
The length of `name` in bytes.
      ]]></field>
      <field name="name"><![CDATA[
The name of the extension to query, for example "RANDR". This is case
sensitive!
      ]]></field>
      <see type="program" name="xdpyinfo" />
      <see type="function" name="xcb_get_extension_data" />
    </doc>
  </request>

  <request name="ListExtensions" opcode="99">
    <reply>
      <field type="CARD8" name="names_len" />
      <pad bytes="24" />
      <list type="STR" name="names">
        <fieldref>names_len</fieldref>
      </list>
    </reply>
  </request>

  <request name="ChangeKeyboardMapping" opcode="100">
    <field type="CARD8" name="keycode_count" />
    <field type="KEYCODE" name="first_keycode" />
    <field type="CARD8" name="keysyms_per_keycode" />
    <pad bytes="2" />
    <list type="KEYSYM" name="keysyms">
      <op op="*">
        <fieldref>keycode_count</fieldref>
        <fieldref>keysyms_per_keycode</fieldref>
      </op>
    </list>
  </request>

  <request name="GetKeyboardMapping" opcode="101">
    <pad bytes="1" />
    <field type="KEYCODE" name="first_keycode" />
    <field type="CARD8" name="count" />
    <reply>
      <field type="BYTE" name="keysyms_per_keycode" />
      <pad bytes="24" />
      <list type="KEYSYM" name="keysyms">
        <fieldref>length</fieldref>
      </list>
    </reply>
  </request>

  <enum name="KB">
    <item name="KeyClickPercent"><bit>0</bit></item>
    <item name="BellPercent">    <bit>1</bit></item>
    <item name="BellPitch">      <bit>2</bit></item>
    <item name="BellDuration">   <bit>3</bit></item>
    <item name="Led">            <bit>4</bit></item>
    <item name="LedMode">        <bit>5</bit></item>
    <item name="Key">            <bit>6</bit></item>
    <item name="AutoRepeatMode"> <bit>7</bit></item>
  </enum>

  <enum name="LedMode">
    <item name="Off"><value>0</value></item>
    <item name="On"> <value>1</value></item>
  </enum>

  <enum name="AutoRepeatMode">
    <item name="Off">    <value>0</value></item>
    <item name="On">     <value>1</value></item>
    <item name="Default"><value>2</value></item>
  </enum>

  <request name="ChangeKeyboardControl" opcode="102">
    <pad bytes="1" />
    <valueparam value-mask-type="CARD32"
                value-mask-name="value_mask"
                value-list-name="value_list" />
  </request>

  <request name="GetKeyboardControl" opcode="103">
    <reply>
      <field type="BYTE" name="global_auto_repeat" enum="AutoRepeatMode" />
      <field type="CARD32" name="led_mask" />
      <field type="CARD8" name="key_click_percent" />
      <field type="CARD8" name="bell_percent" />
      <field type="CARD16" name="bell_pitch" />
      <field type="CARD16" name="bell_duration" />
      <pad bytes="2" />
      <list type="CARD8" name="auto_repeats"><value>32</value></list>
    </reply>
  </request>

  <request name="Bell" opcode="104">
    <field type="INT8" name="percent" />
  </request>

  <request name="ChangePointerControl" opcode="105">
    <pad bytes="1" />
    <field type="INT16" name="acceleration_numerator" />
    <field type="INT16" name="acceleration_denominator" />
    <field type="INT16" name="threshold" />
    <field type="BOOL" name="do_acceleration" />
    <field type="BOOL" name="do_threshold" />
  </request>

  <request name="GetPointerControl" opcode="106">
    <reply>
      <pad bytes="1" />
      <field type="CARD16" name="acceleration_numerator" />
      <field type="CARD16" name="acceleration_denominator" />
      <field type="CARD16" name="threshold" />
      <pad bytes="18" />
    </reply>
  </request>

  <!-- Screen saver timeout and interval can be set to -1 to restore defaults
       and set to 0 to disable the screen saver. -->

  <enum name="Blanking">
    <item name="NotPreferred"><value>0</value></item>
    <item name="Preferred">   <value>1</value></item>
    <item name="Default">     <value>2</value></item>
  </enum>

  <enum name="Exposures">
    <item name="NotAllowed"><value>0</value></item>
    <item name="Allowed">   <value>1</value></item>
    <item name="Default">   <value>2</value></item>
  </enum>

  <request name="SetScreenSaver" opcode="107">
    <pad bytes="1" />
    <field type="INT16" name="timeout" />
    <field type="INT16" name="interval" />
    <field type="CARD8" name="prefer_blanking" enum="Blanking" />
    <field type="CARD8" name="allow_exposures" enum="Exposures" />
  </request>

  <request name="GetScreenSaver" opcode="108">
    <reply>
      <pad bytes="1" />
      <field type="CARD16" name="timeout" />
      <field type="CARD16" name="interval" />
      <field type="BYTE" name="prefer_blanking" enum="Blanking" />
      <field type="BYTE" name="allow_exposures" enum="Exposures" />
      <pad bytes="18" />
    </reply>
  </request>

  <enum name="HostMode">
    <item name="Insert"><value>0</value></item>
    <item name="Delete"><value>1</value></item>
  </enum>

  <!-- also used and extended for Xau authentication -->
  <enum name="Family">
    <item name="Internet">         <value>0</value></item>
    <item name="DECnet">           <value>1</value></item>
    <item name="Chaos">            <value>2</value></item>
    <item name="ServerInterpreted"><value>5</value></item>
    <item name="Internet6">        <value>6</value></item>
  </enum>

  <request name="ChangeHosts" opcode="109">
    <field type="CARD8" name="mode" enum="HostMode" />
    <field type="CARD8" name="family" enum="Family" />
    <pad bytes="1" />
    <field type="CARD16" name="address_len" />
    <list type="BYTE" name="address">
      <fieldref>address_len</fieldref>
    </list>
  </request>

  <struct name="HOST">
    <field type="CARD8" name="family" enum="Family" />
    <pad bytes="1" />
    <field type="CARD16" name="address_len" />
    <list type="BYTE" name="address">
      <fieldref>address_len</fieldref>
    </list>
  </struct>

  <request name="ListHosts" opcode="110">
    <reply>
      <field type="BYTE" name="mode" enum="AccessControl" />
      <field type="CARD16" name="hosts_len" />
      <pad bytes="22" />
      <list type="HOST" name="hosts">
        <fieldref>hosts_len</fieldref>
      </list>
    </reply>
  </request>

  <enum name="AccessControl">
    <item name="Disable"><value>0</value></item>
    <item name="Enable"> <value>1</value></item>
  </enum>

  <request name="SetAccessControl" opcode="111">
    <field type="CARD8" name="mode" enum="AccessControl" />
  </request>

  <enum name="CloseDown">
    <item name="DestroyAll">     <value>0</value></item>
    <item name="RetainPermanent"><value>1</value></item>
    <item name="RetainTemporary"><value>2</value></item>
  </enum>

  <request name="SetCloseDownMode" opcode="112">
    <field type="CARD8" name="mode" enum="CloseDown" />
  </request>

  <enum name="Kill">
    <item name="AllTemporary"><value>0</value></item>
  </enum>

  <request name="KillClient" opcode="113">
    <pad bytes="1" />
    <field type="CARD32" name="resource" altenum="Kill" />
    <doc>
      <brief>kills a client</brief>
      <description><![CDATA[
Forces a close down of the client that created the specified `resource`.
      ]]></description>
      <field name="resource"><![CDATA[
Any resource belonging to the client (for example a Window), used to identify
the client connection.

The special value of `XCB_KILL_ALL_TEMPORARY`, the resources of all clients
that have terminated in `RetainTemporary` (TODO) are destroyed.
      ]]></field>
      <error type="Value"><![CDATA[
The specified `resource` does not exist.
      ]]></error>
      <see type="program" name="xkill" />
    </doc>

  </request>

  <request name="RotateProperties" opcode="114">
    <pad bytes="1" />
    <field type="WINDOW" name="window" />
    <field type="CARD16" name="atoms_len" />
    <field type="INT16" name="delta" />
    <list type="ATOM" name="atoms">
      <fieldref>atoms_len</fieldref>
    </list>
  </request>

  <enum name="ScreenSaver">
    <item name="Reset"> <value>0</value></item>
    <item name="Active"><value>1</value></item>
  </enum>

  <request name="ForceScreenSaver" opcode="115">
    <field type="CARD8" name="mode" enum="ScreenSaver" />
  </request>

  <!-- Reply from SetPointerMapping or SetModifierMapping -->
  <enum name="MappingStatus">
    <item name="Success"><value>0</value></item>
    <item name="Busy">   <value>1</value></item>
    <item name="Failure"><value>2</value></item>
  </enum>

  <request name="SetPointerMapping" opcode="116">
    <field type="CARD8" name="map_len" />
    <list type="CARD8" name="map">
      <fieldref>map_len</fieldref>
    </list>
    <reply>
      <field type="BYTE" name="status" enum="MappingStatus" />
    </reply>
  </request>

  <request name="GetPointerMapping" opcode="117">
    <reply>
      <field type="CARD8" name="map_len" />
      <pad bytes="24" />
      <list type="CARD8" name="map">
        <fieldref>map_len</fieldref>
      </list>
    </reply>
  </request>
  
  <enum name="MapIndex">
    <item name="Shift">  <value>0</value></item>
    <item name="Lock">   <value>1</value></item>
    <item name="Control"><value>2</value></item>
    <item name="1">      <value>3</value></item>
    <item name="2">      <value>4</value></item>
    <item name="3">      <value>5</value></item>
    <item name="4">      <value>6</value></item>
    <item name="5">      <value>7</value></item>
  </enum>

  <request name="SetModifierMapping" opcode="118">
    <field type="CARD8" name="keycodes_per_modifier" />
    <list type="KEYCODE" name="keycodes">
      <op op="*">
        <fieldref>keycodes_per_modifier</fieldref>
        <value>8</value>
      </op>
    </list>
    <reply>
      <field type="BYTE" name="status" enum="MappingStatus" />
    </reply>
  </request>

  <request name="GetModifierMapping" opcode="119">
    <reply>
      <field type="CARD8" name="keycodes_per_modifier" />
      <pad bytes="24" />
      <list type="KEYCODE" name="keycodes">
        <op op="*">
          <fieldref>keycodes_per_modifier</fieldref>
          <value>8</value>
        </op>
      </list>
    </reply>
  </request>

  <!--
    FIXME: NoOperation should allow specifying payload length
    but geez, malloc()ing a 262140 byte buffer just so I have something
    to hand to write(2) seems silly...!
  -->
  <request name="NoOperation" opcode="127" />

</xcb>
