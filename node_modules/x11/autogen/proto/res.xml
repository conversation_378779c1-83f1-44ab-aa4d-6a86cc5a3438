<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright (C) 2006 <PERSON>b
All Rights Reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

Except as contained in this notice, the names of the authors or their
institutions shall not be used in advertising or otherwise to promote the
sale, use or other dealings in this Software without prior written
authorization from the authors.
-->
<xcb header="res" extension-xname="X-Resource" extension-name="Res"
    major-version="1" minor-version="0">
    <import>xproto</import>

    <struct name="Client">
        <field type="CARD32" name="resource_base" />
        <field type="CARD32" name="resource_mask" />
    </struct>

    <struct name="Type">
        <field type="ATOM" name="resource_type" />
        <field type="CARD32" name="count" />
    </struct>

    <request name="QueryVersion" opcode="0">
        <field type="CARD8" name="client_major" />
        <field type="CARD8" name="client_minor" />
        <reply>
            <pad bytes="1" />
            <field type="CARD16" name="server_major" />
            <field type="CARD16" name="server_minor" />
        </reply>
    </request>

    <request name="QueryClients" opcode="1">
        <reply>
            <pad bytes="1" />
            <field type="CARD32" name="num_clients" />
            <pad bytes="20" />
            <list type="Client" name="clients">
                <fieldref>num_clients</fieldref>
            </list>
        </reply>
    </request>

    <request name="QueryClientResources" opcode="2">
        <field type="CARD32" name="xid" />
        <reply>
            <pad bytes="1" />
            <field type="CARD32" name="num_types" />
            <pad bytes="20" />
            <list type="Type" name="types">
                <fieldref>num_types</fieldref>
            </list>
        </reply>
    </request>

    <request name="QueryClientPixmapBytes" opcode="3">
        <field type="CARD32" name="xid" />
        <reply>
            <pad bytes="1" />
            <field type="CARD32" name="bytes" />
            <field type="CARD32" name="bytes_overflow" />
        </reply>
    </request>
</xcb>
