<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright (C) 2004 <PERSON>.  All Rights Reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

Except as contained in this notice, the names of the authors or their
institutions shall not be used in advertising or otherwise to promote the
sale, use or other dealings in this Software without prior written
authorization from the authors.
-->
<!-- This file describes version 0.3 of COMPOSITE. -->
<xcb header="composite" extension-xname="Composite" extension-name="Composite"
    major-version="0" minor-version="3">

  <import>xproto</import>
  <import>xfixes</import>

  <enum name="Redirect">
    <item name="Automatic" />
    <item name="Manual" />
  </enum>

  <request name="QueryVersion" opcode="0">
    <field type="CARD32" name="client_major_version" />
    <field type="CARD32" name="client_minor_version" />
    <reply>
      <pad bytes="1" />
      <field type="CARD32" name="major_version" />
      <field type="CARD32" name="minor_version" />
      <pad bytes="16" />
    </reply>
  </request>

  <request name="RedirectWindow" opcode="1">
    <field type="WINDOW" name="window" />
    <field type="CARD8" name="update" enum="Redirect" />
    <pad bytes="3" />
  </request>

  <request name="RedirectSubwindows" opcode="2">
    <field type="WINDOW" name="window" />
    <field type="CARD8" name="update" enum="Redirect" />
    <pad bytes="3" />
  </request>

  <request name="UnredirectWindow" opcode="3">
    <field type="WINDOW" name="window" />
    <field type="CARD8" name="update" enum="Redirect" />
    <pad bytes="3" />
  </request>

  <request name="UnredirectSubwindows" opcode="4">
    <field type="WINDOW" name="window" />
    <field type="CARD8" name="update" enum="Redirect" />
    <pad bytes="3" />
  </request>

  <request name="CreateRegionFromBorderClip" opcode="5">
    <field type="REGION" name="region" />
    <field type="WINDOW" name="window" />
  </request>

  <request name="NameWindowPixmap" opcode="6">
    <field type="WINDOW" name="window" />
    <field type="PIXMAP" name="pixmap" />
  </request>
  
  <!-- new in version 0.3 -->
  
  <request name="GetOverlayWindow" opcode="7">
    <field type="WINDOW" name="window" />
    <reply>
      <pad bytes="1" />
      <field type="WINDOW" name="overlay_win" />
      <pad bytes="20" />
    </reply>
  </request>

  <request name="ReleaseOverlayWindow" opcode="8">
    <field type="WINDOW" name="window" />
  </request>
</xcb>
