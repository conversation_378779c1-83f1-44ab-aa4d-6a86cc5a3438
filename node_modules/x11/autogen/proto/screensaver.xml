<!--

Copyright (C) 2005 <PERSON>.
All Rights Reserved.

Permission is hereby granted, free of charge, to any person ob/Sintaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

Except as contained in this notice, the names of the authors or their
institutions shall not be used in advertising or otherwise to promote the
sale, use or other dealings in this Software without prior written
authorization from the authors.
-->
<!--
Screen Saver Extension to the X Protocol
Draft Standard Version 1.1
-->
<xcb header="screensaver" extension-xname="MIT-SCREEN-SAVER" extension-name="ScreenSaver" major-version="1" minor-version="1">

  <!-- Types -->

  <import>xproto</import>

  <enum name="Kind">
    <item name="Blanked" />
    <item name="Internal" />
    <item name="External" />
  </enum>

  <enum name="Event">
    <item name="NotifyMask"><bit>0</bit></item>
    <item name="CycleMask"> <bit>1</bit></item>
  </enum>

  <enum name="State">
    <item name="Off" />
    <item name="On" />
    <item name="Cycle" />
    <item name="Disabled" />
  </enum>

  <!-- Errors -->
  <!-- Requests -->
  <request name="QueryVersion" opcode="0">
    <field type="CARD8" name="client_major_version"/>
    <field type="CARD8" name="client_minor_version"/>
    <pad bytes="2"/>
    <reply>
      <pad bytes="1"/>
      <field type="CARD16" name="server_major_version"/>
      <field type="CARD16" name="server_minor_version"/>
      <pad bytes="20"/>
    </reply>
  </request>

  <request name="QueryInfo" opcode="1">
    <field type="DRAWABLE" name="drawable"/>
    <reply>
      <field type="CARD8" name="state"/>
      <field type="WINDOW" name="saver_window"/>
      <field type="CARD32" name="ms_until_server"/>
      <field type="CARD32" name="ms_since_user_input"/>
      <field type="CARD32" name="event_mask" />
      <field type="BYTE" name="kind"/>  <!-- enum Kind -->
      <pad bytes="7"/>
    </reply>
  </request>

  <request name="SelectInput" opcode="2">
    <field type="DRAWABLE" name="drawable"/>
    <field type="CARD32" name="event_mask" />   <!-- enum Event -->
  </request>

  <request name="SetAttributes" opcode="3">
    <field type="DRAWABLE" name="drawable"/>
    <field type="INT16" name="x"/>
    <field type="INT16" name="y"/>
    <field type="CARD16" name="width"/>
    <field type="CARD16" name="height"/>
    <field type="CARD16" name="border_width"/>
    <field type="BYTE" name="class"/>   <!-- enum XCBWindowClass -->
    <field type="CARD8" name="depth"/>
    <field type="VISUALID" name="visual"/>
    <valueparam value-mask-type="CARD32"
                value-mask-name="value_mask"
                value-list-name="value_list" />
  </request>

  <request name="UnsetAttributes" opcode="4">
    <field type="DRAWABLE" name="drawable"/>
  </request>
  
  <!-- version 1.1 -->
  <request name="Suspend" opcode="5">
    <field type="BOOL" name="suspend" />
    <pad bytes="3" />
  </request>

  <!-- Events -->

  <event name="Notify" number="0">
    <field type="CARD8" name="code"/>
    <field type="BYTE" name="state"/>    <!-- enum State -->
    <pad bytes="1" />
    <field type="CARD16" name="sequence_number"/>
    <field type="TIMESTAMP" name="time"/>
    <field type="WINDOW" name="root"/>
    <field type="WINDOW" name="window"/>
    <field type="BYTE" name="kind"/>     <!-- enum Kind -->
    <field type="BOOL" name="forced"/>
    <pad bytes="14"/>
  </event>

</xcb>
