<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright (C) 2006 <PERSON>.
All Rights Reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

Except as contained in this notice, the names of the authors or their
institutions shall not be used in advertising or otherwise to promote the
sale, use or other dealings in this Software without prior written
authorization from the authors.
-->

<xcb header="xinerama" extension-xname="XINERAMA" extension-name="Xinerama"
    major-version="1" minor-version="1">

    <!-- Version 1.1 -->

    <import>xproto</import>

    <struct name="ScreenInfo">
        <field type="INT16" name="x_org" />
        <field type="INT16" name="y_org" />
        <field type="CARD16" name="width" />
        <field type="CARD16" name="height" />
    </struct>

    <request name="QueryVersion" opcode="0">
        <field type="CARD8" name="major" />
        <field type="CARD8" name="minor" />
        <reply>
            <pad bytes="1" />
            <field type="CARD16" name="major" />
            <field type="CARD16" name="minor" />
        </reply>
    </request>

    <request name="GetState" opcode="1">
        <field type="WINDOW" name="window" />
        <reply>
            <field type="BYTE" name="state" />
            <field type="WINDOW" name="window" />
        </reply>
    </request>

    <request name="GetScreenCount" opcode="2">
        <field type="WINDOW" name="window" />
        <reply>
            <field type="BYTE" name="screen_count" />
            <field type="WINDOW" name="window" />
        </reply>
    </request>

    <request name="GetScreenSize" opcode="3">
        <field type="WINDOW" name="window" />
        <field type="CARD32" name="screen" />
        <reply>
            <pad bytes="1" />
            <field type="CARD32" name="width" />
            <field type="CARD32" name="height" />
            <field type="WINDOW" name="window" />
            <field type="CARD32" name="screen" />
        </reply>
    </request>

    <request name="IsActive" opcode="4">
        <reply>
            <pad bytes="1" />
            <field type="CARD32" name="state" />
        </reply>
    </request>

    <request name="QueryScreens" opcode="5">
        <reply>
            <pad bytes="1" />
            <field type="CARD32" name="number" />
            <pad bytes="20" />
            <list type="ScreenInfo" name="screen_info">
                <fieldref>number</fieldref>
            </list>
        </reply>
    </request>

</xcb>
