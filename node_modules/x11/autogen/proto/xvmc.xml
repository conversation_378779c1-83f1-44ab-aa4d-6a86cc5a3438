<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright (C) 2006 <PERSON>.
All Rights Reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

Except as contained in this notice, the names of the authors or their
institutions shall not be used in advertising or otherwise to promote the
sale, use or other dealings in this Software without prior written
authorization from the authors.
-->

<xcb header="xvmc" extension-xname="XVideo-MotionCompensation" extension-name="XvMC" major-version="1" minor-version="1">
    <import>xv</import>

    <xidtype name="CONTEXT" />
    <xidtype name="SURFACE" />
    <xidtype name="SUBPICTURE" />

    <struct name="SurfaceInfo">
        <field type="SURFACE" name="id" />
        <field type="CARD16" name="chroma_format" />
        <field type="CARD16" name="pad0" />
        <field type="CARD16" name="max_width" />
        <field type="CARD16" name="max_height" />
        <field type="CARD16" name="subpicture_max_width" />
        <field type="CARD16" name="subpicture_max_height" />
        <field type="CARD32" name="mc_type" />
        <field type="CARD32" name="flags" />
    </struct>
    
    <request name="QueryVersion" opcode="0"> 
        <reply>
            <pad bytes="1" />
            <field type="CARD32" name="major" />
            <field type="CARD32" name="minor" />
        </reply>
    </request> 

    <request name="ListSurfaceTypes" opcode="1">
        <field type="PORT" name="port_id" />
        <reply>
            <pad bytes="1" />
            <field type="CARD32" name="num" />
            <pad bytes="20" />
            <list type="SurfaceInfo" name="surfaces">
                <fieldref>num</fieldref>
            </list>
        </reply>
    </request>

    <request name="CreateContext" opcode="2">
        <field type="CONTEXT" name="context_id" />
        <field type="PORT" name="port_id" />
        <field type="SURFACE" name="surface_id" />
        <field type="CARD16" name="width" />
        <field type="CARD16" name="height" />
        <field type="CARD32" name="flags" />
        <reply>
            <pad bytes="1" />
            <field type="CARD16" name="width_actual" />
            <field type="CARD16" name="height_actual" />
            <field type="CARD32" name="flags_return" />
            <pad bytes="20" />
            <list type="CARD32" name="priv_data">
		<fieldref>length</fieldref>
	    </list>
        </reply>
    </request>

    <request name="DestroyContext" opcode="3">
        <field type="CONTEXT" name="context_id" />
    </request>
    
    <request name="CreateSurface" opcode="4">
        <field type="SURFACE" name="surface_id" />
        <field type="CONTEXT" name="context_id" />
        <reply>
            <pad bytes="1" />
            <pad bytes="24" />
            <list type="CARD32" name="priv_data">
		<fieldref>length</fieldref>
	    </list>
        </reply>
    </request>

    <request name="DestroySurface" opcode="5">
        <field type="SURFACE" name="surface_id" />
    </request>

    <request name="CreateSubpicture" opcode="6">
        <field type="SUBPICTURE" name="subpicture_id" />
        <field type="CONTEXT" name="context" />
        <field type="CARD32" name="xvimage_id" />
        <field type="CARD16" name="width" />
        <field type="CARD16" name="height" />
        <reply>
            <pad bytes="1" />
            <field type="CARD16" name="width_actual" />
            <field type="CARD16" name="height_actual" />
            <field type="CARD16" name="num_palette_entries" />
            <field type="CARD16" name="entry_bytes" />
            <list type="CARD8" name="component_order">
                <value>4</value>
            </list>
            <pad bytes="12" />
            <list type="CARD32" name="priv_data">
		<fieldref>length</fieldref>
	    </list>
        </reply>
    </request>

    <request name="DestroySubpicture" opcode="7">
        <field type="SUBPICTURE" name="subpicture_id" />
    </request>

    <request name="ListSubpictureTypes" opcode="8">
        <field type="PORT" name="port_id" />
        <field type="SURFACE" name="surface_id" />
        <reply>
            <pad bytes="1" />
            <field type="CARD32" name="num" />
            <pad bytes="20" />
            <list type="ImageFormatInfo" name="types">
                <fieldref>num</fieldref>
            </list>
        </reply>    
    </request>

</xcb> 
