<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright (C) 2001-2004 <PERSON>, <PERSON><PERSON>, and <PERSON>.
All Rights Reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

Except as contained in this notice, the names of the authors or their
institutions shall not be used in advertising or otherwise to promote the
sale, use or other dealings in this Software without prior written
authorization from the authors.
-->
<xcb header="shm" extension-xname="MIT-SHM" extension-name="Shm"
    major-version="1" minor-version="1">
  <import>xproto</import>

  <xidtype name="SEG" />

  <event name="Completion" number="0">
    <pad bytes="1" />
    <field type="DRAWABLE" name="drawable" />
    <field type="CARD16" name="minor_event" />
    <field type="BYTE" name="major_event" />
    <pad bytes="1" />
    <field type="SEG" name="shmseg" />
    <field type="CARD32" name="offset" />
  </event>

  <errorcopy name="BadSeg" number="0" ref="Value" />

  <request name="QueryVersion" opcode="0">
    <reply>
      <field type="BOOL" name="shared_pixmaps" />
      <field type="CARD16" name="major_version" />
      <field type="CARD16" name="minor_version" />
      <field type="CARD16" name="uid" />
      <field type="CARD16" name="gid" />
      <field type="CARD8" name="pixmap_format" />
      <pad bytes="15" />
    </reply>
  </request>

  <request name="Attach" opcode="1">
    <field type="SEG" name="shmseg" />
    <field type="CARD32" name="shmid" />
    <field type="BOOL" name="read_only" />
    <pad bytes="3" />
  </request>

  <request name="Detach" opcode="2">
    <field type="SEG" name="shmseg" />
  </request>

  <request name="PutImage" opcode="3">
    <field type="DRAWABLE" name="drawable" />
    <field type="GCONTEXT" name="gc" />
    <field type="CARD16" name="total_width" />
    <field type="CARD16" name="total_height" />
    <field type="CARD16" name="src_x" />
    <field type="CARD16" name="src_y" />
    <field type="CARD16" name="src_width" />
    <field type="CARD16" name="src_height" />
    <field type="INT16" name="dst_x" />
    <field type="INT16" name="dst_y" />
    <field type="CARD8" name="depth" />
    <field type="CARD8" name="format" />
    <field type="CARD8" name="send_event" />
    <pad bytes="1" />
    <field type="SEG" name="shmseg" />
    <field type="CARD32" name="offset" />
  </request>

  <request name="GetImage" opcode="4">
    <field type="DRAWABLE" name="drawable" />
    <field type="INT16" name="x" />
    <field type="INT16" name="y" />
    <field type="CARD16" name="width" />
    <field type="CARD16" name="height" />
    <field type="CARD32" name="plane_mask" />
    <field type="CARD8" name="format" />
    <pad bytes="3" />
    <field type="SEG" name="shmseg" />
    <field type="CARD32" name="offset" />
    <reply>
      <field type="CARD8" name="depth" />
      <field type="VISUALID" name="visual" />
      <field type="CARD32" name="size" />
    </reply>
  </request>

  <request name="CreatePixmap" opcode="5">
    <field type="PIXMAP" name="pid" />
    <field type="DRAWABLE" name="drawable" />
    <field type="CARD16" name="width" />
    <field type="CARD16" name="height" />
    <field type="CARD8" name="depth" />
    <pad bytes="3" />
    <field type="SEG" name="shmseg" />
    <field type="CARD32" name="offset" />
  </request>
</xcb>
