<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright (C) 2001-2004 <PERSON>, <PERSON><PERSON>, and <PERSON>.
All Rights Reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

Except as contained in this notice, the names of the authors or their
institutions shall not be used in advertising or otherwise to promote the
sale, use or other dealings in this Software without prior written
authorization from the authors.
-->
<xcb header="dpms" extension-xname="DPMS" extension-name="DPMS"
    major-version="0" minor-version="0">
  <request name="GetVersion" opcode="0">
    <field type="CARD16" name="client_major_version" />
    <field type="CARD16" name="client_minor_version" />
    <reply>
      <pad bytes="1" />
      <field type="CARD16" name="server_major_version" />
      <field type="CARD16" name="server_minor_version" />
    </reply>
  </request>

  <request name="Capable" opcode="1">
    <reply>
      <pad bytes="1" />
      <field type="BOOL" name="capable" />
      <pad bytes="23" />
    </reply>
  </request>

  <request name="GetTimeouts" opcode="2">
    <reply>
      <pad bytes="1" />
      <field type="CARD16" name="standby_timeout" />
      <field type="CARD16" name="suspend_timeout" />
      <field type="CARD16" name="off_timeout" />
      <pad bytes="18" />
    </reply>
  </request>

  <request name="SetTimeouts" opcode="3">
    <field type="CARD16" name="standby_timeout" />
    <field type="CARD16" name="suspend_timeout" />
    <field type="CARD16" name="off_timeout" />
  </request>

  <request name="Enable" opcode="4" />

  <request name="Disable" opcode="5" />

  <enum name="DPMSMode">
    <item name="On" />
    <item name="Standby" />
    <item name="Suspend" />
    <item name="Off" />
  </enum>

  <request name="ForceLevel" opcode="6">
    <field type="CARD16" name="power_level" enum="DPMSMode" />
  </request>

  <request name="Info" opcode="7">
    <reply>
      <pad bytes="1" />
      <field type="CARD16" name="power_level" enum="DPMSMode" />
      <field type="BOOL" name="state" />
      <pad bytes="21" />
    </reply>
  </request>
</xcb>
