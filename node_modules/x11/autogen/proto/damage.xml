<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright (C) 2004 <PERSON>
Copyright (C) 2007 <PERSON>b
All Rights Reserved.

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

Except as contained in this notice, the names of the authors or their
institutions shall not be used in advertising or otherwise to promote the
sale, use or other dealings in this Software without prior written
authorization from the authors.
-->
<!-- This file describes version 1.1 of DAMAGE. -->
<xcb header="damage" extension-xname="DAMAGE" extension-name="Damage"
    major-version="1" minor-version="1">
  <import>xproto</import>
  <import>xfixes</import>

  <xidtype name="DAMAGE" />

  <enum name="ReportLevel">
    <item name="RawRectangles" />
    <item name="DeltaRectangles" />
    <item name="BoundingBox" />
    <item name="NonEmpty" />
  </enum>

  <error name="BadDamage" number="0" />

  <request name="QueryVersion" opcode="0">
    <field type="CARD32" name="client_major_version" />
    <field type="CARD32" name="client_minor_version" />
    <reply>
      <pad bytes="1" />
      <field type="CARD32" name="major_version" />
      <field type="CARD32" name="minor_version" />
      <pad bytes="16" />
    </reply>
  </request>

  <request name="Create" opcode="1">
    <field type="DAMAGE"   name="damage" />
    <field type="DRAWABLE" name="drawable" />
    <field type="CARD8"    name="level" enum="ReportLevel" />
    <pad bytes="3" />
  </request>

  <request name="Destroy" opcode="2">
    <field type="DAMAGE" name="damage" />
  </request>

  <request name="Subtract" opcode="3">
    <field type="DAMAGE" name="damage" />
    <field type="REGION" name="repair" altenum="Region" />
    <field type="REGION" name="parts" altenum="Region" />
  </request>

  <request name="Add" opcode="4">
    <field type="DRAWABLE" name="drawable" />
    <field type="REGION" name="region" />
  </request>

  <event name="Notify" number="0">
    <field type="CARD8"     name="level" enum="ReportLevel" />
    <field type="DRAWABLE"  name="drawable" />
    <field type="DAMAGE"    name="damage" />
    <field type="TIMESTAMP" name="timestamp" />
    <field type="RECTANGLE" name="area" />
    <field type="RECTANGLE" name="geometry" />
  </event>
</xcb>
