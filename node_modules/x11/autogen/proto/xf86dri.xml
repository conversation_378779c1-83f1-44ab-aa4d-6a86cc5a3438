<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright (C) 2005 <PERSON>.
All Rights Reserved.

Permission is hereby granted, free of charge, to any person ob/Sintaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

Except as contained in this notice, the names of the authors or their
institutions shall not be used in advertising or otherwise to promote the
sale, use or other dealings in this Software without prior written
authorization from the authors.
-->

<xcb header="xf86dri" extension-xname="XFree86-DRI" extension-name="XF86Dri"
    major-version="4" minor-version="1">

    <!-- Using http://dri.sourceforge.net/doc/dri_extensions_low_level.txt -->

    <!-- Types -->

    <struct name="DrmClipRect">
	<field type="INT16" name="x1" />
	<field type="INT16" name="y1" />
	<field type="INT16" name="x2" />
	<field type="INT16" name="x3" />
    </struct>
	
    <!-- Requests -->
    <request name="QueryVersion" opcode="0">
	<reply>
	    <pad bytes="1" />
	    <field type="CARD16" name="dri_major_version" />
	    <field type="CARD16" name="dri_minor_version" />
	    <field type="CARD32" name="dri_minor_patch" />
	</reply>
    </request>

    <request name="QueryDirectRenderingCapable" opcode="1">
	<field type="CARD32" name="screen" />
	<reply>
	    <pad bytes="1" />
	    <field type="BOOL" name="is_capable" />
	</reply>
    </request>

    <request name="OpenConnection" opcode="2">
	<field type="CARD32" name="screen" />
	<reply>
	    <pad bytes="1" />
	    <field type="CARD32" name="sarea_handle_low" />
	    <field type="CARD32" name="sarea_handle_high" />
	    <field type="CARD32" name="bus_id_len" />
	    <pad bytes="12" />
	    <list type="char" name="bus_id">
		<fieldref>bus_id_len</fieldref>
	    </list>
	</reply>
    </request>

    <request name="CloseConnection" opcode="3">
	<field type="CARD32" name="screen" />
    </request>

    <request name="GetClientDriverName" opcode="4">
	<field type="CARD32" name="screen" />
	<reply>
	    <pad bytes="1" />
	    <field type="CARD32" name="client_driver_major_version" />
	    <field type="CARD32" name="client_driver_minor_version" />
	    <field type="CARD32" name="client_driver_patch_version" />
	    <field type="CARD32" name="client_driver_name_len" />
	    <pad bytes="8" />
	    <list type="char" name="client_driver_name">
		<fieldref>client_driver_name_len</fieldref>
	    </list>
	</reply>
    </request>

    <request name="CreateContext" opcode="5">
	<field type="CARD32" name="screen" />
	<field type="CARD32" name="visual" />
	<field type="CARD32" name="context" />
	<reply>
	    <pad bytes="1" />
	    <field type="CARD32" name="hw_context" />
	</reply>
    </request>

    <request name="DestroyContext" opcode="6">
	<field type="CARD32" name="screen" />
	<field type="CARD32" name="context" />
    </request>

    <request name="CreateDrawable" opcode="7">
	<field type="CARD32" name="screen" />
	<field type="CARD32" name="drawable" />
	<reply>
	    <pad bytes="1" />
	    <field type="CARD32" name="hw_drawable_handle" />
	</reply>
    </request>

    <request name="DestroyDrawable" opcode="8">
	<field type="CARD32" name="screen" />
	<field type="CARD32" name="drawable" />
    </request>

    <request name="GetDrawableInfo" opcode="9">
	<field type="CARD32" name="screen" />
	<field type="CARD32" name="drawable" />
	<reply>
	    <pad bytes="1" />
	    <field type="CARD32" name="drawable_table_index" />
	    <field type="CARD32" name="drawable_table_stamp" />
	    <field type="INT16" name="drawable_origin_X" />
	    <field type="INT16" name="drawable_origin_Y" />
	    <field type="INT16" name="drawable_size_W" />
	    <field type="INT16" name="drawable_size_H" />
	    <field type="CARD32" name="num_clip_rects" />
	    <field type="INT16" name="back_x" />
	    <field type="INT16" name="back_y" />
	    <field type="CARD32" name="num_back_clip_rects" />
	    <list type="DrmClipRect" name="clip_rects">
		<fieldref>num_clip_rects</fieldref>
	    </list>
	    <list type="DrmClipRect" name="back_clip_rects">
		<fieldref>num_back_clip_rects</fieldref>
	    </list>
	</reply>
    </request>

    <request name="GetDeviceInfo" opcode="10">
	<field type="CARD32" name="screen" />
	<reply>
	    <pad bytes="1" />
	    <field type="CARD32" name="framebuffer_handle_low" />
	    <field type="CARD32" name="framebuffer_handle_high" />
	    <field type="CARD32" name="framebuffer_origin_offset" />
	    <field type="CARD32" name="framebuffer_size" />
	    <field type="CARD32" name="framebuffer_stride" />
	    <field type="CARD32" name="device_private_size" />
	    <list type="CARD32" name="device_private">
		<fieldref>device_private_size</fieldref>
	    </list>
	</reply>
    </request>

    <!-- Not in this doc, but specified in Mesa/src/glx/x11/xf86dri.h -->
    <request name="AuthConnection" opcode="11">
	<field type="CARD32" name="screen" />
	<field type="CARD32" name="magic" />
	<reply>
	    <pad bytes="1" />
	    <field type="CARD32" name="authenticated" />
	</reply>
    </request>	    
</xcb>
