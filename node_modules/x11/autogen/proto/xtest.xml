<?xml version="1.0" encoding="utf-8"?>
<!--
Copyright (C) 2006 <PERSON>
All Rights Reserved.

Permission is hereby granted, free of charge, to any person ob/Sintaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

Except as contained in this notice, the names of the authors or their
institutions shall not be used in advertising or otherwise to promote the
sale, use or other dealings in this Software without prior written
authorization from the authors.
-->

<xcb header="xtest" extension-xname="XTEST" extension-name="Test"
    major-version="2" minor-version="1">
    <!-- Conforms to XTEST 2.1 -->

    <import>xproto</import>
    
    <!-- Requests -->
    <request name="GetVersion" opcode="0">
        <field type="CARD8"  name="major_version" />
        <pad bytes="1" />
        <field type="CARD16" name="minor_version" />
        <reply>
            <field type="CARD8"  name="major_version" />
            <field type="CARD16" name="minor_version" />
        </reply>
    </request>

    <enum name="Cursor">
        <item name="None">   <value>0</value></item>
        <item name="Current"><value>1</value></item>
    </enum>

    <request name="CompareCursor" opcode="1">
        <field type="WINDOW" name="window" />
        <field type="CURSOR" name="cursor" />
        <reply>
            <field type="BOOL" name="same" />
        </reply>
    </request>
    
    <!-- There are several ways to use the FakeInput request:
            1. Simulate a keypress:
                type   = XCBKeyPress or XCBKeyRelease
                detail = keycode
            2. Simulate a button:
                type   = XCBButtonPress or XCBButtonRelease
                detail = enum XCBButton (1..5)
            3. Simulate mouse motion:
                type   = XCBMotionNotify
                detail = 0 or 1
                root = XCBNone or a root window id
                rootX,
                rootY  = position (relative if detail==1)

        Version 2.2 adds support for the XInput extension:
            4. Simulate a device keypress
            5. Simulate a device button
            6. Simulate a proximity event
            7. Simulate a device motion event
                [These are not yet supported, since we would also need to
                 send the variable-length state of the axes.]

        In each case, time is set to a delay in milliseconds.
    -->

    <request name="FakeInput" opcode="2">
        <field type="BYTE"   name="type" />
        <field type="BYTE"   name="detail" />
        <pad bytes="2" />
        <field type="CARD32" name="time" />
        <field type="WINDOW" name="root" />
        <pad bytes="8" />
        <field type="INT16" name="rootX" />
        <field type="INT16" name="rootY" />
        <pad bytes="7" />
        <!-- Version 2.2: XInput device -->
        <field type="CARD8"  name="deviceid" />
        <!-- Version 2.2: optional axes state here -->
    </request>

    <request name="GrabControl" opcode="3">
        <field type="BOOL" name="impervious" />
        <pad bytes="3" />
    </request>
    
    <!-- No events or errors -->

</xcb>
