before_script:
  - "export XAUTHORITY=/tmp/.Xauthority-Xvfb"
  - "xauth add :99 . $(mcookie)"
  - "xauth add *********:99 . $(mcookie)"
  - "/sbin/start-stop-daemon --start --quiet --pidfile /tmp/custom_xvfb_99.pid --make-pidfile --background --exec /usr/bin/Xvfb -- :99 -nolisten $NOLISTEN -auth $XAUTHORITY"
  - "sleep 1"

env:
  - NOLISTEN=tcp DISPLAY=:99.0
  - NOLISTEN=unix DISPLAY=:99.0
  - NOLISTEN=unix DISPLAY=*********:99.0

language: node_js
node_js:
   - '0.10'
   - '0.12'
   - '4.2'
   - '5'
   - '6'
   - '7'
