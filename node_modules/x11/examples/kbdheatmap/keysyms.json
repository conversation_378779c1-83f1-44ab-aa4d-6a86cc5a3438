{"records": [{"keysym": 32, "unicode": 32, "status": ".", "names": ["space"]}, {"keysym": 33, "unicode": 33, "status": ".", "names": ["exclam"]}, {"keysym": 34, "unicode": 34, "status": ".", "names": ["quotedbl"]}, {"keysym": 35, "unicode": 35, "status": ".", "names": ["numbersign"]}, {"keysym": 36, "unicode": 36, "status": ".", "names": ["dollar"]}, {"keysym": 37, "unicode": 37, "status": ".", "names": ["percent"]}, {"keysym": 38, "unicode": 38, "status": ".", "names": ["ampersand"]}, {"keysym": 39, "unicode": 39, "status": ".", "names": ["apostrophe", "quoteright"]}, {"keysym": 40, "unicode": 40, "status": ".", "names": ["parenleft"]}, {"keysym": 41, "unicode": 41, "status": ".", "names": ["parenright"]}, {"keysym": 42, "unicode": 42, "status": ".", "names": ["asterisk"]}, {"keysym": 43, "unicode": 43, "status": ".", "names": ["plus"]}, {"keysym": 44, "unicode": 44, "status": ".", "names": ["comma"]}, {"keysym": 45, "unicode": 45, "status": ".", "names": ["minus"]}, {"keysym": 46, "unicode": 46, "status": ".", "names": ["period"]}, {"keysym": 47, "unicode": 47, "status": ".", "names": ["slash"]}, {"keysym": 48, "unicode": 48, "status": ".", "names": ["0"]}, {"keysym": 49, "unicode": 49, "status": ".", "names": ["1"]}, {"keysym": 50, "unicode": 50, "status": ".", "names": ["2"]}, {"keysym": 51, "unicode": 51, "status": ".", "names": ["3"]}, {"keysym": 52, "unicode": 52, "status": ".", "names": ["4"]}, {"keysym": 53, "unicode": 53, "status": ".", "names": ["5"]}, {"keysym": 54, "unicode": 54, "status": ".", "names": ["6"]}, {"keysym": 55, "unicode": 55, "status": ".", "names": ["7"]}, {"keysym": 56, "unicode": 56, "status": ".", "names": ["8"]}, {"keysym": 57, "unicode": 57, "status": ".", "names": ["9"]}, {"keysym": 58, "unicode": 58, "status": ".", "names": ["colon"]}, {"keysym": 59, "unicode": 59, "status": ".", "names": ["semicolon"]}, {"keysym": 60, "unicode": 60, "status": ".", "names": ["less"]}, {"keysym": 61, "unicode": 61, "status": ".", "names": ["equal"]}, {"keysym": 62, "unicode": 62, "status": ".", "names": ["greater"]}, {"keysym": 63, "unicode": 63, "status": ".", "names": ["question"]}, {"keysym": 64, "unicode": 64, "status": ".", "names": ["at"]}, {"keysym": 65, "unicode": 65, "status": ".", "names": ["A"]}, {"keysym": 66, "unicode": 66, "status": ".", "names": ["B"]}, {"keysym": 67, "unicode": 67, "status": ".", "names": ["C"]}, {"keysym": 68, "unicode": 68, "status": ".", "names": ["D"]}, {"keysym": 69, "unicode": 69, "status": ".", "names": ["E"]}, {"keysym": 70, "unicode": 70, "status": ".", "names": ["F"]}, {"keysym": 71, "unicode": 71, "status": ".", "names": ["G"]}, {"keysym": 72, "unicode": 72, "status": ".", "names": ["H"]}, {"keysym": 73, "unicode": 73, "status": ".", "names": ["I"]}, {"keysym": 74, "unicode": 74, "status": ".", "names": ["J"]}, {"keysym": 75, "unicode": 75, "status": ".", "names": ["K"]}, {"keysym": 76, "unicode": 76, "status": ".", "names": ["L"]}, {"keysym": 77, "unicode": 77, "status": ".", "names": ["M"]}, {"keysym": 78, "unicode": 78, "status": ".", "names": ["N"]}, {"keysym": 79, "unicode": 79, "status": ".", "names": ["O"]}, {"keysym": 80, "unicode": 80, "status": ".", "names": ["P"]}, {"keysym": 81, "unicode": 81, "status": ".", "names": ["Q"]}, {"keysym": 82, "unicode": 82, "status": ".", "names": ["R"]}, {"keysym": 83, "unicode": 83, "status": ".", "names": ["S"]}, {"keysym": 84, "unicode": 84, "status": ".", "names": ["T"]}, {"keysym": 85, "unicode": 85, "status": ".", "names": ["U"]}, {"keysym": 86, "unicode": 86, "status": ".", "names": ["V"]}, {"keysym": 87, "unicode": 87, "status": ".", "names": ["W"]}, {"keysym": 88, "unicode": 88, "status": ".", "names": ["X"]}, {"keysym": 89, "unicode": 89, "status": ".", "names": ["Y"]}, {"keysym": 90, "unicode": 90, "status": ".", "names": ["Z"]}, {"keysym": 91, "unicode": 91, "status": ".", "names": ["bracketleft"]}, {"keysym": 92, "unicode": 92, "status": ".", "names": ["backslash"]}, {"keysym": 93, "unicode": 93, "status": ".", "names": ["bracketright"]}, {"keysym": 94, "unicode": 94, "status": ".", "names": ["asciicircum"]}, {"keysym": 95, "unicode": 95, "status": ".", "names": ["underscore"]}, {"keysym": 96, "unicode": 96, "status": ".", "names": ["grave", "quoteleft"]}, {"keysym": 97, "unicode": 97, "status": ".", "names": ["a"]}, {"keysym": 98, "unicode": 98, "status": ".", "names": ["b"]}, {"keysym": 99, "unicode": 99, "status": ".", "names": ["c"]}, {"keysym": 100, "unicode": 100, "status": ".", "names": ["d"]}, {"keysym": 101, "unicode": 101, "status": ".", "names": ["e"]}, {"keysym": 102, "unicode": 102, "status": ".", "names": ["f"]}, {"keysym": 103, "unicode": 103, "status": ".", "names": ["g"]}, {"keysym": 104, "unicode": 104, "status": ".", "names": ["h"]}, {"keysym": 105, "unicode": 105, "status": ".", "names": ["i"]}, {"keysym": 106, "unicode": 106, "status": ".", "names": ["j"]}, {"keysym": 107, "unicode": 107, "status": ".", "names": ["k"]}, {"keysym": 108, "unicode": 108, "status": ".", "names": ["l"]}, {"keysym": 109, "unicode": 109, "status": ".", "names": ["m"]}, {"keysym": 110, "unicode": 110, "status": ".", "names": ["n"]}, {"keysym": 111, "unicode": 111, "status": ".", "names": ["o"]}, {"keysym": 112, "unicode": 112, "status": ".", "names": ["p"]}, {"keysym": 113, "unicode": 113, "status": ".", "names": ["q"]}, {"keysym": 114, "unicode": 114, "status": ".", "names": ["r"]}, {"keysym": 115, "unicode": 115, "status": ".", "names": ["s"]}, {"keysym": 116, "unicode": 116, "status": ".", "names": ["t"]}, {"keysym": 117, "unicode": 117, "status": ".", "names": ["u"]}, {"keysym": 118, "unicode": 118, "status": ".", "names": ["v"]}, {"keysym": 119, "unicode": 119, "status": ".", "names": ["w"]}, {"keysym": 120, "unicode": 120, "status": ".", "names": ["x"]}, {"keysym": 121, "unicode": 121, "status": ".", "names": ["y"]}, {"keysym": 122, "unicode": 122, "status": ".", "names": ["z"]}, {"keysym": 123, "unicode": 123, "status": ".", "names": ["braceleft"]}, {"keysym": 124, "unicode": 124, "status": ".", "names": ["bar"]}, {"keysym": 125, "unicode": 125, "status": ".", "names": ["braceright"]}, {"keysym": 126, "unicode": 126, "status": ".", "names": ["asciitilde"]}, {"keysym": 160, "unicode": 160, "status": ".", "names": ["nobreakspace"]}, {"keysym": 161, "unicode": 161, "status": ".", "names": ["exclamdown"]}, {"keysym": 162, "unicode": 162, "status": ".", "names": ["cent"]}, {"keysym": 163, "unicode": 163, "status": ".", "names": ["sterling"]}, {"keysym": 164, "unicode": 164, "status": ".", "names": ["currency"]}, {"keysym": 165, "unicode": 165, "status": ".", "names": ["yen"]}, {"keysym": 166, "unicode": 166, "status": ".", "names": ["brokenbar"]}, {"keysym": 167, "unicode": 167, "status": ".", "names": ["section"]}, {"keysym": 168, "unicode": 168, "status": ".", "names": ["diaeresis"]}, {"keysym": 169, "unicode": 169, "status": ".", "names": ["copyright"]}, {"keysym": 170, "unicode": 170, "status": ".", "names": ["ordfeminine"]}, {"keysym": 171, "unicode": 171, "status": ".", "names": ["guillemotleft"]}, {"keysym": 172, "unicode": 172, "status": ".", "names": ["notsign"]}, {"keysym": 173, "unicode": 173, "status": ".", "names": ["hyphen"]}, {"keysym": 174, "unicode": 174, "status": ".", "names": ["registered"]}, {"keysym": 175, "unicode": 175, "status": ".", "names": ["macron"]}, {"keysym": 176, "unicode": 176, "status": ".", "names": ["degree"]}, {"keysym": 177, "unicode": 177, "status": ".", "names": ["plus<PERSON>us"]}, {"keysym": 178, "unicode": 178, "status": ".", "names": ["twosuperior"]}, {"keysym": 179, "unicode": 179, "status": ".", "names": ["threesuperior"]}, {"keysym": 180, "unicode": 180, "status": ".", "names": ["acute"]}, {"keysym": 181, "unicode": 181, "status": ".", "names": ["mu"]}, {"keysym": 182, "unicode": 182, "status": ".", "names": ["paragraph"]}, {"keysym": 183, "unicode": 183, "status": ".", "names": ["periodcentered"]}, {"keysym": 184, "unicode": 184, "status": ".", "names": ["cedilla"]}, {"keysym": 185, "unicode": 185, "status": ".", "names": ["onesuperior"]}, {"keysym": 186, "unicode": 186, "status": ".", "names": ["masculine"]}, {"keysym": 187, "unicode": 187, "status": ".", "names": ["guil<PERSON><PERSON><PERSON>"]}, {"keysym": 188, "unicode": 188, "status": ".", "names": ["onequarter"]}, {"keysym": 189, "unicode": 189, "status": ".", "names": ["onehalf"]}, {"keysym": 190, "unicode": 190, "status": ".", "names": ["threequarters"]}, {"keysym": 191, "unicode": 191, "status": ".", "names": ["questiondown"]}, {"keysym": 192, "unicode": 192, "status": ".", "names": ["<PERSON><PERSON>"]}, {"keysym": 193, "unicode": 193, "status": ".", "names": ["Aacute"]}, {"keysym": 194, "unicode": 194, "status": ".", "names": ["Acircumflex"]}, {"keysym": 195, "unicode": 195, "status": ".", "names": ["<PERSON><PERSON>"]}, {"keysym": 196, "unicode": 196, "status": ".", "names": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"keysym": 197, "unicode": 197, "status": ".", "names": ["<PERSON><PERSON>"]}, {"keysym": 198, "unicode": 198, "status": ".", "names": ["AE"]}, {"keysym": 199, "unicode": 199, "status": ".", "names": ["Ccedilla"]}, {"keysym": 200, "unicode": 200, "status": ".", "names": ["<PERSON><PERSON>"]}, {"keysym": 201, "unicode": 201, "status": ".", "names": ["Eacute"]}, {"keysym": 202, "unicode": 202, "status": ".", "names": ["Ecircumflex"]}, {"keysym": 203, "unicode": 203, "status": ".", "names": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"keysym": 204, "unicode": 204, "status": ".", "names": ["<PERSON><PERSON>"]}, {"keysym": 205, "unicode": 205, "status": ".", "names": ["Iacute"]}, {"keysym": 206, "unicode": 206, "status": ".", "names": ["Icircumflex"]}, {"keysym": 207, "unicode": 207, "status": ".", "names": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"keysym": 208, "unicode": 208, "status": ".", "names": ["ETH", "Eth"]}, {"keysym": 209, "unicode": 209, "status": ".", "names": ["Ntilde"]}, {"keysym": 210, "unicode": 210, "status": ".", "names": ["<PERSON><PERSON>"]}, {"keysym": 211, "unicode": 211, "status": ".", "names": ["Oacute"]}, {"keysym": 212, "unicode": 212, "status": ".", "names": ["Ocircumflex"]}, {"keysym": 213, "unicode": 213, "status": ".", "names": ["<PERSON><PERSON><PERSON>"]}, {"keysym": 214, "unicode": 214, "status": ".", "names": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"keysym": 215, "unicode": 215, "status": ".", "names": ["multiply"]}, {"keysym": 216, "unicode": 216, "status": ".", "names": ["Ooblique"]}, {"keysym": 217, "unicode": 217, "status": ".", "names": ["<PERSON><PERSON>"]}, {"keysym": 218, "unicode": 218, "status": ".", "names": ["Uacute"]}, {"keysym": 219, "unicode": 219, "status": ".", "names": ["Ucircumflex"]}, {"keysym": 220, "unicode": 220, "status": ".", "names": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"keysym": 221, "unicode": 221, "status": ".", "names": ["Ya<PERSON>"]}, {"keysym": 222, "unicode": 222, "status": ".", "names": ["THORN", "Thorn"]}, {"keysym": 223, "unicode": 223, "status": ".", "names": ["ssharp"]}, {"keysym": 224, "unicode": 224, "status": ".", "names": ["agrave"]}, {"keysym": 225, "unicode": 225, "status": ".", "names": ["aacute"]}, {"keysym": 226, "unicode": 226, "status": ".", "names": ["acircumflex"]}, {"keysym": 227, "unicode": 227, "status": ".", "names": ["atilde"]}, {"keysym": 228, "unicode": 228, "status": ".", "names": ["ad<PERSON><PERSON><PERSON>"]}, {"keysym": 229, "unicode": 229, "status": ".", "names": ["aring"]}, {"keysym": 230, "unicode": 230, "status": ".", "names": ["ae"]}, {"keysym": 231, "unicode": 231, "status": ".", "names": ["ccedilla"]}, {"keysym": 232, "unicode": 232, "status": ".", "names": ["egrave"]}, {"keysym": 233, "unicode": 233, "status": ".", "names": ["eacute"]}, {"keysym": 234, "unicode": 234, "status": ".", "names": ["ecircumflex"]}, {"keysym": 235, "unicode": 235, "status": ".", "names": ["ed<PERSON><PERSON><PERSON>"]}, {"keysym": 236, "unicode": 236, "status": ".", "names": ["igrave"]}, {"keysym": 237, "unicode": 237, "status": ".", "names": ["iacute"]}, {"keysym": 238, "unicode": 238, "status": ".", "names": ["icircumflex"]}, {"keysym": 239, "unicode": 239, "status": ".", "names": ["idiaeresis"]}, {"keysym": 240, "unicode": 240, "status": ".", "names": ["eth"]}, {"keysym": 241, "unicode": 241, "status": ".", "names": ["ntilde"]}, {"keysym": 242, "unicode": 242, "status": ".", "names": ["ograve"]}, {"keysym": 243, "unicode": 243, "status": ".", "names": ["oacute"]}, {"keysym": 244, "unicode": 244, "status": ".", "names": ["ocircumflex"]}, {"keysym": 245, "unicode": 245, "status": ".", "names": ["otilde"]}, {"keysym": 246, "unicode": 246, "status": ".", "names": ["odia<PERSON>sis"]}, {"keysym": 247, "unicode": 247, "status": ".", "names": ["division"]}, {"keysym": 248, "unicode": 248, "status": ".", "names": ["oslash"]}, {"keysym": 249, "unicode": 249, "status": ".", "names": ["ugrave"]}, {"keysym": 250, "unicode": 250, "status": ".", "names": ["uacute"]}, {"keysym": 251, "unicode": 251, "status": ".", "names": ["ucircumflex"]}, {"keysym": 252, "unicode": 252, "status": ".", "names": ["u<PERSON><PERSON><PERSON>"]}, {"keysym": 253, "unicode": 253, "status": ".", "names": ["yacute"]}, {"keysym": 254, "unicode": 254, "status": ".", "names": ["thorn"]}, {"keysym": 255, "unicode": 255, "status": ".", "names": ["ydiae<PERSON><PERSON>"]}, {"keysym": 417, "unicode": 260, "status": ".", "names": ["Aogonek"]}, {"keysym": 418, "unicode": 728, "status": ".", "names": ["breve"]}, {"keysym": 419, "unicode": 321, "status": ".", "names": ["Lstroke"]}, {"keysym": 421, "unicode": 317, "status": ".", "names": ["<PERSON><PERSON><PERSON>"]}, {"keysym": 422, "unicode": 346, "status": ".", "names": ["Sacute"]}, {"keysym": 425, "unicode": 352, "status": ".", "names": ["<PERSON><PERSON><PERSON>"]}, {"keysym": 426, "unicode": 350, "status": ".", "names": ["Scedilla"]}, {"keysym": 427, "unicode": 356, "status": ".", "names": ["<PERSON><PERSON><PERSON>"]}, {"keysym": 428, "unicode": 377, "status": ".", "names": ["Zacute"]}, {"keysym": 430, "unicode": 381, "status": ".", "names": ["<PERSON><PERSON><PERSON>"]}, {"keysym": 431, "unicode": 379, "status": ".", "names": ["Zabovedot"]}, {"keysym": 433, "unicode": 261, "status": ".", "names": ["aogonek"]}, {"keysym": 434, "unicode": 731, "status": ".", "names": ["ogonek"]}, {"keysym": 435, "unicode": 322, "status": ".", "names": ["lstroke"]}, {"keysym": 437, "unicode": 318, "status": ".", "names": ["lcaron"]}, {"keysym": 438, "unicode": 347, "status": ".", "names": ["sacute"]}, {"keysym": 439, "unicode": 711, "status": ".", "names": ["caron"]}, {"keysym": 441, "unicode": 353, "status": ".", "names": ["scaron"]}, {"keysym": 442, "unicode": 351, "status": ".", "names": ["scedilla"]}, {"keysym": 443, "unicode": 357, "status": ".", "names": ["tcaron"]}, {"keysym": 444, "unicode": 378, "status": ".", "names": ["zacute"]}, {"keysym": 445, "unicode": 733, "status": ".", "names": ["doubleacute"]}, {"keysym": 446, "unicode": 382, "status": ".", "names": ["z<PERSON>on"]}, {"keysym": 447, "unicode": 380, "status": ".", "names": ["zabovedot"]}, {"keysym": 448, "unicode": 340, "status": ".", "names": ["<PERSON><PERSON>"]}, {"keysym": 451, "unicode": 258, "status": ".", "names": ["Abreve"]}, {"keysym": 453, "unicode": 313, "status": ".", "names": ["<PERSON><PERSON>"]}, {"keysym": 454, "unicode": 262, "status": ".", "names": ["Cacute"]}, {"keysym": 456, "unicode": 268, "status": ".", "names": ["<PERSON><PERSON><PERSON>"]}, {"keysym": 458, "unicode": 280, "status": ".", "names": ["Eogonek"]}, {"keysym": 460, "unicode": 282, "status": ".", "names": ["<PERSON><PERSON><PERSON>"]}, {"keysym": 463, "unicode": 270, "status": ".", "names": ["<PERSON><PERSON><PERSON>"]}, {"keysym": 464, "unicode": 272, "status": ".", "names": ["Dstroke"]}, {"keysym": 465, "unicode": 323, "status": ".", "names": ["Nacute"]}, {"keysym": 466, "unicode": 327, "status": ".", "names": ["<PERSON><PERSON><PERSON>"]}, {"keysym": 469, "unicode": 336, "status": ".", "names": ["Odoubleacute"]}, {"keysym": 472, "unicode": 344, "status": ".", "names": ["<PERSON><PERSON><PERSON>"]}, {"keysym": 473, "unicode": 366, "status": ".", "names": ["<PERSON><PERSON>"]}, {"keysym": 475, "unicode": 368, "status": ".", "names": ["Udoubleacute"]}, {"keysym": 478, "unicode": 354, "status": ".", "names": ["Tcedilla"]}, {"keysym": 480, "unicode": 341, "status": ".", "names": ["racute"]}, {"keysym": 483, "unicode": 259, "status": ".", "names": ["abreve"]}, {"keysym": 485, "unicode": 314, "status": ".", "names": ["lacute"]}, {"keysym": 486, "unicode": 263, "status": ".", "names": ["cacute"]}, {"keysym": 488, "unicode": 269, "status": ".", "names": ["ccaron"]}, {"keysym": 490, "unicode": 281, "status": ".", "names": ["eogonek"]}, {"keysym": 492, "unicode": 283, "status": ".", "names": ["ecaron"]}, {"keysym": 495, "unicode": 271, "status": ".", "names": ["dcaron"]}, {"keysym": 496, "unicode": 273, "status": ".", "names": ["dstroke"]}, {"keysym": 497, "unicode": 324, "status": ".", "names": ["nacute"]}, {"keysym": 498, "unicode": 328, "status": ".", "names": ["ncaron"]}, {"keysym": 501, "unicode": 337, "status": ".", "names": ["odoubleacute"]}, {"keysym": 504, "unicode": 345, "status": ".", "names": ["rcaron"]}, {"keysym": 505, "unicode": 367, "status": ".", "names": ["uring"]}, {"keysym": 507, "unicode": 369, "status": ".", "names": ["udoubleacute"]}, {"keysym": 510, "unicode": 355, "status": ".", "names": ["tcedilla"]}, {"keysym": 511, "unicode": 729, "status": ".", "names": ["abovedot"]}, {"keysym": 673, "unicode": 294, "status": ".", "names": ["Hstroke"]}, {"keysym": 678, "unicode": 292, "status": ".", "names": ["Hcircumflex"]}, {"keysym": 681, "unicode": 304, "status": ".", "names": ["Iabovedot"]}, {"keysym": 683, "unicode": 286, "status": ".", "names": ["Gbreve"]}, {"keysym": 684, "unicode": 308, "status": ".", "names": ["Jcircumflex"]}, {"keysym": 689, "unicode": 295, "status": ".", "names": ["hstroke"]}, {"keysym": 694, "unicode": 293, "status": ".", "names": ["hcircumflex"]}, {"keysym": 697, "unicode": 305, "status": ".", "names": ["idotless"]}, {"keysym": 699, "unicode": 287, "status": ".", "names": ["gbreve"]}, {"keysym": 700, "unicode": 309, "status": ".", "names": ["jcircumflex"]}, {"keysym": 709, "unicode": 266, "status": ".", "names": ["Cabovedot"]}, {"keysym": 710, "unicode": 264, "status": ".", "names": ["Ccircumflex"]}, {"keysym": 725, "unicode": 288, "status": ".", "names": ["Gabovedot"]}, {"keysym": 728, "unicode": 284, "status": ".", "names": ["Gcircumflex"]}, {"keysym": 733, "unicode": 364, "status": ".", "names": ["Ubreve"]}, {"keysym": 734, "unicode": 348, "status": ".", "names": ["Scircumflex"]}, {"keysym": 741, "unicode": 267, "status": ".", "names": ["cabovedot"]}, {"keysym": 742, "unicode": 265, "status": ".", "names": ["ccircumflex"]}, {"keysym": 757, "unicode": 289, "status": ".", "names": ["gabovedot"]}, {"keysym": 760, "unicode": 285, "status": ".", "names": ["gcircumflex"]}, {"keysym": 765, "unicode": 365, "status": ".", "names": ["ubreve"]}, {"keysym": 766, "unicode": 349, "status": ".", "names": ["scircumflex"]}, {"keysym": 930, "unicode": 312, "status": ".", "names": ["kra"]}, {"keysym": 931, "unicode": 342, "status": ".", "names": ["Rcedilla"]}, {"keysym": 933, "unicode": 296, "status": ".", "names": ["Itilde"]}, {"keysym": 934, "unicode": 315, "status": ".", "names": ["Lcedilla"]}, {"keysym": 938, "unicode": 274, "status": ".", "names": ["Emacron"]}, {"keysym": 939, "unicode": 290, "status": ".", "names": ["Gcedilla"]}, {"keysym": 940, "unicode": 358, "status": ".", "names": ["T<PERSON>sh"]}, {"keysym": 947, "unicode": 343, "status": ".", "names": ["rcedilla"]}, {"keysym": 949, "unicode": 297, "status": ".", "names": ["itilde"]}, {"keysym": 950, "unicode": 316, "status": ".", "names": ["lcedilla"]}, {"keysym": 954, "unicode": 275, "status": ".", "names": ["emacron"]}, {"keysym": 955, "unicode": 291, "status": ".", "names": ["gcedilla"]}, {"keysym": 956, "unicode": 359, "status": ".", "names": ["tslash"]}, {"keysym": 957, "unicode": 330, "status": ".", "names": ["ENG"]}, {"keysym": 959, "unicode": 331, "status": ".", "names": ["eng"]}, {"keysym": 960, "unicode": 256, "status": ".", "names": ["Amacron"]}, {"keysym": 967, "unicode": 302, "status": ".", "names": ["Iogonek"]}, {"keysym": 972, "unicode": 278, "status": ".", "names": ["Eabovedot"]}, {"keysym": 975, "unicode": 298, "status": ".", "names": ["Imacron"]}, {"keysym": 977, "unicode": 325, "status": ".", "names": ["Ncedilla"]}, {"keysym": 978, "unicode": 332, "status": ".", "names": ["Omacron"]}, {"keysym": 979, "unicode": 310, "status": ".", "names": ["Kcedilla"]}, {"keysym": 985, "unicode": 370, "status": ".", "names": ["Uogonek"]}, {"keysym": 989, "unicode": 360, "status": ".", "names": ["Utilde"]}, {"keysym": 990, "unicode": 362, "status": ".", "names": ["Umacron"]}, {"keysym": 992, "unicode": 257, "status": ".", "names": ["amacron"]}, {"keysym": 999, "unicode": 303, "status": ".", "names": ["iogonek"]}, {"keysym": 1004, "unicode": 279, "status": ".", "names": ["eabovedot"]}, {"keysym": 1007, "unicode": 299, "status": ".", "names": ["imacron"]}, {"keysym": 1009, "unicode": 326, "status": ".", "names": ["ncedilla"]}, {"keysym": 1010, "unicode": 333, "status": ".", "names": ["omacron"]}, {"keysym": 1011, "unicode": 311, "status": ".", "names": ["kcedilla"]}, {"keysym": 1017, "unicode": 371, "status": ".", "names": ["uogonek"]}, {"keysym": 1021, "unicode": 361, "status": ".", "names": ["utilde"]}, {"keysym": 1022, "unicode": 363, "status": ".", "names": ["umacron"]}, {"keysym": 1150, "unicode": 8254, "status": ".", "names": ["overline"]}, {"keysym": 1185, "unicode": 12290, "status": ".", "names": ["kana_fullstop"]}, {"keysym": 1186, "unicode": 12300, "status": ".", "names": ["kana_openingbracket"]}, {"keysym": 1187, "unicode": 12301, "status": ".", "names": ["kana_closingbracket"]}, {"keysym": 1188, "unicode": 12289, "status": ".", "names": ["kana_comma"]}, {"keysym": 1189, "unicode": 12539, "status": ".", "names": ["kana_conjunctive"]}, {"keysym": 1190, "unicode": 12530, "status": ".", "names": ["kana_WO"]}, {"keysym": 1191, "unicode": 12449, "status": ".", "names": ["kana_a"]}, {"keysym": 1192, "unicode": 12451, "status": ".", "names": ["kana_i"]}, {"keysym": 1193, "unicode": 12453, "status": ".", "names": ["kana_u"]}, {"keysym": 1194, "unicode": 12455, "status": ".", "names": ["kana_e"]}, {"keysym": 1195, "unicode": 12457, "status": ".", "names": ["kana_o"]}, {"keysym": 1196, "unicode": 12515, "status": ".", "names": ["kana_ya"]}, {"keysym": 1197, "unicode": 12517, "status": ".", "names": ["kana_yu"]}, {"keysym": 1198, "unicode": 12519, "status": ".", "names": ["kana_yo"]}, {"keysym": 1199, "unicode": 12483, "status": ".", "names": ["kana_tsu"]}, {"keysym": 1200, "unicode": 12540, "status": ".", "names": ["prolongedsound"]}, {"keysym": 1201, "unicode": 12450, "status": ".", "names": ["kana_A"]}, {"keysym": 1202, "unicode": 12452, "status": ".", "names": ["kana_I"]}, {"keysym": 1203, "unicode": 12454, "status": ".", "names": ["kana_U"]}, {"keysym": 1204, "unicode": 12456, "status": ".", "names": ["kana_<PERSON>"]}, {"keysym": 1205, "unicode": 12458, "status": ".", "names": ["kana_<PERSON>"]}, {"keysym": 1206, "unicode": 12459, "status": ".", "names": ["kana_KA"]}, {"keysym": 1207, "unicode": 12461, "status": ".", "names": ["kana_KI"]}, {"keysym": 1208, "unicode": 12463, "status": ".", "names": ["kana_KU"]}, {"keysym": 1209, "unicode": 12465, "status": ".", "names": ["kana_<PERSON>E"]}, {"keysym": 1210, "unicode": 12467, "status": ".", "names": ["kana_<PERSON><PERSON>"]}, {"keysym": 1211, "unicode": 12469, "status": ".", "names": ["kana_<PERSON>"]}, {"keysym": 1212, "unicode": 12471, "status": ".", "names": ["kana_SHI"]}, {"keysym": 1213, "unicode": 12473, "status": ".", "names": ["kana_SU"]}, {"keysym": 1214, "unicode": 12475, "status": ".", "names": ["kana_SE"]}, {"keysym": 1215, "unicode": 12477, "status": ".", "names": ["kana_<PERSON>O"]}, {"keysym": 1216, "unicode": 12479, "status": ".", "names": ["kana_TA"]}, {"keysym": 1217, "unicode": 12481, "status": ".", "names": ["kana_CHI"]}, {"keysym": 1218, "unicode": 12484, "status": ".", "names": ["kana_TSU"]}, {"keysym": 1219, "unicode": 12486, "status": ".", "names": ["kana_TE"]}, {"keysym": 1220, "unicode": 12488, "status": ".", "names": ["kana_TO"]}, {"keysym": 1221, "unicode": 12490, "status": ".", "names": ["kana_NA"]}, {"keysym": 1222, "unicode": 12491, "status": ".", "names": ["kana_NI"]}, {"keysym": 1223, "unicode": 12492, "status": ".", "names": ["kana_NU"]}, {"keysym": 1224, "unicode": 12493, "status": ".", "names": ["kana_NE"]}, {"keysym": 1225, "unicode": 12494, "status": ".", "names": ["kana_<PERSON>"]}, {"keysym": 1226, "unicode": 12495, "status": ".", "names": ["kana_HA"]}, {"keysym": 1227, "unicode": 12498, "status": ".", "names": ["kana_HI"]}, {"keysym": 1228, "unicode": 12501, "status": ".", "names": ["kana_FU"]}, {"keysym": 1229, "unicode": 12504, "status": ".", "names": ["kana_HE"]}, {"keysym": 1230, "unicode": 12507, "status": ".", "names": ["kana_<PERSON><PERSON>"]}, {"keysym": 1231, "unicode": 12510, "status": ".", "names": ["kana_<PERSON>"]}, {"keysym": 1232, "unicode": 12511, "status": ".", "names": ["kana_<PERSON>"]}, {"keysym": 1233, "unicode": 12512, "status": ".", "names": ["kana_MU"]}, {"keysym": 1234, "unicode": 12513, "status": ".", "names": ["kana_<PERSON>"]}, {"keysym": 1235, "unicode": 12514, "status": ".", "names": ["kana_<PERSON><PERSON>"]}, {"keysym": 1236, "unicode": 12516, "status": ".", "names": ["kana_Y<PERSON>"]}, {"keysym": 1237, "unicode": 12518, "status": ".", "names": ["kana_YU"]}, {"keysym": 1238, "unicode": 12520, "status": ".", "names": ["kana_<PERSON><PERSON>"]}, {"keysym": 1239, "unicode": 12521, "status": ".", "names": ["kana_<PERSON>"]}, {"keysym": 1240, "unicode": 12522, "status": ".", "names": ["kana_RI"]}, {"keysym": 1241, "unicode": 12523, "status": ".", "names": ["kana_RU"]}, {"keysym": 1242, "unicode": 12524, "status": ".", "names": ["kana_RE"]}, {"keysym": 1243, "unicode": 12525, "status": ".", "names": ["kana_RO"]}, {"keysym": 1244, "unicode": 12527, "status": ".", "names": ["kana_<PERSON>"]}, {"keysym": 1245, "unicode": 12531, "status": ".", "names": ["kana_<PERSON>"]}, {"keysym": 1246, "unicode": 12443, "status": ".", "names": ["voicedsound"]}, {"keysym": 1247, "unicode": 12444, "status": ".", "names": ["semivoicedsound"]}, {"keysym": 1452, "unicode": 1548, "status": ".", "names": ["Arabic_comma"]}, {"keysym": 1467, "unicode": 1563, "status": ".", "names": ["Arabic_semicolon"]}, {"keysym": 1471, "unicode": 1567, "status": ".", "names": ["Arabic_question_mark"]}, {"keysym": 1473, "unicode": 1569, "status": ".", "names": ["Arabic_hamza"]}, {"keysym": 1474, "unicode": 1570, "status": ".", "names": ["Arabic_maddaonalef"]}, {"keysym": 1475, "unicode": 1571, "status": ".", "names": ["Arabic_hamzaonalef"]}, {"keysym": 1476, "unicode": 1572, "status": ".", "names": ["Arabic_hamzaonwaw"]}, {"keysym": 1477, "unicode": 1573, "status": ".", "names": ["Arabic_hamzaunderalef"]}, {"keysym": 1478, "unicode": 1574, "status": ".", "names": ["Arabic_hamza<PERSON>eh"]}, {"keysym": 1479, "unicode": 1575, "status": ".", "names": ["Arabic_alef"]}, {"keysym": 1480, "unicode": 1576, "status": ".", "names": ["Arabic_beh"]}, {"keysym": 1481, "unicode": 1577, "status": ".", "names": ["Arabic_tehmarbuta"]}, {"keysym": 1482, "unicode": 1578, "status": ".", "names": ["Arabic_teh"]}, {"keysym": 1483, "unicode": 1579, "status": ".", "names": ["Arabic_theh"]}, {"keysym": 1484, "unicode": 1580, "status": ".", "names": ["Arabic_jeem"]}, {"keysym": 1485, "unicode": 1581, "status": ".", "names": ["Arabic_hah"]}, {"keysym": 1486, "unicode": 1582, "status": ".", "names": ["Arabic_khah"]}, {"keysym": 1487, "unicode": 1583, "status": ".", "names": ["Arabic_dal"]}, {"keysym": 1488, "unicode": 1584, "status": ".", "names": ["Arabic_thal"]}, {"keysym": 1489, "unicode": 1585, "status": ".", "names": ["Arabic_ra"]}, {"keysym": 1490, "unicode": 1586, "status": ".", "names": ["Arabic_zain"]}, {"keysym": 1491, "unicode": 1587, "status": ".", "names": ["Arabic_seen"]}, {"keysym": 1492, "unicode": 1588, "status": ".", "names": ["Arabic_sheen"]}, {"keysym": 1493, "unicode": 1589, "status": ".", "names": ["Arabic_sad"]}, {"keysym": 1494, "unicode": 1590, "status": ".", "names": ["Arabic_dad"]}, {"keysym": 1495, "unicode": 1591, "status": ".", "names": ["Arabic_tah"]}, {"keysym": 1496, "unicode": 1592, "status": ".", "names": ["Arabic_zah"]}, {"keysym": 1497, "unicode": 1593, "status": ".", "names": ["Arabic_ain"]}, {"keysym": 1498, "unicode": 1594, "status": ".", "names": ["Arabic_ghain"]}, {"keysym": 1504, "unicode": 1600, "status": ".", "names": ["Arabic_tatweel"]}, {"keysym": 1505, "unicode": 1601, "status": ".", "names": ["Arabic_feh"]}, {"keysym": 1506, "unicode": 1602, "status": ".", "names": ["Arabic_qaf"]}, {"keysym": 1507, "unicode": 1603, "status": ".", "names": ["Arabic_kaf"]}, {"keysym": 1508, "unicode": 1604, "status": ".", "names": ["Arabic_lam"]}, {"keysym": 1509, "unicode": 1605, "status": ".", "names": ["Arabic_meem"]}, {"keysym": 1510, "unicode": 1606, "status": ".", "names": ["Arabic_noon"]}, {"keysym": 1511, "unicode": 1607, "status": ".", "names": ["Arabic_ha"]}, {"keysym": 1512, "unicode": 1608, "status": ".", "names": ["Arabic_waw"]}, {"keysym": 1513, "unicode": 1609, "status": ".", "names": ["Arabic_alefma<PERSON>ura"]}, {"keysym": 1514, "unicode": 1610, "status": ".", "names": ["Arabic_yeh"]}, {"keysym": 1515, "unicode": 1611, "status": ".", "names": ["Arabic_fathatan"]}, {"keysym": 1516, "unicode": 1612, "status": ".", "names": ["Arabic_dammatan"]}, {"keysym": 1517, "unicode": 1613, "status": ".", "names": ["Arabic_kasratan"]}, {"keysym": 1518, "unicode": 1614, "status": ".", "names": ["Arabic_fatha"]}, {"keysym": 1519, "unicode": 1615, "status": ".", "names": ["Arabic_damma"]}, {"keysym": 1520, "unicode": 1616, "status": ".", "names": ["Arabic_kasra"]}, {"keysym": 1521, "unicode": 1617, "status": ".", "names": ["Arabic_shadda"]}, {"keysym": 1522, "unicode": 1618, "status": ".", "names": ["Arabic_sukun"]}, {"keysym": 1697, "unicode": 1106, "status": ".", "names": ["Serbian_dje"]}, {"keysym": 1698, "unicode": 1107, "status": ".", "names": ["Macedonia_gje"]}, {"keysym": 1699, "unicode": 1105, "status": ".", "names": ["Cyrillic_io"]}, {"keysym": 1700, "unicode": 1108, "status": ".", "names": ["Ukrainian_ie"]}, {"keysym": 1701, "unicode": 1109, "status": ".", "names": ["Macedonia_dse"]}, {"keysym": 1702, "unicode": 1110, "status": ".", "names": ["Ukrainian_i"]}, {"keysym": 1703, "unicode": 1111, "status": ".", "names": ["Ukrainian_yi"]}, {"keysym": 1704, "unicode": 1112, "status": ".", "names": ["Cyrillic_je"]}, {"keysym": 1705, "unicode": 1113, "status": ".", "names": ["Cyrillic_lje"]}, {"keysym": 1706, "unicode": 1114, "status": ".", "names": ["Cyrillic_nje"]}, {"keysym": 1707, "unicode": 1115, "status": ".", "names": ["Serbian_tshe"]}, {"keysym": 1708, "unicode": 1116, "status": ".", "names": ["Macedonia_kje"]}, {"keysym": 1710, "unicode": 1118, "status": ".", "names": ["Byelorussian_shortu"]}, {"keysym": 1711, "unicode": 1119, "status": ".", "names": ["Cyrillic_dzhe"]}, {"keysym": 1712, "unicode": 8470, "status": ".", "names": ["numerosign"]}, {"keysym": 1713, "unicode": 1026, "status": ".", "names": ["Serbian_DJE"]}, {"keysym": 1714, "unicode": 1027, "status": ".", "names": ["Macedonia_GJE"]}, {"keysym": 1715, "unicode": 1025, "status": ".", "names": ["Cyrillic_IO"]}, {"keysym": 1716, "unicode": 1028, "status": ".", "names": ["Ukrainian_IE"]}, {"keysym": 1717, "unicode": 1029, "status": ".", "names": ["Macedonia_DSE"]}, {"keysym": 1718, "unicode": 1030, "status": ".", "names": ["Ukrainian_I"]}, {"keysym": 1719, "unicode": 1031, "status": ".", "names": ["Ukrainian_YI"]}, {"keysym": 1720, "unicode": 1032, "status": ".", "names": ["Cyrillic_JE"]}, {"keysym": 1721, "unicode": 1033, "status": ".", "names": ["Cyrillic_LJE"]}, {"keysym": 1722, "unicode": 1034, "status": ".", "names": ["Cyrillic_NJE"]}, {"keysym": 1723, "unicode": 1035, "status": ".", "names": ["Serbian_TSHE"]}, {"keysym": 1724, "unicode": 1036, "status": ".", "names": ["Macedonia_KJE"]}, {"keysym": 1726, "unicode": 1038, "status": ".", "names": ["Byelorussian_SHORTU"]}, {"keysym": 1727, "unicode": 1039, "status": ".", "names": ["Cyrillic_DZHE"]}, {"keysym": 1728, "unicode": 1102, "status": ".", "names": ["Cyrillic_yu"]}, {"keysym": 1729, "unicode": 1072, "status": ".", "names": ["Cyrillic_a"]}, {"keysym": 1730, "unicode": 1073, "status": ".", "names": ["Cyrillic_be"]}, {"keysym": 1731, "unicode": 1094, "status": ".", "names": ["Cyrillic_tse"]}, {"keysym": 1732, "unicode": 1076, "status": ".", "names": ["Cyrillic_de"]}, {"keysym": 1733, "unicode": 1077, "status": ".", "names": ["Cyrillic_ie"]}, {"keysym": 1734, "unicode": 1092, "status": ".", "names": ["Cyrillic_ef"]}, {"keysym": 1735, "unicode": 1075, "status": ".", "names": ["Cyrillic_ghe"]}, {"keysym": 1736, "unicode": 1093, "status": ".", "names": ["Cyrillic_ha"]}, {"keysym": 1737, "unicode": 1080, "status": ".", "names": ["Cyrillic_i"]}, {"keysym": 1738, "unicode": 1081, "status": ".", "names": ["Cyrillic_shorti"]}, {"keysym": 1739, "unicode": 1082, "status": ".", "names": ["Cyrillic_ka"]}, {"keysym": 1740, "unicode": 1083, "status": ".", "names": ["Cyrillic_el"]}, {"keysym": 1741, "unicode": 1084, "status": ".", "names": ["Cyrillic_em"]}, {"keysym": 1742, "unicode": 1085, "status": ".", "names": ["Cyrillic_en"]}, {"keysym": 1743, "unicode": 1086, "status": ".", "names": ["Cyrillic_o"]}, {"keysym": 1744, "unicode": 1087, "status": ".", "names": ["Cyrillic_pe"]}, {"keysym": 1745, "unicode": 1103, "status": ".", "names": ["Cyrillic_ya"]}, {"keysym": 1746, "unicode": 1088, "status": ".", "names": ["Cyrillic_er"]}, {"keysym": 1747, "unicode": 1089, "status": ".", "names": ["Cyrillic_es"]}, {"keysym": 1748, "unicode": 1090, "status": ".", "names": ["Cyrillic_te"]}, {"keysym": 1749, "unicode": 1091, "status": ".", "names": ["Cyrillic_u"]}, {"keysym": 1750, "unicode": 1078, "status": ".", "names": ["Cyrillic_zhe"]}, {"keysym": 1751, "unicode": 1074, "status": ".", "names": ["Cyrillic_ve"]}, {"keysym": 1752, "unicode": 1100, "status": ".", "names": ["Cyrillic_softsign"]}, {"keysym": 1753, "unicode": 1099, "status": ".", "names": ["Cyrillic_yeru"]}, {"keysym": 1754, "unicode": 1079, "status": ".", "names": ["Cyrillic_ze"]}, {"keysym": 1755, "unicode": 1096, "status": ".", "names": ["Cyrillic_sha"]}, {"keysym": 1756, "unicode": 1101, "status": ".", "names": ["Cyrillic_e"]}, {"keysym": 1757, "unicode": 1097, "status": ".", "names": ["Cyrillic_shcha"]}, {"keysym": 1758, "unicode": 1095, "status": ".", "names": ["Cyrillic_che"]}, {"keysym": 1759, "unicode": 1098, "status": ".", "names": ["Cyrillic_hardsign"]}, {"keysym": 1760, "unicode": 1070, "status": ".", "names": ["Cyrillic_YU"]}, {"keysym": 1761, "unicode": 1040, "status": ".", "names": ["Cyrillic_A"]}, {"keysym": 1762, "unicode": 1041, "status": ".", "names": ["Cyrillic_BE"]}, {"keysym": 1763, "unicode": 1062, "status": ".", "names": ["Cyrillic_TSE"]}, {"keysym": 1764, "unicode": 1044, "status": ".", "names": ["Cyrillic_DE"]}, {"keysym": 1765, "unicode": 1045, "status": ".", "names": ["Cyrillic_IE"]}, {"keysym": 1766, "unicode": 1060, "status": ".", "names": ["Cyrillic_EF"]}, {"keysym": 1767, "unicode": 1043, "status": ".", "names": ["Cyrillic_GHE"]}, {"keysym": 1768, "unicode": 1061, "status": ".", "names": ["Cyrillic_HA"]}, {"keysym": 1769, "unicode": 1048, "status": ".", "names": ["Cyrillic_I"]}, {"keysym": 1770, "unicode": 1049, "status": ".", "names": ["Cyrillic_SHORTI"]}, {"keysym": 1771, "unicode": 1050, "status": ".", "names": ["Cyrillic_KA"]}, {"keysym": 1772, "unicode": 1051, "status": ".", "names": ["Cyrillic_EL"]}, {"keysym": 1773, "unicode": 1052, "status": ".", "names": ["Cyrillic_EM"]}, {"keysym": 1774, "unicode": 1053, "status": ".", "names": ["Cyrillic_EN"]}, {"keysym": 1775, "unicode": 1054, "status": ".", "names": ["Cyrillic_O"]}, {"keysym": 1776, "unicode": 1055, "status": ".", "names": ["Cyrillic_PE"]}, {"keysym": 1777, "unicode": 1071, "status": ".", "names": ["Cyrillic_YA"]}, {"keysym": 1778, "unicode": 1056, "status": ".", "names": ["Cyrillic_ER"]}, {"keysym": 1779, "unicode": 1057, "status": ".", "names": ["Cyrillic_ES"]}, {"keysym": 1780, "unicode": 1058, "status": ".", "names": ["Cyrillic_TE"]}, {"keysym": 1781, "unicode": 1059, "status": ".", "names": ["Cyrillic_U"]}, {"keysym": 1782, "unicode": 1046, "status": ".", "names": ["Cyrillic_ZHE"]}, {"keysym": 1783, "unicode": 1042, "status": ".", "names": ["Cyrillic_VE"]}, {"keysym": 1784, "unicode": 1068, "status": ".", "names": ["Cyrillic_SOFTSIGN"]}, {"keysym": 1785, "unicode": 1067, "status": ".", "names": ["Cyrillic_YERU"]}, {"keysym": 1786, "unicode": 1047, "status": ".", "names": ["Cyrillic_ZE"]}, {"keysym": 1787, "unicode": 1064, "status": ".", "names": ["Cyrillic_SHA"]}, {"keysym": 1788, "unicode": 1069, "status": ".", "names": ["Cyrillic_E"]}, {"keysym": 1789, "unicode": 1065, "status": ".", "names": ["Cyrillic_SHCHA"]}, {"keysym": 1790, "unicode": 1063, "status": ".", "names": ["Cyrillic_CHE"]}, {"keysym": 1791, "unicode": 1066, "status": ".", "names": ["Cyrillic_HARDSIGN"]}, {"keysym": 1953, "unicode": 902, "status": ".", "names": ["Greek_ALPHAaccent"]}, {"keysym": 1954, "unicode": 904, "status": ".", "names": ["Greek_EPSILONaccent"]}, {"keysym": 1955, "unicode": 905, "status": ".", "names": ["Greek_ETAaccent"]}, {"keysym": 1956, "unicode": 906, "status": ".", "names": ["Greek_IOTAaccent"]}, {"keysym": 1957, "unicode": 938, "status": ".", "names": ["Greek_IOTAdiaeresis"]}, {"keysym": 1959, "unicode": 908, "status": ".", "names": ["Greek_OMICRONaccent"]}, {"keysym": 1960, "unicode": 910, "status": ".", "names": ["Greek_UPSILONaccent"]}, {"keysym": 1961, "unicode": 939, "status": ".", "names": ["Greek_UPSILONdieresis"]}, {"keysym": 1963, "unicode": 911, "status": ".", "names": ["Greek_OMEGAaccent"]}, {"keysym": 1966, "unicode": 901, "status": ".", "names": ["Greek_accentdieresis"]}, {"keysym": 1967, "unicode": 8213, "status": ".", "names": ["Greek_horizbar"]}, {"keysym": 1969, "unicode": 940, "status": ".", "names": ["Greek_alphaaccent"]}, {"keysym": 1970, "unicode": 941, "status": ".", "names": ["Greek_epsilonaccent"]}, {"keysym": 1971, "unicode": 942, "status": ".", "names": ["Greek_etaaccent"]}, {"keysym": 1972, "unicode": 943, "status": ".", "names": ["Greek_iotaaccent"]}, {"keysym": 1973, "unicode": 970, "status": ".", "names": ["Greek_iotadieresis"]}, {"keysym": 1974, "unicode": 912, "status": ".", "names": ["Greek_iotaaccentdieresis"]}, {"keysym": 1975, "unicode": 972, "status": ".", "names": ["Greek_omicronaccent"]}, {"keysym": 1976, "unicode": 973, "status": ".", "names": ["Greek_upsilonaccent"]}, {"keysym": 1977, "unicode": 971, "status": ".", "names": ["Greek_upsilondieresis"]}, {"keysym": 1978, "unicode": 944, "status": ".", "names": ["Greek_upsilonaccentdieresis"]}, {"keysym": 1979, "unicode": 974, "status": ".", "names": ["Greek_omegaaccent"]}, {"keysym": 1985, "unicode": 913, "status": ".", "names": ["Greek_ALPHA"]}, {"keysym": 1986, "unicode": 914, "status": ".", "names": ["Greek_BETA"]}, {"keysym": 1987, "unicode": 915, "status": ".", "names": ["Greek_GAMMA"]}, {"keysym": 1988, "unicode": 916, "status": ".", "names": ["Greek_DELTA"]}, {"keysym": 1989, "unicode": 917, "status": ".", "names": ["Greek_EPSILON"]}, {"keysym": 1990, "unicode": 918, "status": ".", "names": ["Greek_ZETA"]}, {"keysym": 1991, "unicode": 919, "status": ".", "names": ["Greek_ETA"]}, {"keysym": 1992, "unicode": 920, "status": ".", "names": ["Greek_THETA"]}, {"keysym": 1993, "unicode": 921, "status": ".", "names": ["Greek_IOTA"]}, {"keysym": 1994, "unicode": 922, "status": ".", "names": ["Greek_KAPPA"]}, {"keysym": 1995, "unicode": 923, "status": ".", "names": ["Greek_LAMBDA", "Greek_LAMDA"]}, {"keysym": 1996, "unicode": 924, "status": ".", "names": ["Greek_MU"]}, {"keysym": 1997, "unicode": 925, "status": ".", "names": ["Greek_NU"]}, {"keysym": 1998, "unicode": 926, "status": ".", "names": ["Greek_XI"]}, {"keysym": 1999, "unicode": 927, "status": ".", "names": ["Greek_OMICRON"]}, {"keysym": 2000, "unicode": 928, "status": ".", "names": ["Greek_PI"]}, {"keysym": 2001, "unicode": 929, "status": ".", "names": ["Greek_RHO"]}, {"keysym": 2002, "unicode": 931, "status": ".", "names": ["Greek_SIGMA"]}, {"keysym": 2004, "unicode": 932, "status": ".", "names": ["Greek_TAU"]}, {"keysym": 2005, "unicode": 933, "status": ".", "names": ["Greek_UPSILON"]}, {"keysym": 2006, "unicode": 934, "status": ".", "names": ["Greek_PHI"]}, {"keysym": 2007, "unicode": 935, "status": ".", "names": ["Greek_CHI"]}, {"keysym": 2008, "unicode": 936, "status": ".", "names": ["Greek_PSI"]}, {"keysym": 2009, "unicode": 937, "status": ".", "names": ["Greek_OMEGA"]}, {"keysym": 2017, "unicode": 945, "status": ".", "names": ["Greek_alpha"]}, {"keysym": 2018, "unicode": 946, "status": ".", "names": ["Greek_beta"]}, {"keysym": 2019, "unicode": 947, "status": ".", "names": ["Greek_gamma"]}, {"keysym": 2020, "unicode": 948, "status": ".", "names": ["Greek_delta"]}, {"keysym": 2021, "unicode": 949, "status": ".", "names": ["Greek_epsilon"]}, {"keysym": 2022, "unicode": 950, "status": ".", "names": ["Greek_zeta"]}, {"keysym": 2023, "unicode": 951, "status": ".", "names": ["Greek_eta"]}, {"keysym": 2024, "unicode": 952, "status": ".", "names": ["Greek_theta"]}, {"keysym": 2025, "unicode": 953, "status": ".", "names": ["Greek_iota"]}, {"keysym": 2026, "unicode": 954, "status": ".", "names": ["Greek_kappa"]}, {"keysym": 2027, "unicode": 955, "status": ".", "names": ["Greek_lambda"]}, {"keysym": 2028, "unicode": 956, "status": ".", "names": ["Greek_mu"]}, {"keysym": 2029, "unicode": 957, "status": ".", "names": ["Greek_nu"]}, {"keysym": 2030, "unicode": 958, "status": ".", "names": ["Greek_xi"]}, {"keysym": 2031, "unicode": 959, "status": ".", "names": ["Greek_omicron"]}, {"keysym": 2032, "unicode": 960, "status": ".", "names": ["Greek_pi"]}, {"keysym": 2033, "unicode": 961, "status": ".", "names": ["Greek_rho"]}, {"keysym": 2034, "unicode": 963, "status": ".", "names": ["Greek_sigma"]}, {"keysym": 2035, "unicode": 962, "status": ".", "names": ["Greek_finalsmallsigma"]}, {"keysym": 2036, "unicode": 964, "status": ".", "names": ["Greek_tau"]}, {"keysym": 2037, "unicode": 965, "status": ".", "names": ["Greek_upsilon"]}, {"keysym": 2038, "unicode": 966, "status": ".", "names": ["Greek_phi"]}, {"keysym": 2039, "unicode": 967, "status": ".", "names": ["Greek_chi"]}, {"keysym": 2040, "unicode": 968, "status": ".", "names": ["Greek_psi"]}, {"keysym": 2041, "unicode": 969, "status": ".", "names": ["Greek_omega"]}, {"keysym": 2209, "unicode": 9143, "status": ".", "names": ["leftradical"]}, {"keysym": 2210, "unicode": 9484, "status": "d", "names": ["topleftradical"]}, {"keysym": 2211, "unicode": 9472, "status": "d", "names": ["horizconnector"]}, {"keysym": 2212, "unicode": 8992, "status": ".", "names": ["topintegral"]}, {"keysym": 2213, "unicode": 8993, "status": ".", "names": ["botintegral"]}, {"keysym": 2214, "unicode": 9474, "status": "d", "names": ["vertconnector"]}, {"keysym": 2215, "unicode": 9121, "status": ".", "names": ["topleftsqbracket"]}, {"keysym": 2216, "unicode": 9123, "status": ".", "names": ["botleftsqbracket"]}, {"keysym": 2217, "unicode": 9124, "status": ".", "names": ["toprightsqbracket"]}, {"keysym": 2218, "unicode": 9126, "status": ".", "names": ["botrightsqbracket"]}, {"keysym": 2219, "unicode": 9115, "status": ".", "names": ["topleftparens"]}, {"keysym": 2220, "unicode": 9117, "status": ".", "names": ["botleftparens"]}, {"keysym": 2221, "unicode": 9118, "status": ".", "names": ["toprightparens"]}, {"keysym": 2222, "unicode": 9120, "status": ".", "names": ["botrightparens"]}, {"keysym": 2223, "unicode": 9128, "status": ".", "names": ["leftmiddle<PERSON><PERSON><PERSON>ce"]}, {"keysym": 2224, "unicode": 9132, "status": ".", "names": ["rightmi<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"keysym": 2225, "unicode": 0, "status": "o", "names": ["topleftsummation"]}, {"keysym": 2226, "unicode": 0, "status": "o", "names": ["botleftsummation"]}, {"keysym": 2227, "unicode": 0, "status": "o", "names": ["topvertsummationconnector"]}, {"keysym": 2228, "unicode": 0, "status": "o", "names": ["botvertsummationconnector"]}, {"keysym": 2229, "unicode": 0, "status": "o", "names": ["toprightsummation"]}, {"keysym": 2230, "unicode": 0, "status": "o", "names": ["botrightsummation"]}, {"keysym": 2231, "unicode": 0, "status": "o", "names": ["rightmiddlesummation"]}, {"keysym": 2236, "unicode": 8804, "status": ".", "names": ["lessthanequal"]}, {"keysym": 2237, "unicode": 8800, "status": ".", "names": ["notequal"]}, {"keysym": 2238, "unicode": 8805, "status": ".", "names": ["greaterthanequal"]}, {"keysym": 2239, "unicode": 8747, "status": ".", "names": ["integral"]}, {"keysym": 2240, "unicode": 8756, "status": ".", "names": ["therefore"]}, {"keysym": 2241, "unicode": 8733, "status": ".", "names": ["variation"]}, {"keysym": 2242, "unicode": 8734, "status": ".", "names": ["infinity"]}, {"keysym": 2245, "unicode": 8711, "status": ".", "names": ["nabla"]}, {"keysym": 2248, "unicode": 8764, "status": ".", "names": ["approximate"]}, {"keysym": 2249, "unicode": 8771, "status": ".", "names": ["similarequal"]}, {"keysym": 2253, "unicode": 8660, "status": ".", "names": ["ifonlyif"]}, {"keysym": 2254, "unicode": 8658, "status": ".", "names": ["implies"]}, {"keysym": 2255, "unicode": 8801, "status": ".", "names": ["identical"]}, {"keysym": 2262, "unicode": 8730, "status": ".", "names": ["radical"]}, {"keysym": 2266, "unicode": 8834, "status": ".", "names": ["includedin"]}, {"keysym": 2267, "unicode": 8835, "status": ".", "names": ["includes"]}, {"keysym": 2268, "unicode": 8745, "status": ".", "names": ["intersection"]}, {"keysym": 2269, "unicode": 8746, "status": ".", "names": ["union"]}, {"keysym": 2270, "unicode": 8743, "status": ".", "names": ["logicaland"]}, {"keysym": 2271, "unicode": 8744, "status": ".", "names": ["logicalor"]}, {"keysym": 2287, "unicode": 8706, "status": ".", "names": ["partialderivative"]}, {"keysym": 2294, "unicode": 402, "status": ".", "names": ["function"]}, {"keysym": 2299, "unicode": 8592, "status": ".", "names": ["leftarrow"]}, {"keysym": 2300, "unicode": 8593, "status": ".", "names": ["uparrow"]}, {"keysym": 2301, "unicode": 8594, "status": ".", "names": ["rightarrow"]}, {"keysym": 2302, "unicode": 8595, "status": ".", "names": ["downarrow"]}, {"keysym": 2527, "unicode": 0, "status": "o", "names": ["blank"]}, {"keysym": 2528, "unicode": 9670, "status": ".", "names": ["<PERSON><PERSON><PERSON>"]}, {"keysym": 2529, "unicode": 9618, "status": ".", "names": ["checkerboard"]}, {"keysym": 2530, "unicode": 9225, "status": ".", "names": ["ht"]}, {"keysym": 2531, "unicode": 9228, "status": ".", "names": ["ff"]}, {"keysym": 2532, "unicode": 9229, "status": ".", "names": ["cr"]}, {"keysym": 2533, "unicode": 9226, "status": ".", "names": ["lf"]}, {"keysym": 2536, "unicode": 9252, "status": ".", "names": ["nl"]}, {"keysym": 2537, "unicode": 9227, "status": ".", "names": ["vt"]}, {"keysym": 2538, "unicode": 9496, "status": ".", "names": ["low<PERSON><PERSON>er"]}, {"keysym": 2539, "unicode": 9488, "status": ".", "names": ["uprightcorner"]}, {"keysym": 2540, "unicode": 9484, "status": ".", "names": ["upleftcorner"]}, {"keysym": 2541, "unicode": 9492, "status": ".", "names": ["lowleftcorner"]}, {"keysym": 2542, "unicode": 9532, "status": ".", "names": ["crossinglines"]}, {"keysym": 2543, "unicode": 9146, "status": ".", "names": ["horizlinescan1"]}, {"keysym": 2544, "unicode": 9147, "status": ".", "names": ["horizlinescan3"]}, {"keysym": 2545, "unicode": 9472, "status": ".", "names": ["horizlinescan5"]}, {"keysym": 2546, "unicode": 9148, "status": ".", "names": ["horizlinescan7"]}, {"keysym": 2547, "unicode": 9149, "status": ".", "names": ["horizlinescan9"]}, {"keysym": 2548, "unicode": 9500, "status": ".", "names": ["leftt"]}, {"keysym": 2549, "unicode": 9508, "status": ".", "names": ["rightt"]}, {"keysym": 2550, "unicode": 9524, "status": ".", "names": ["bott"]}, {"keysym": 2551, "unicode": 9516, "status": ".", "names": ["topt"]}, {"keysym": 2552, "unicode": 9474, "status": ".", "names": ["vertbar"]}, {"keysym": 2721, "unicode": 8195, "status": ".", "names": ["emspace"]}, {"keysym": 2722, "unicode": 8194, "status": ".", "names": ["enspace"]}, {"keysym": 2723, "unicode": 8196, "status": ".", "names": ["em3space"]}, {"keysym": 2724, "unicode": 8197, "status": ".", "names": ["em4space"]}, {"keysym": 2725, "unicode": 8199, "status": ".", "names": ["digitspace"]}, {"keysym": 2726, "unicode": 8200, "status": ".", "names": ["punctspace"]}, {"keysym": 2727, "unicode": 8201, "status": ".", "names": ["thinspace"]}, {"keysym": 2728, "unicode": 8202, "status": ".", "names": ["hairspace"]}, {"keysym": 2729, "unicode": 8212, "status": ".", "names": ["emdash"]}, {"keysym": 2730, "unicode": 8211, "status": ".", "names": ["endash"]}, {"keysym": 2732, "unicode": 9251, "status": "o", "names": ["signifblank"]}, {"keysym": 2734, "unicode": 8230, "status": ".", "names": ["ellipsis"]}, {"keysym": 2735, "unicode": 8229, "status": ".", "names": ["doubbaselinedot"]}, {"keysym": 2736, "unicode": 8531, "status": ".", "names": ["onethird"]}, {"keysym": 2737, "unicode": 8532, "status": ".", "names": ["twothirds"]}, {"keysym": 2738, "unicode": 8533, "status": ".", "names": ["onefifth"]}, {"keysym": 2739, "unicode": 8534, "status": ".", "names": ["twofifths"]}, {"keysym": 2740, "unicode": 8535, "status": ".", "names": ["threefifths"]}, {"keysym": 2741, "unicode": 8536, "status": ".", "names": ["fourfifths"]}, {"keysym": 2742, "unicode": 8537, "status": ".", "names": ["onesixth"]}, {"keysym": 2743, "unicode": 8538, "status": ".", "names": ["fivesixths"]}, {"keysym": 2744, "unicode": 8453, "status": ".", "names": ["careof"]}, {"keysym": 2747, "unicode": 8210, "status": ".", "names": ["figdash"]}, {"keysym": 2748, "unicode": 10216, "status": "o", "names": ["leftanglebracket"]}, {"keysym": 2749, "unicode": 46, "status": "o", "names": ["decimalpoint"]}, {"keysym": 2750, "unicode": 10217, "status": "o", "names": ["rightanglebracket"]}, {"keysym": 2751, "unicode": 0, "status": "o", "names": ["marker"]}, {"keysym": 2755, "unicode": 8539, "status": ".", "names": ["oneeighth"]}, {"keysym": 2756, "unicode": 8540, "status": ".", "names": ["threeeighths"]}, {"keysym": 2757, "unicode": 8541, "status": ".", "names": ["fiveeighths"]}, {"keysym": 2758, "unicode": 8542, "status": ".", "names": ["seveneighths"]}, {"keysym": 2761, "unicode": 8482, "status": ".", "names": ["trademark"]}, {"keysym": 2762, "unicode": 9747, "status": "o", "names": ["signaturemark"]}, {"keysym": 2763, "unicode": 0, "status": "o", "names": ["trademarkincircle"]}, {"keysym": 2764, "unicode": 9665, "status": "o", "names": ["leftopentriangle"]}, {"keysym": 2765, "unicode": 9655, "status": "o", "names": ["rightopentriangle"]}, {"keysym": 2766, "unicode": 9675, "status": "o", "names": ["emopencircle"]}, {"keysym": 2767, "unicode": 9647, "status": "o", "names": ["emopenrectangle"]}, {"keysym": 2768, "unicode": 8216, "status": ".", "names": ["leftsinglequotemark"]}, {"keysym": 2769, "unicode": 8217, "status": ".", "names": ["rightsinglequotemark"]}, {"keysym": 2770, "unicode": 8220, "status": ".", "names": ["leftdoublequotemark"]}, {"keysym": 2771, "unicode": 8221, "status": ".", "names": ["rightdoublequotemark"]}, {"keysym": 2772, "unicode": 8478, "status": ".", "names": ["prescription"]}, {"keysym": 2774, "unicode": 8242, "status": ".", "names": ["minutes"]}, {"keysym": 2775, "unicode": 8243, "status": ".", "names": ["seconds"]}, {"keysym": 2777, "unicode": 10013, "status": ".", "names": ["latincross"]}, {"keysym": 2778, "unicode": 0, "status": "o", "names": ["hexagram"]}, {"keysym": 2779, "unicode": 9644, "status": "o", "names": ["filledrectbullet"]}, {"keysym": 2780, "unicode": 9664, "status": "o", "names": ["filledlefttribullet"]}, {"keysym": 2781, "unicode": 9654, "status": "o", "names": ["filledrighttribullet"]}, {"keysym": 2782, "unicode": 9679, "status": "o", "names": ["emfilledcircle"]}, {"keysym": 2783, "unicode": 9646, "status": "o", "names": ["emfilledrect"]}, {"keysym": 2784, "unicode": 9702, "status": "o", "names": ["enopencircbullet"]}, {"keysym": 2785, "unicode": 9643, "status": "o", "names": ["enopensquarebullet"]}, {"keysym": 2786, "unicode": 9645, "status": "o", "names": ["openrectbullet"]}, {"keysym": 2787, "unicode": 9651, "status": "o", "names": ["opentribulletup"]}, {"keysym": 2788, "unicode": 9661, "status": "o", "names": ["opentribulletdown"]}, {"keysym": 2789, "unicode": 9734, "status": "o", "names": ["openstar"]}, {"keysym": 2790, "unicode": 8226, "status": "o", "names": ["enfilledcircbullet"]}, {"keysym": 2791, "unicode": 9642, "status": "o", "names": ["enfilledsqbullet"]}, {"keysym": 2792, "unicode": 9650, "status": "o", "names": ["filledtribulletup"]}, {"keysym": 2793, "unicode": 9660, "status": "o", "names": ["filledtribulletdown"]}, {"keysym": 2794, "unicode": 9756, "status": "o", "names": ["leftpointer"]}, {"keysym": 2795, "unicode": 9758, "status": "o", "names": ["rightpointer"]}, {"keysym": 2796, "unicode": 9827, "status": ".", "names": ["club"]}, {"keysym": 2797, "unicode": 9830, "status": ".", "names": ["diamond"]}, {"keysym": 2798, "unicode": 9829, "status": ".", "names": ["heart"]}, {"keysym": 2800, "unicode": 10016, "status": ".", "names": ["maltesecross"]}, {"keysym": 2801, "unicode": 8224, "status": ".", "names": ["dagger"]}, {"keysym": 2802, "unicode": 8225, "status": ".", "names": ["<PERSON><PERSON>"]}, {"keysym": 2803, "unicode": 10003, "status": ".", "names": ["checkmark"]}, {"keysym": 2804, "unicode": 10007, "status": ".", "names": ["ballotcross"]}, {"keysym": 2805, "unicode": 9839, "status": ".", "names": ["musicalsharp"]}, {"keysym": 2806, "unicode": 9837, "status": ".", "names": ["musicalflat"]}, {"keysym": 2807, "unicode": 9794, "status": ".", "names": ["malesymbol"]}, {"keysym": 2808, "unicode": 9792, "status": ".", "names": ["femalesymbol"]}, {"keysym": 2809, "unicode": 9742, "status": ".", "names": ["telephone"]}, {"keysym": 2810, "unicode": 8981, "status": ".", "names": ["telephonerecorder"]}, {"keysym": 2811, "unicode": 8471, "status": ".", "names": ["phonographcopyright"]}, {"keysym": 2812, "unicode": 8248, "status": ".", "names": ["caret"]}, {"keysym": 2813, "unicode": 8218, "status": ".", "names": ["singlelowquotemark"]}, {"keysym": 2814, "unicode": 8222, "status": ".", "names": ["doublelowquotemark"]}, {"keysym": 2815, "unicode": 0, "status": "o", "names": ["cursor"]}, {"keysym": 2979, "unicode": 60, "status": "d", "names": ["leftcaret"]}, {"keysym": 2982, "unicode": 62, "status": "d", "names": ["rightcaret"]}, {"keysym": 2984, "unicode": 8744, "status": "d", "names": ["downcaret"]}, {"keysym": 2985, "unicode": 8743, "status": "d", "names": ["upcaret"]}, {"keysym": 3008, "unicode": 175, "status": "d", "names": ["overbar"]}, {"keysym": 3010, "unicode": 8869, "status": ".", "names": ["downtack"]}, {"keysym": 3011, "unicode": 8745, "status": "d", "names": ["upshoe"]}, {"keysym": 3012, "unicode": 8970, "status": ".", "names": ["downstile"]}, {"keysym": 3014, "unicode": 95, "status": "d", "names": ["underbar"]}, {"keysym": 3018, "unicode": 8728, "status": ".", "names": ["jot"]}, {"keysym": 3020, "unicode": 9109, "status": ".", "names": ["quad"]}, {"keysym": 3022, "unicode": 8868, "status": ".", "names": ["uptack"]}, {"keysym": 3023, "unicode": 9675, "status": ".", "names": ["circle"]}, {"keysym": 3027, "unicode": 8968, "status": ".", "names": ["upstile"]}, {"keysym": 3030, "unicode": 8746, "status": "d", "names": ["downshoe"]}, {"keysym": 3032, "unicode": 8835, "status": "d", "names": ["rightshoe"]}, {"keysym": 3034, "unicode": 8834, "status": "d", "names": ["leftshoe"]}, {"keysym": 3036, "unicode": 8866, "status": ".", "names": ["lefttack"]}, {"keysym": 3068, "unicode": 8867, "status": ".", "names": ["righttack"]}, {"keysym": 3295, "unicode": 8215, "status": ".", "names": ["hebrew_doublelowline"]}, {"keysym": 3296, "unicode": 1488, "status": ".", "names": ["hebrew_aleph"]}, {"keysym": 3297, "unicode": 1489, "status": ".", "names": ["hebrew_bet", "hebrew_beth"]}, {"keysym": 3298, "unicode": 1490, "status": ".", "names": ["hebrew_gimel", "hebrew_gimmel"]}, {"keysym": 3299, "unicode": 1491, "status": ".", "names": ["hebrew_dalet", "hebrew_daleth"]}, {"keysym": 3300, "unicode": 1492, "status": ".", "names": ["hebrew_he"]}, {"keysym": 3301, "unicode": 1493, "status": ".", "names": ["hebrew_waw"]}, {"keysym": 3302, "unicode": 1494, "status": ".", "names": ["hebrew_zain", "hebrew_zayin"]}, {"keysym": 3303, "unicode": 1495, "status": ".", "names": ["hebrew_chet", "hebrew_het"]}, {"keysym": 3304, "unicode": 1496, "status": ".", "names": ["hebrew_tet", "hebrew_teth"]}, {"keysym": 3305, "unicode": 1497, "status": ".", "names": ["hebrew_yod"]}, {"keysym": 3306, "unicode": 1498, "status": ".", "names": ["hebrew_finalkaph"]}, {"keysym": 3307, "unicode": 1499, "status": ".", "names": ["hebrew_kaph"]}, {"keysym": 3308, "unicode": 1500, "status": ".", "names": ["hebrew_lamed"]}, {"keysym": 3309, "unicode": 1501, "status": ".", "names": ["hebrew_finalmem"]}, {"keysym": 3310, "unicode": 1502, "status": ".", "names": ["hebrew_mem"]}, {"keysym": 3311, "unicode": 1503, "status": ".", "names": ["hebrew_finalnun"]}, {"keysym": 3312, "unicode": 1504, "status": ".", "names": ["hebrew_nun"]}, {"keysym": 3313, "unicode": 1505, "status": ".", "names": ["hebrew_samech", "hebrew_samekh"]}, {"keysym": 3314, "unicode": 1506, "status": ".", "names": ["hebrew_ayin"]}, {"keysym": 3315, "unicode": 1507, "status": ".", "names": ["hebrew_finalpe"]}, {"keysym": 3316, "unicode": 1508, "status": ".", "names": ["hebrew_pe"]}, {"keysym": 3317, "unicode": 1509, "status": ".", "names": ["hebrew_finalzade", "hebrew_final<PERSON>i"]}, {"keysym": 3318, "unicode": 1510, "status": ".", "names": ["hebrew_zade", "hebrew_zadi"]}, {"keysym": 3319, "unicode": 1511, "status": ".", "names": ["hebrew_kuf", "hebrew_qoph"]}, {"keysym": 3320, "unicode": 1512, "status": ".", "names": ["hebrew_resh"]}, {"keysym": 3321, "unicode": 1513, "status": ".", "names": ["hebrew_shin"]}, {"keysym": 3322, "unicode": 1514, "status": ".", "names": ["hebrew_taf", "hebrew_taw"]}, {"keysym": 3489, "unicode": 3585, "status": ".", "names": ["Thai_kokai"]}, {"keysym": 3490, "unicode": 3586, "status": ".", "names": ["Thai_khokhai"]}, {"keysym": 3491, "unicode": 3587, "status": ".", "names": ["Thai_k<PERSON>khuat"]}, {"keysym": 3492, "unicode": 3588, "status": ".", "names": ["Thai_khokhwai"]}, {"keysym": 3493, "unicode": 3589, "status": ".", "names": ["Thai_khokhon"]}, {"keysym": 3494, "unicode": 3590, "status": ".", "names": ["Thai_khorakhang"]}, {"keysym": 3495, "unicode": 3591, "status": ".", "names": ["Thai_ngongu"]}, {"keysym": 3496, "unicode": 3592, "status": ".", "names": ["Thai_chochan"]}, {"keysym": 3497, "unicode": 3593, "status": ".", "names": ["Thai_choching"]}, {"keysym": 3498, "unicode": 3594, "status": ".", "names": ["Thai_chochang"]}, {"keysym": 3499, "unicode": 3595, "status": ".", "names": ["Thai_soso"]}, {"keysym": 3500, "unicode": 3596, "status": ".", "names": ["Thai_chochoe"]}, {"keysym": 3501, "unicode": 3597, "status": ".", "names": ["Thai_yoying"]}, {"keysym": 3502, "unicode": 3598, "status": ".", "names": ["Thai_dochada"]}, {"keysym": 3503, "unicode": 3599, "status": ".", "names": ["Thai_topatak"]}, {"keysym": 3504, "unicode": 3600, "status": ".", "names": ["Thai_thothan"]}, {"keysym": 3505, "unicode": 3601, "status": ".", "names": ["<PERSON>_th<PERSON><PERSON><PERSON><PERSON>"]}, {"keysym": 3506, "unicode": 3602, "status": ".", "names": ["<PERSON>_thop<PERSON><PERSON>o"]}, {"keysym": 3507, "unicode": 3603, "status": ".", "names": ["Thai_nonen"]}, {"keysym": 3508, "unicode": 3604, "status": ".", "names": ["<PERSON>_dodek"]}, {"keysym": 3509, "unicode": 3605, "status": ".", "names": ["Thai_totao"]}, {"keysym": 3510, "unicode": 3606, "status": ".", "names": ["Thai_thothung"]}, {"keysym": 3511, "unicode": 3607, "status": ".", "names": ["<PERSON>_thoth<PERSON>"]}, {"keysym": 3512, "unicode": 3608, "status": ".", "names": ["Thai_thothong"]}, {"keysym": 3513, "unicode": 3609, "status": ".", "names": ["<PERSON>_nonu"]}, {"keysym": 3514, "unicode": 3610, "status": ".", "names": ["Thai_bobaimai"]}, {"keysym": 3515, "unicode": 3611, "status": ".", "names": ["Thai_popla"]}, {"keysym": 3516, "unicode": 3612, "status": ".", "names": ["Thai_phophung"]}, {"keysym": 3517, "unicode": 3613, "status": ".", "names": ["Thai_fofa"]}, {"keysym": 3518, "unicode": 3614, "status": ".", "names": ["<PERSON>_phophan"]}, {"keysym": 3519, "unicode": 3615, "status": ".", "names": ["Thai_fofan"]}, {"keysym": 3520, "unicode": 3616, "status": ".", "names": ["Thai_phosamphao"]}, {"keysym": 3521, "unicode": 3617, "status": ".", "names": ["Thai_moma"]}, {"keysym": 3522, "unicode": 3618, "status": ".", "names": ["Thai_yoyak"]}, {"keysym": 3523, "unicode": 3619, "status": ".", "names": ["Thai_rorua"]}, {"keysym": 3524, "unicode": 3620, "status": ".", "names": ["Thai_ru"]}, {"keysym": 3525, "unicode": 3621, "status": ".", "names": ["Thai_loling"]}, {"keysym": 3526, "unicode": 3622, "status": ".", "names": ["Thai_lu"]}, {"keysym": 3527, "unicode": 3623, "status": ".", "names": ["Thai_wowaen"]}, {"keysym": 3528, "unicode": 3624, "status": ".", "names": ["Thai_sosala"]}, {"keysym": 3529, "unicode": 3625, "status": ".", "names": ["Thai_sorusi"]}, {"keysym": 3530, "unicode": 3626, "status": ".", "names": ["Thai_sosua"]}, {"keysym": 3531, "unicode": 3627, "status": ".", "names": ["Thai_hohip"]}, {"keysym": 3532, "unicode": 3628, "status": ".", "names": ["Thai_lochula"]}, {"keysym": 3533, "unicode": 3629, "status": ".", "names": ["Thai_oang"]}, {"keysym": 3534, "unicode": 3630, "status": ".", "names": ["Thai_honokhuk"]}, {"keysym": 3535, "unicode": 3631, "status": ".", "names": ["Thai_paiyannoi"]}, {"keysym": 3536, "unicode": 3632, "status": ".", "names": ["Thai_saraa"]}, {"keysym": 3537, "unicode": 3633, "status": ".", "names": ["Thai_maihanakat"]}, {"keysym": 3538, "unicode": 3634, "status": ".", "names": ["Thai_saraaa"]}, {"keysym": 3539, "unicode": 3635, "status": ".", "names": ["Thai_saraam"]}, {"keysym": 3540, "unicode": 3636, "status": ".", "names": ["Thai_sarai"]}, {"keysym": 3541, "unicode": 3637, "status": ".", "names": ["Thai_saraii"]}, {"keysym": 3542, "unicode": 3638, "status": ".", "names": ["Thai_saraue"]}, {"keysym": 3543, "unicode": 3639, "status": ".", "names": ["Thai_sarauee"]}, {"keysym": 3544, "unicode": 3640, "status": ".", "names": ["Thai_sarau"]}, {"keysym": 3545, "unicode": 3641, "status": ".", "names": ["Thai_sarauu"]}, {"keysym": 3546, "unicode": 3642, "status": ".", "names": ["Thai_phinthu"]}, {"keysym": 3550, "unicode": 0, "status": "o", "names": ["Thai_maihanakat_maitho"]}, {"keysym": 3551, "unicode": 3647, "status": ".", "names": ["Thai_baht"]}, {"keysym": 3552, "unicode": 3648, "status": ".", "names": ["Thai_sarae"]}, {"keysym": 3553, "unicode": 3649, "status": ".", "names": ["Thai_saraae"]}, {"keysym": 3554, "unicode": 3650, "status": ".", "names": ["Thai_sarao"]}, {"keysym": 3555, "unicode": 3651, "status": ".", "names": ["<PERSON>_sa<PERSON><PERSON><PERSON><PERSON>uan"]}, {"keysym": 3556, "unicode": 3652, "status": ".", "names": ["<PERSON>_sa<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"keysym": 3557, "unicode": 3653, "status": ".", "names": ["Thai_lakkhangyao"]}, {"keysym": 3558, "unicode": 3654, "status": ".", "names": ["<PERSON>_maiyamok"]}, {"keysym": 3559, "unicode": 3655, "status": ".", "names": ["<PERSON>_maitaikhu"]}, {"keysym": 3560, "unicode": 3656, "status": ".", "names": ["<PERSON>_maiek"]}, {"keysym": 3561, "unicode": 3657, "status": ".", "names": ["Thai_maitho"]}, {"keysym": 3562, "unicode": 3658, "status": ".", "names": ["Thai_maitri"]}, {"keysym": 3563, "unicode": 3659, "status": ".", "names": ["<PERSON>_maichattawa"]}, {"keysym": 3564, "unicode": 3660, "status": ".", "names": ["Thai_thantha<PERSON>t"]}, {"keysym": 3565, "unicode": 3661, "status": ".", "names": ["Thai_nikhahit"]}, {"keysym": 3568, "unicode": 3664, "status": ".", "names": ["Thai_leksun"]}, {"keysym": 3569, "unicode": 3665, "status": ".", "names": ["Thai_leknung"]}, {"keysym": 3570, "unicode": 3666, "status": ".", "names": ["Thai_leksong"]}, {"keysym": 3571, "unicode": 3667, "status": ".", "names": ["<PERSON>_leksam"]}, {"keysym": 3572, "unicode": 3668, "status": ".", "names": ["Thai_leksi"]}, {"keysym": 3573, "unicode": 3669, "status": ".", "names": ["Thai_lekha"]}, {"keysym": 3574, "unicode": 3670, "status": ".", "names": ["Thai_lekhok"]}, {"keysym": 3575, "unicode": 3671, "status": ".", "names": ["Thai_lekchet"]}, {"keysym": 3576, "unicode": 3672, "status": ".", "names": ["Thai_lekpaet"]}, {"keysym": 3577, "unicode": 3673, "status": ".", "names": ["Thai_lekkao"]}, {"keysym": 3745, "unicode": 12593, "status": "f", "names": ["Hangul_Kiyeog"]}, {"keysym": 3746, "unicode": 12594, "status": "f", "names": ["Hangul_SsangKiyeog"]}, {"keysym": 3747, "unicode": 12595, "status": "f", "names": ["Hangul_KiyeogSios"]}, {"keysym": 3748, "unicode": 12596, "status": "f", "names": ["Hangul_Nieun"]}, {"keysym": 3749, "unicode": 12597, "status": "f", "names": ["Hangul_Ni<PERSON><PERSON><PERSON><PERSON>j"]}, {"keysym": 3750, "unicode": 12598, "status": "f", "names": ["Hangul_NieunHieuh"]}, {"keysym": 3751, "unicode": 12599, "status": "f", "names": ["Hangul_Dikeud"]}, {"keysym": 3752, "unicode": 12600, "status": "f", "names": ["Hangul_Ssang<PERSON>ud"]}, {"keysym": 3753, "unicode": 12601, "status": "f", "names": ["Hangul_Rieul"]}, {"keysym": 3754, "unicode": 12602, "status": "f", "names": ["Hangul_RieulKiyeog"]}, {"keysym": 3755, "unicode": 12603, "status": "f", "names": ["Hangul_RieulMieum"]}, {"keysym": 3756, "unicode": 12604, "status": "f", "names": ["Hangul_R<PERSON><PERSON>b"]}, {"keysym": 3757, "unicode": 12605, "status": "f", "names": ["Hangul_RieulSios"]}, {"keysym": 3758, "unicode": 12606, "status": "f", "names": ["Hangul_Rieul<PERSON>ieut"]}, {"keysym": 3759, "unicode": 12607, "status": "f", "names": ["Hangul_R<PERSON>l<PERSON><PERSON>euf"]}, {"keysym": 3760, "unicode": 12608, "status": "f", "names": ["Hangul_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"keysym": 3761, "unicode": 12609, "status": "f", "names": ["Hangul_Mieum"]}, {"keysym": 3762, "unicode": 12610, "status": "f", "names": ["Hangul_Pieub"]}, {"keysym": 3763, "unicode": 12611, "status": "f", "names": ["Hangul_Ssang<PERSON>ieub"]}, {"keysym": 3764, "unicode": 12612, "status": "f", "names": ["Hangul_PieubSios"]}, {"keysym": 3765, "unicode": 12613, "status": "f", "names": ["Hangul_Sios"]}, {"keysym": 3766, "unicode": 12614, "status": "f", "names": ["Hangul_SsangSios"]}, {"keysym": 3767, "unicode": 12615, "status": "f", "names": ["Hangul_Ieung"]}, {"keysym": 3768, "unicode": 12616, "status": "f", "names": ["<PERSON>_<PERSON><PERSON><PERSON>"]}, {"keysym": 3769, "unicode": 12617, "status": "f", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"keysym": 3770, "unicode": 12618, "status": "f", "names": ["Hangul_<PERSON><PERSON><PERSON>"]}, {"keysym": 3771, "unicode": 12619, "status": "f", "names": ["Hangul_Khieuq"]}, {"keysym": 3772, "unicode": 12620, "status": "f", "names": ["Hangul_Tieut"]}, {"keysym": 3773, "unicode": 12621, "status": "f", "names": ["Hangul_Phieuf"]}, {"keysym": 3774, "unicode": 12622, "status": "f", "names": ["<PERSON>_<PERSON><PERSON>h"]}, {"keysym": 3775, "unicode": 12623, "status": "f", "names": ["Hangul_A"]}, {"keysym": 3776, "unicode": 12624, "status": "f", "names": ["Hangul_AE"]}, {"keysym": 3777, "unicode": 12625, "status": "f", "names": ["Hangul_YA"]}, {"keysym": 3778, "unicode": 12626, "status": "f", "names": ["Hangul_YAE"]}, {"keysym": 3779, "unicode": 12627, "status": "f", "names": ["Hangul_EO"]}, {"keysym": 3780, "unicode": 12628, "status": "f", "names": ["Hangul_E"]}, {"keysym": 3781, "unicode": 12629, "status": "f", "names": ["Hangul_YEO"]}, {"keysym": 3782, "unicode": 12630, "status": "f", "names": ["Hangul_YE"]}, {"keysym": 3783, "unicode": 12631, "status": "f", "names": ["Hangul_O"]}, {"keysym": 3784, "unicode": 12632, "status": "f", "names": ["Hangul_WA"]}, {"keysym": 3785, "unicode": 12633, "status": "f", "names": ["Hangul_WAE"]}, {"keysym": 3786, "unicode": 12634, "status": "f", "names": ["Hangul_OE"]}, {"keysym": 3787, "unicode": 12635, "status": "f", "names": ["Hangul_YO"]}, {"keysym": 3788, "unicode": 12636, "status": "f", "names": ["Hangul_U"]}, {"keysym": 3789, "unicode": 12637, "status": "f", "names": ["Hangul_WEO"]}, {"keysym": 3790, "unicode": 12638, "status": "f", "names": ["Hangul_WE"]}, {"keysym": 3791, "unicode": 12639, "status": "f", "names": ["Hangul_WI"]}, {"keysym": 3792, "unicode": 12640, "status": "f", "names": ["Hangul_YU"]}, {"keysym": 3793, "unicode": 12641, "status": "f", "names": ["Hangul_EU"]}, {"keysym": 3794, "unicode": 12642, "status": "f", "names": ["Hangul_YI"]}, {"keysym": 3795, "unicode": 12643, "status": "f", "names": ["Hangul_I"]}, {"keysym": 3796, "unicode": 4520, "status": "f", "names": ["Hangul_J_Kiyeog"]}, {"keysym": 3797, "unicode": 4521, "status": "f", "names": ["Hangul_J_SsangKiyeog"]}, {"keysym": 3798, "unicode": 4522, "status": "f", "names": ["Hangul_J_KiyeogSios"]}, {"keysym": 3799, "unicode": 4523, "status": "f", "names": ["Hangul_J_Nieun"]}, {"keysym": 3800, "unicode": 4524, "status": "f", "names": ["Hangul_J_NieunJ<PERSON>j"]}, {"keysym": 3801, "unicode": 4525, "status": "f", "names": ["Hangul_J_NieunHieuh"]}, {"keysym": 3802, "unicode": 4526, "status": "f", "names": ["Hangul_J_Dikeud"]}, {"keysym": 3803, "unicode": 4527, "status": "f", "names": ["<PERSON>_<PERSON>_Rieul"]}, {"keysym": 3804, "unicode": 4528, "status": "f", "names": ["Hangul_J_RieulKiyeog"]}, {"keysym": 3805, "unicode": 4529, "status": "f", "names": ["Hangul_J_RieulMieum"]}, {"keysym": 3806, "unicode": 4530, "status": "f", "names": ["Hangul_<PERSON>_<PERSON><PERSON>"]}, {"keysym": 3807, "unicode": 4531, "status": "f", "names": ["Hangul_J_RieulSios"]}, {"keysym": 3808, "unicode": 4532, "status": "f", "names": ["Hangul_J_R<PERSON>t"]}, {"keysym": 3809, "unicode": 4533, "status": "f", "names": ["Hangul_J_R<PERSON><PERSON><PERSON>f"]}, {"keysym": 3810, "unicode": 4534, "status": "f", "names": ["<PERSON>_<PERSON>_<PERSON><PERSON><PERSON><PERSON>"]}, {"keysym": 3811, "unicode": 4535, "status": "f", "names": ["Hangul_J_Mieum"]}, {"keysym": 3812, "unicode": 4536, "status": "f", "names": ["<PERSON>_<PERSON>_Pieub"]}, {"keysym": 3813, "unicode": 4537, "status": "f", "names": ["Hangul_J_PieubSios"]}, {"keysym": 3814, "unicode": 4538, "status": "f", "names": ["Hangul_J_Sios"]}, {"keysym": 3815, "unicode": 4539, "status": "f", "names": ["Hangul_J_SsangSios"]}, {"keysym": 3816, "unicode": 4540, "status": "f", "names": ["Hangul_J_Ieung"]}, {"keysym": 3817, "unicode": 4541, "status": "f", "names": ["<PERSON>_<PERSON>_Jieuj"]}, {"keysym": 3818, "unicode": 4542, "status": "f", "names": ["<PERSON>_<PERSON>_<PERSON>"]}, {"keysym": 3819, "unicode": 4543, "status": "f", "names": ["Hangul_J_Khieuq"]}, {"keysym": 3820, "unicode": 4544, "status": "f", "names": ["Hangul_J_T<PERSON>t"]}, {"keysym": 3821, "unicode": 4545, "status": "f", "names": ["<PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"keysym": 3822, "unicode": 4546, "status": "f", "names": ["<PERSON>_<PERSON>_<PERSON>h"]}, {"keysym": 3823, "unicode": 12653, "status": "f", "names": ["Hangul_RieulY<PERSON>in<PERSON>ieuh"]}, {"keysym": 3824, "unicode": 12657, "status": "f", "names": ["Hangul_SunkyeongeumMieum"]}, {"keysym": 3825, "unicode": 12664, "status": "f", "names": ["Hangul_SunkyeongeumPieub"]}, {"keysym": 3826, "unicode": 12671, "status": "f", "names": ["Hangul_PanSios"]}, {"keysym": 3827, "unicode": 12673, "status": "f", "names": ["Hangul_KkogjiDalrinIeung"]}, {"keysym": 3828, "unicode": 12676, "status": "f", "names": ["Hangul_SunkyeongeumPhieuf"]}, {"keysym": 3829, "unicode": 12678, "status": "f", "names": ["Hangul_<PERSON><PERSON><PERSON><PERSON>"]}, {"keysym": 3830, "unicode": 12685, "status": "f", "names": ["Hangul_AraeA"]}, {"keysym": 3831, "unicode": 12686, "status": "f", "names": ["Hangul_AraeAE"]}, {"keysym": 3832, "unicode": 4587, "status": "f", "names": ["Hangul_J_PanSios"]}, {"keysym": 3833, "unicode": 4592, "status": "f", "names": ["Hangul_J_KkogjiDalrinIeung"]}, {"keysym": 3834, "unicode": 4601, "status": "f", "names": ["<PERSON>_J_<PERSON><PERSON><PERSON>"]}, {"keysym": 3839, "unicode": 8361, "status": "o", "names": ["Korean_Won"]}, {"keysym": 5052, "unicode": 338, "status": ".", "names": ["OE"]}, {"keysym": 5053, "unicode": 339, "status": ".", "names": ["oe"]}, {"keysym": 5054, "unicode": 376, "status": ".", "names": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"keysym": 8352, "unicode": 8352, "status": "u", "names": ["EcuSign"]}, {"keysym": 8353, "unicode": 8353, "status": "u", "names": ["ColonSign"]}, {"keysym": 8354, "unicode": 8354, "status": "u", "names": ["CruzeiroSign"]}, {"keysym": 8355, "unicode": 8355, "status": "u", "names": ["FFrancSign"]}, {"keysym": 8356, "unicode": 8356, "status": "u", "names": ["LiraSign"]}, {"keysym": 8357, "unicode": 8357, "status": "u", "names": ["MillSign"]}, {"keysym": 8358, "unicode": 8358, "status": "u", "names": ["NairaSign"]}, {"keysym": 8359, "unicode": 8359, "status": "u", "names": ["PesetaSign"]}, {"keysym": 8360, "unicode": 8360, "status": "u", "names": ["RupeeSign"]}, {"keysym": 8361, "unicode": 8361, "status": "u", "names": ["WonSign"]}, {"keysym": 8362, "unicode": 8362, "status": "u", "names": ["NewSheqelSign"]}, {"keysym": 8363, "unicode": 8363, "status": "u", "names": ["DongSign"]}, {"keysym": 8364, "unicode": 8364, "status": ".", "names": ["EuroSign"]}, {"keysym": 64769, "unicode": 0, "status": "f", "names": ["3270_Duplicate"]}, {"keysym": 64770, "unicode": 0, "status": "f", "names": ["3270_FieldMark"]}, {"keysym": 64771, "unicode": 0, "status": "f", "names": ["3270_Right2"]}, {"keysym": 64772, "unicode": 0, "status": "f", "names": ["3270_Left2"]}, {"keysym": 64773, "unicode": 0, "status": "f", "names": ["3270_BackTab"]}, {"keysym": 64774, "unicode": 0, "status": "f", "names": ["3270_EraseEOF"]}, {"keysym": 64775, "unicode": 0, "status": "f", "names": ["3270_EraseInput"]}, {"keysym": 64776, "unicode": 0, "status": "f", "names": ["3270_Reset"]}, {"keysym": 64777, "unicode": 0, "status": "f", "names": ["3270_Quit"]}, {"keysym": 64778, "unicode": 0, "status": "f", "names": ["3270_PA1"]}, {"keysym": 64779, "unicode": 0, "status": "f", "names": ["3270_PA2"]}, {"keysym": 64780, "unicode": 0, "status": "f", "names": ["3270_PA3"]}, {"keysym": 64781, "unicode": 0, "status": "f", "names": ["3270_Test"]}, {"keysym": 64782, "unicode": 0, "status": "f", "names": ["3270_Attn"]}, {"keysym": 64783, "unicode": 0, "status": "f", "names": ["3270_CursorBlink"]}, {"keysym": 64784, "unicode": 0, "status": "f", "names": ["3270_AltCursor"]}, {"keysym": 64785, "unicode": 0, "status": "f", "names": ["3270_KeyClick"]}, {"keysym": 64786, "unicode": 0, "status": "f", "names": ["3270_Jump"]}, {"keysym": 64787, "unicode": 0, "status": "f", "names": ["3270_Ident"]}, {"keysym": 64788, "unicode": 0, "status": "f", "names": ["3270_Rule"]}, {"keysym": 64789, "unicode": 0, "status": "f", "names": ["3270_Copy"]}, {"keysym": 64790, "unicode": 0, "status": "f", "names": ["3270_Play"]}, {"keysym": 64791, "unicode": 0, "status": "f", "names": ["3270_Setup"]}, {"keysym": 64792, "unicode": 0, "status": "f", "names": ["3270_Record"]}, {"keysym": 64793, "unicode": 0, "status": "f", "names": ["3270_ChangeScreen"]}, {"keysym": 64794, "unicode": 0, "status": "f", "names": ["3270_DeleteWord"]}, {"keysym": 64795, "unicode": 0, "status": "f", "names": ["3270_ExSelect"]}, {"keysym": 64796, "unicode": 0, "status": "f", "names": ["3270_CursorSelect"]}, {"keysym": 64797, "unicode": 0, "status": "f", "names": ["3270_PrintScreen"]}, {"keysym": 64798, "unicode": 0, "status": "f", "names": ["3270_Enter"]}, {"keysym": 65025, "unicode": 0, "status": "f", "names": ["ISO_Lock"]}, {"keysym": 65026, "unicode": 0, "status": "f", "names": ["ISO_Level2_Latch"]}, {"keysym": 65027, "unicode": 0, "status": "f", "names": ["ISO_Level3_Shift"]}, {"keysym": 65028, "unicode": 0, "status": "f", "names": ["ISO_Level3_Latch"]}, {"keysym": 65029, "unicode": 0, "status": "f", "names": ["ISO_Level3_Lock"]}, {"keysym": 65030, "unicode": 0, "status": "f", "names": ["ISO_Group_Latch"]}, {"keysym": 65031, "unicode": 0, "status": "f", "names": ["ISO_Group_Lock"]}, {"keysym": 65032, "unicode": 0, "status": "f", "names": ["ISO_Next_Group"]}, {"keysym": 65033, "unicode": 0, "status": "f", "names": ["ISO_Next_Group_Lock"]}, {"keysym": 65034, "unicode": 0, "status": "f", "names": ["ISO_Prev_Group"]}, {"keysym": 65035, "unicode": 0, "status": "f", "names": ["ISO_Prev_Group_Lock"]}, {"keysym": 65036, "unicode": 0, "status": "f", "names": ["ISO_First_Group"]}, {"keysym": 65037, "unicode": 0, "status": "f", "names": ["ISO_First_Group_Lock"]}, {"keysym": 65038, "unicode": 0, "status": "f", "names": ["ISO_Last_Group"]}, {"keysym": 65039, "unicode": 0, "status": "f", "names": ["ISO_Last_Group_Lock"]}, {"keysym": 65056, "unicode": 0, "status": "f", "names": ["ISO_Left_Tab"]}, {"keysym": 65057, "unicode": 0, "status": "f", "names": ["ISO_Move_Line_Up"]}, {"keysym": 65058, "unicode": 0, "status": "f", "names": ["ISO_Move_Line_Down"]}, {"keysym": 65059, "unicode": 0, "status": "f", "names": ["ISO_Partial_Line_Up"]}, {"keysym": 65060, "unicode": 0, "status": "f", "names": ["ISO_Partial_Line_Down"]}, {"keysym": 65061, "unicode": 0, "status": "f", "names": ["ISO_Partial_Space_Left"]}, {"keysym": 65062, "unicode": 0, "status": "f", "names": ["ISO_Partial_Space_Right"]}, {"keysym": 65063, "unicode": 0, "status": "f", "names": ["ISO_Set_Margin_Left"]}, {"keysym": 65064, "unicode": 0, "status": "f", "names": ["ISO_Set_Margin_Right"]}, {"keysym": 65065, "unicode": 0, "status": "f", "names": ["ISO_Release_Margin_Left"]}, {"keysym": 65066, "unicode": 0, "status": "f", "names": ["ISO_Release_Margin_Right"]}, {"keysym": 65067, "unicode": 0, "status": "f", "names": ["ISO_Release_Both_Margins"]}, {"keysym": 65068, "unicode": 0, "status": "f", "names": ["ISO_Fast_Cursor_Left"]}, {"keysym": 65069, "unicode": 0, "status": "f", "names": ["ISO_Fast_Cursor_Right"]}, {"keysym": 65070, "unicode": 0, "status": "f", "names": ["ISO_Fast_Cursor_Up"]}, {"keysym": 65071, "unicode": 0, "status": "f", "names": ["ISO_Fast_Cursor_Down"]}, {"keysym": 65072, "unicode": 0, "status": "f", "names": ["ISO_Continuous_Underline"]}, {"keysym": 65073, "unicode": 0, "status": "f", "names": ["ISO_Discontinuous_Underline"]}, {"keysym": 65074, "unicode": 0, "status": "f", "names": ["ISO_Emphasize"]}, {"keysym": 65075, "unicode": 0, "status": "f", "names": ["ISO_Center_Object"]}, {"keysym": 65076, "unicode": 0, "status": "f", "names": ["ISO_Enter"]}, {"keysym": 65104, "unicode": 768, "status": "f", "names": ["dead_grave"]}, {"keysym": 65105, "unicode": 769, "status": "f", "names": ["dead_acute"]}, {"keysym": 65106, "unicode": 770, "status": "f", "names": ["dead_circumflex"]}, {"keysym": 65107, "unicode": 771, "status": "f", "names": ["dead_tilde"]}, {"keysym": 65108, "unicode": 772, "status": "f", "names": ["dead_macron"]}, {"keysym": 65109, "unicode": 774, "status": "f", "names": ["dead_breve"]}, {"keysym": 65110, "unicode": 775, "status": "f", "names": ["dead_abovedot"]}, {"keysym": 65111, "unicode": 776, "status": "f", "names": ["dead_diaeresis"]}, {"keysym": 65112, "unicode": 778, "status": "f", "names": ["dead_abovering"]}, {"keysym": 65113, "unicode": 779, "status": "f", "names": ["dead_doubleacute"]}, {"keysym": 65114, "unicode": 780, "status": "f", "names": ["dead_caron"]}, {"keysym": 65115, "unicode": 807, "status": "f", "names": ["dead_cedilla"]}, {"keysym": 65116, "unicode": 808, "status": "f", "names": ["dead_ogonek"]}, {"keysym": 65117, "unicode": 837, "status": "f", "names": ["dead_iota"]}, {"keysym": 65118, "unicode": 12441, "status": "f", "names": ["dead_voiced_sound"]}, {"keysym": 65119, "unicode": 12442, "status": "f", "names": ["dead_semivoiced_sound"]}, {"keysym": 65136, "unicode": 0, "status": "f", "names": ["AccessX_Enable"]}, {"keysym": 65137, "unicode": 0, "status": "f", "names": ["AccessX_Feedback_Enable"]}, {"keysym": 65138, "unicode": 0, "status": "f", "names": ["RepeatKeys_Enable"]}, {"keysym": 65139, "unicode": 0, "status": "f", "names": ["SlowKeys_Enable"]}, {"keysym": 65140, "unicode": 0, "status": "f", "names": ["BounceKeys_Enable"]}, {"keysym": 65141, "unicode": 0, "status": "f", "names": ["StickyKeys_Enable"]}, {"keysym": 65142, "unicode": 0, "status": "f", "names": ["MouseKeys_Enable"]}, {"keysym": 65143, "unicode": 0, "status": "f", "names": ["Mouse<PERSON><PERSON>s_Accel_Enable"]}, {"keysym": 65144, "unicode": 0, "status": "f", "names": ["Overlay1_Enable"]}, {"keysym": 65145, "unicode": 0, "status": "f", "names": ["Overlay2_Enable"]}, {"keysym": 65146, "unicode": 0, "status": "f", "names": ["AudibleBell_Enable"]}, {"keysym": 65232, "unicode": 0, "status": "f", "names": ["First_Virtual_Screen"]}, {"keysym": 65233, "unicode": 0, "status": "f", "names": ["Prev_Virtual_Screen"]}, {"keysym": 65234, "unicode": 0, "status": "f", "names": ["Next_Virtual_Screen"]}, {"keysym": 65236, "unicode": 0, "status": "f", "names": ["Last_Virtual_Screen"]}, {"keysym": 65237, "unicode": 0, "status": "f", "names": ["Terminate_Server"]}, {"keysym": 65248, "unicode": 0, "status": "f", "names": ["Pointer_Left"]}, {"keysym": 65249, "unicode": 0, "status": "f", "names": ["Pointer_Right"]}, {"keysym": 65250, "unicode": 0, "status": "f", "names": ["Pointer_Up"]}, {"keysym": 65251, "unicode": 0, "status": "f", "names": ["Pointer_Down"]}, {"keysym": 65252, "unicode": 0, "status": "f", "names": ["Pointer_UpLeft"]}, {"keysym": 65253, "unicode": 0, "status": "f", "names": ["Pointer_UpRight"]}, {"keysym": 65254, "unicode": 0, "status": "f", "names": ["Pointer_DownLeft"]}, {"keysym": 65255, "unicode": 0, "status": "f", "names": ["Pointer_DownRight"]}, {"keysym": 65256, "unicode": 0, "status": "f", "names": ["Pointer_<PERSON><PERSON>_Dflt"]}, {"keysym": 65257, "unicode": 0, "status": "f", "names": ["Pointer_Button1"]}, {"keysym": 65258, "unicode": 0, "status": "f", "names": ["Pointer_Button2"]}, {"keysym": 65259, "unicode": 0, "status": "f", "names": ["Pointer_Button3"]}, {"keysym": 65260, "unicode": 0, "status": "f", "names": ["Pointer_<PERSON><PERSON>4"]}, {"keysym": 65261, "unicode": 0, "status": "f", "names": ["Pointer_<PERSON><PERSON>5"]}, {"keysym": 65262, "unicode": 0, "status": "f", "names": ["Pointer_DblClick_Dflt"]}, {"keysym": 65263, "unicode": 0, "status": "f", "names": ["Pointer_DblClick1"]}, {"keysym": 65264, "unicode": 0, "status": "f", "names": ["Pointer_DblClick2"]}, {"keysym": 65265, "unicode": 0, "status": "f", "names": ["Pointer_DblClick3"]}, {"keysym": 65266, "unicode": 0, "status": "f", "names": ["Pointer_DblClick4"]}, {"keysym": 65267, "unicode": 0, "status": "f", "names": ["Pointer_DblClick5"]}, {"keysym": 65268, "unicode": 0, "status": "f", "names": ["Pointer_Drag_Dflt"]}, {"keysym": 65269, "unicode": 0, "status": "f", "names": ["Pointer_Drag1"]}, {"keysym": 65270, "unicode": 0, "status": "f", "names": ["Pointer_Drag2"]}, {"keysym": 65271, "unicode": 0, "status": "f", "names": ["Pointer_Drag3"]}, {"keysym": 65272, "unicode": 0, "status": "f", "names": ["Pointer_Drag4"]}, {"keysym": 65273, "unicode": 0, "status": "f", "names": ["Pointer_Enable<PERSON>eys"]}, {"keysym": 65274, "unicode": 0, "status": "f", "names": ["Pointer_Accelerate"]}, {"keysym": 65275, "unicode": 0, "status": "f", "names": ["Pointer_DfltBtnNext"]}, {"keysym": 65276, "unicode": 0, "status": "f", "names": ["Pointer_DfltBtnPrev"]}, {"keysym": 65277, "unicode": 0, "status": "f", "names": ["Pointer_Drag5"]}, {"keysym": 65288, "unicode": 8, "status": "f", "names": ["BackSpace"]}, {"keysym": 65289, "unicode": 9, "status": "f", "names": ["Tab"]}, {"keysym": 65290, "unicode": 10, "status": "f", "names": ["Linefeed"]}, {"keysym": 65291, "unicode": 11, "status": "f", "names": ["Clear"]}, {"keysym": 65293, "unicode": 13, "status": "f", "names": ["Return"]}, {"keysym": 65299, "unicode": 19, "status": "f", "names": ["Pause"]}, {"keysym": 65300, "unicode": 20, "status": "f", "names": ["Sc<PERSON>_Lock"]}, {"keysym": 65301, "unicode": 21, "status": "f", "names": ["Sys_Req"]}, {"keysym": 65307, "unicode": 27, "status": "f", "names": ["Escape"]}, {"keysym": 65312, "unicode": 0, "status": "f", "names": ["Multi_key"]}, {"keysym": 65313, "unicode": 0, "status": "f", "names": ["Kanji"]}, {"keysym": 65314, "unicode": 0, "status": "f", "names": ["Muhen<PERSON>"]}, {"keysym": 65315, "unicode": 0, "status": "f", "names": ["Henkan_Mode"]}, {"keysym": 65316, "unicode": 0, "status": "f", "names": ["<PERSON><PERSON>"]}, {"keysym": 65317, "unicode": 0, "status": "f", "names": ["Hi<PERSON>na"]}, {"keysym": 65318, "unicode": 0, "status": "f", "names": ["<PERSON><PERSON><PERSON>"]}, {"keysym": 65319, "unicode": 0, "status": "f", "names": ["Hiragana_Katakana"]}, {"keysym": 65320, "unicode": 0, "status": "f", "names": ["Zenkaku"]}, {"keysym": 65321, "unicode": 0, "status": "f", "names": ["<PERSON><PERSON><PERSON>"]}, {"keysym": 65322, "unicode": 0, "status": "f", "names": ["Zenkaku_Hankaku"]}, {"keysym": 65323, "unicode": 0, "status": "f", "names": ["Touroku"]}, {"keysym": 65324, "unicode": 0, "status": "f", "names": ["Massyo"]}, {"keysym": 65325, "unicode": 0, "status": "f", "names": ["<PERSON><PERSON>_<PERSON>"]}, {"keysym": 65326, "unicode": 0, "status": "f", "names": ["<PERSON><PERSON>_<PERSON><PERSON>"]}, {"keysym": 65327, "unicode": 0, "status": "f", "names": ["Eisu_Shift"]}, {"keysym": 65328, "unicode": 0, "status": "f", "names": ["Eisu_toggle"]}, {"keysym": 65329, "unicode": 0, "status": "f", "names": ["Hangul"]}, {"keysym": 65330, "unicode": 0, "status": "f", "names": ["Hangul_Start"]}, {"keysym": 65331, "unicode": 0, "status": "f", "names": ["Hangul_End"]}, {"keysym": 65332, "unicode": 0, "status": "f", "names": ["Hangul_Hanja"]}, {"keysym": 65333, "unicode": 0, "status": "f", "names": ["<PERSON>_<PERSON>o"]}, {"keysym": 65334, "unicode": 0, "status": "f", "names": ["Hangul_Romaja"]}, {"keysym": 65335, "unicode": 0, "status": "f", "names": ["Codeinput"]}, {"keysym": 65336, "unicode": 0, "status": "f", "names": ["Hangul_Je<PERSON>ja"]}, {"keysym": 65337, "unicode": 0, "status": "f", "names": ["Hangul_Banja"]}, {"keysym": 65338, "unicode": 0, "status": "f", "names": ["Hangul_PreHanja"]}, {"keysym": 65339, "unicode": 0, "status": "f", "names": ["Hangul_PostHanja"]}, {"keysym": 65340, "unicode": 0, "status": "f", "names": ["SingleCandidate"]}, {"keysym": 65341, "unicode": 0, "status": "f", "names": ["MultipleCandidate"]}, {"keysym": 65342, "unicode": 0, "status": "f", "names": ["PreviousCandidate"]}, {"keysym": 65343, "unicode": 0, "status": "f", "names": ["Hangul_Special"]}, {"keysym": 65360, "unicode": 0, "status": "f", "names": ["Home"]}, {"keysym": 65361, "unicode": 0, "status": "f", "names": ["Left"]}, {"keysym": 65362, "unicode": 0, "status": "f", "names": ["Up"]}, {"keysym": 65363, "unicode": 0, "status": "f", "names": ["Right"]}, {"keysym": 65364, "unicode": 0, "status": "f", "names": ["Down"]}, {"keysym": 65365, "unicode": 0, "status": "f", "names": ["Prior"]}, {"keysym": 65366, "unicode": 0, "status": "f", "names": ["Next"]}, {"keysym": 65367, "unicode": 0, "status": "f", "names": ["End"]}, {"keysym": 65368, "unicode": 0, "status": "f", "names": ["<PERSON><PERSON>"]}, {"keysym": 65376, "unicode": 0, "status": "f", "names": ["Select"]}, {"keysym": 65377, "unicode": 0, "status": "f", "names": ["Print"]}, {"keysym": 65378, "unicode": 0, "status": "f", "names": ["Execute"]}, {"keysym": 65379, "unicode": 0, "status": "f", "names": ["Insert"]}, {"keysym": 65381, "unicode": 0, "status": "f", "names": ["Undo"]}, {"keysym": 65382, "unicode": 0, "status": "f", "names": ["Redo"]}, {"keysym": 65383, "unicode": 0, "status": "f", "names": ["<PERSON><PERSON>"]}, {"keysym": 65384, "unicode": 0, "status": "f", "names": ["Find"]}, {"keysym": 65385, "unicode": 0, "status": "f", "names": ["Cancel"]}, {"keysym": 65386, "unicode": 0, "status": "f", "names": ["Help"]}, {"keysym": 65387, "unicode": 0, "status": "f", "names": ["Break"]}, {"keysym": 65406, "unicode": 0, "status": "f", "names": ["Mode_switch"]}, {"keysym": 65407, "unicode": 0, "status": "f", "names": ["Num_Lock"]}, {"keysym": 65408, "unicode": 32, "status": "f", "names": ["KP_Space"]}, {"keysym": 65417, "unicode": 9, "status": "f", "names": ["KP_Tab"]}, {"keysym": 65421, "unicode": 13, "status": "f", "names": ["KP_Enter"]}, {"keysym": 65425, "unicode": 0, "status": "f", "names": ["KP_F1"]}, {"keysym": 65426, "unicode": 0, "status": "f", "names": ["KP_F2"]}, {"keysym": 65427, "unicode": 0, "status": "f", "names": ["KP_F3"]}, {"keysym": 65428, "unicode": 0, "status": "f", "names": ["KP_F4"]}, {"keysym": 65429, "unicode": 0, "status": "f", "names": ["KP_Home"]}, {"keysym": 65430, "unicode": 0, "status": "f", "names": ["KP_Left"]}, {"keysym": 65431, "unicode": 0, "status": "f", "names": ["KP_Up"]}, {"keysym": 65432, "unicode": 0, "status": "f", "names": ["KP_Right"]}, {"keysym": 65433, "unicode": 0, "status": "f", "names": ["KP_Down"]}, {"keysym": 65434, "unicode": 0, "status": "f", "names": ["KP_Prior"]}, {"keysym": 65435, "unicode": 0, "status": "f", "names": ["KP_Next"]}, {"keysym": 65436, "unicode": 0, "status": "f", "names": ["KP_End"]}, {"keysym": 65437, "unicode": 0, "status": "f", "names": ["K<PERSON>_<PERSON>gin"]}, {"keysym": 65438, "unicode": 0, "status": "f", "names": ["KP_Insert"]}, {"keysym": 65439, "unicode": 0, "status": "f", "names": ["KP_Delete"]}, {"keysym": 65450, "unicode": 42, "status": "f", "names": ["KP_Multiply"]}, {"keysym": 65451, "unicode": 43, "status": "f", "names": ["KP_Add"]}, {"keysym": 65452, "unicode": 44, "status": "f", "names": ["KP_Separator"]}, {"keysym": 65453, "unicode": 45, "status": "f", "names": ["KP_Subtract"]}, {"keysym": 65454, "unicode": 46, "status": "f", "names": ["KP_Decimal"]}, {"keysym": 65455, "unicode": 47, "status": "f", "names": ["KP_Divide"]}, {"keysym": 65456, "unicode": 48, "status": "f", "names": ["KP_0"]}, {"keysym": 65457, "unicode": 49, "status": "f", "names": ["KP_1"]}, {"keysym": 65458, "unicode": 50, "status": "f", "names": ["KP_2"]}, {"keysym": 65459, "unicode": 51, "status": "f", "names": ["KP_3"]}, {"keysym": 65460, "unicode": 52, "status": "f", "names": ["KP_4"]}, {"keysym": 65461, "unicode": 53, "status": "f", "names": ["KP_5"]}, {"keysym": 65462, "unicode": 54, "status": "f", "names": ["KP_6"]}, {"keysym": 65463, "unicode": 55, "status": "f", "names": ["KP_7"]}, {"keysym": 65464, "unicode": 56, "status": "f", "names": ["KP_8"]}, {"keysym": 65465, "unicode": 57, "status": "f", "names": ["KP_9"]}, {"keysym": 65469, "unicode": 61, "status": "f", "names": ["KP_Equal"]}, {"keysym": 65470, "unicode": 0, "status": "f", "names": ["F1"]}, {"keysym": 65471, "unicode": 0, "status": "f", "names": ["F2"]}, {"keysym": 65472, "unicode": 0, "status": "f", "names": ["F3"]}, {"keysym": 65473, "unicode": 0, "status": "f", "names": ["F4"]}, {"keysym": 65474, "unicode": 0, "status": "f", "names": ["F5"]}, {"keysym": 65475, "unicode": 0, "status": "f", "names": ["F6"]}, {"keysym": 65476, "unicode": 0, "status": "f", "names": ["F7"]}, {"keysym": 65477, "unicode": 0, "status": "f", "names": ["F8"]}, {"keysym": 65478, "unicode": 0, "status": "f", "names": ["F9"]}, {"keysym": 65479, "unicode": 0, "status": "f", "names": ["F10"]}, {"keysym": 65480, "unicode": 0, "status": "f", "names": ["F11"]}, {"keysym": 65481, "unicode": 0, "status": "f", "names": ["F12"]}, {"keysym": 65482, "unicode": 0, "status": "f", "names": ["F13"]}, {"keysym": 65483, "unicode": 0, "status": "f", "names": ["F14"]}, {"keysym": 65484, "unicode": 0, "status": "f", "names": ["F15"]}, {"keysym": 65485, "unicode": 0, "status": "f", "names": ["F16"]}, {"keysym": 65486, "unicode": 0, "status": "f", "names": ["F17"]}, {"keysym": 65487, "unicode": 0, "status": "f", "names": ["F18"]}, {"keysym": 65488, "unicode": 0, "status": "f", "names": ["F19"]}, {"keysym": 65489, "unicode": 0, "status": "f", "names": ["F20"]}, {"keysym": 65490, "unicode": 0, "status": "f", "names": ["F21"]}, {"keysym": 65491, "unicode": 0, "status": "f", "names": ["F22"]}, {"keysym": 65492, "unicode": 0, "status": "f", "names": ["F23"]}, {"keysym": 65493, "unicode": 0, "status": "f", "names": ["F24"]}, {"keysym": 65494, "unicode": 0, "status": "f", "names": ["F25"]}, {"keysym": 65495, "unicode": 0, "status": "f", "names": ["F26"]}, {"keysym": 65496, "unicode": 0, "status": "f", "names": ["F27"]}, {"keysym": 65497, "unicode": 0, "status": "f", "names": ["F28"]}, {"keysym": 65498, "unicode": 0, "status": "f", "names": ["F29"]}, {"keysym": 65499, "unicode": 0, "status": "f", "names": ["F30"]}, {"keysym": 65500, "unicode": 0, "status": "f", "names": ["F31"]}, {"keysym": 65501, "unicode": 0, "status": "f", "names": ["F32"]}, {"keysym": 65502, "unicode": 0, "status": "f", "names": ["F33"]}, {"keysym": 65503, "unicode": 0, "status": "f", "names": ["F34"]}, {"keysym": 65504, "unicode": 0, "status": "f", "names": ["F35"]}, {"keysym": 65505, "unicode": 0, "status": "f", "names": ["Shift_L"]}, {"keysym": 65506, "unicode": 0, "status": "f", "names": ["Shift_R"]}, {"keysym": 65507, "unicode": 0, "status": "f", "names": ["Control_L"]}, {"keysym": 65508, "unicode": 0, "status": "f", "names": ["Control_R"]}, {"keysym": 65509, "unicode": 0, "status": "f", "names": ["Caps_Lock"]}, {"keysym": 65510, "unicode": 0, "status": "f", "names": ["Shift_Lock"]}, {"keysym": 65511, "unicode": 0, "status": "f", "names": ["Meta_L"]}, {"keysym": 65512, "unicode": 0, "status": "f", "names": ["Meta_R"]}, {"keysym": 65513, "unicode": 0, "status": "f", "names": ["Alt_L"]}, {"keysym": 65514, "unicode": 0, "status": "f", "names": ["Alt_R"]}, {"keysym": 65515, "unicode": 0, "status": "f", "names": ["Super_L"]}, {"keysym": 65516, "unicode": 0, "status": "f", "names": ["Super_R"]}, {"keysym": 65517, "unicode": 0, "status": "f", "names": ["Hyper_L"]}, {"keysym": 65518, "unicode": 0, "status": "f", "names": ["Hyper_R"]}, {"keysym": 65535, "unicode": 0, "status": "f", "names": ["Delete"]}, {"keysym": 16777215, "unicode": 0, "status": "f", "names": ["VoidSymbol"]}, {"keysym": 1709, "unicode": 1169, "status": ".", "names": ["Ukrainian_ghe_with_upturn"]}, {"keysym": 1725, "unicode": 1168, "status": ".", "names": ["Ukrainian_GHE_WITH_UPTURN"]}, {"keysym": 5281, "unicode": 0, "status": "r", "names": ["Armenian_eternity"]}, {"keysym": 5282, "unicode": 1415, "status": "u", "names": ["Armenian_ligature_ew"]}, {"keysym": 5283, "unicode": 1417, "status": "u", "names": ["Armenian_verjaket"]}, {"keysym": 5284, "unicode": 41, "status": "r", "names": ["Armenian_parenright"]}, {"keysym": 5285, "unicode": 40, "status": "r", "names": ["Armenian_parenleft"]}, {"keysym": 5286, "unicode": 187, "status": "r", "names": ["Armenian_guillem<PERSON><PERSON>"]}, {"keysym": 5287, "unicode": 171, "status": "r", "names": ["Armenian_guillemotleft"]}, {"keysym": 5288, "unicode": 8212, "status": "r", "names": ["Armenian_em_dash"]}, {"keysym": 5289, "unicode": 46, "status": "r", "names": ["Armenian_mijaket"]}, {"keysym": 5290, "unicode": 1373, "status": "u", "names": ["Armenian_but"]}, {"keysym": 5291, "unicode": 44, "status": "r", "names": ["Armenian_comma"]}, {"keysym": 5292, "unicode": 8211, "status": "r", "names": ["Armenian_en_dash"]}, {"keysym": 5293, "unicode": 1418, "status": "u", "names": ["Armenian_yenta<PERSON>na"]}, {"keysym": 5294, "unicode": 8230, "status": "r", "names": ["Armenian_ellipsis"]}, {"keysym": 5295, "unicode": 1372, "status": "u", "names": ["<PERSON>_<PERSON><PERSON><PERSON>"]}, {"keysym": 5296, "unicode": 1371, "status": "u", "names": ["Armenian_shesht"]}, {"keysym": 5297, "unicode": 1374, "status": "u", "names": ["Armenian_paruyk"]}, {"keysym": 5298, "unicode": 1329, "status": "u", "names": ["Armenian_AYB"]}, {"keysym": 5299, "unicode": 1377, "status": "u", "names": ["Armenian_ayb"]}, {"keysym": 5300, "unicode": 1330, "status": "u", "names": ["Armenian_BEN"]}, {"keysym": 5301, "unicode": 1378, "status": "u", "names": ["<PERSON>_ben"]}, {"keysym": 5302, "unicode": 1331, "status": "u", "names": ["Armenian_GIM"]}, {"keysym": 5303, "unicode": 1379, "status": "u", "names": ["Armenian_gim"]}, {"keysym": 5304, "unicode": 1332, "status": "u", "names": ["Armenian_DA"]}, {"keysym": 5305, "unicode": 1380, "status": "u", "names": ["Armenian_da"]}, {"keysym": 5306, "unicode": 1333, "status": "u", "names": ["Armenian_YECH"]}, {"keysym": 5307, "unicode": 1381, "status": "u", "names": ["Armenian_yech"]}, {"keysym": 5308, "unicode": 1334, "status": "u", "names": ["Armenian_ZA"]}, {"keysym": 5309, "unicode": 1382, "status": "u", "names": ["Armenian_za"]}, {"keysym": 5310, "unicode": 1335, "status": "u", "names": ["Armenian_E"]}, {"keysym": 5311, "unicode": 1383, "status": "u", "names": ["Armenian_e"]}, {"keysym": 5312, "unicode": 1336, "status": "u", "names": ["Armenian_AT"]}, {"keysym": 5313, "unicode": 1384, "status": "u", "names": ["Armenian_at"]}, {"keysym": 5314, "unicode": 1337, "status": "u", "names": ["Armenian_TO"]}, {"keysym": 5315, "unicode": 1385, "status": "u", "names": ["Armenian_to"]}, {"keysym": 5316, "unicode": 1338, "status": "u", "names": ["Armenian_ZHE"]}, {"keysym": 5317, "unicode": 1386, "status": "u", "names": ["Armenian_zhe"]}, {"keysym": 5318, "unicode": 1339, "status": "u", "names": ["Armenian_INI"]}, {"keysym": 5319, "unicode": 1387, "status": "u", "names": ["Armenian_ini"]}, {"keysym": 5320, "unicode": 1340, "status": "u", "names": ["Armenian_LYUN"]}, {"keysym": 5321, "unicode": 1388, "status": "u", "names": ["Armenian_lyun"]}, {"keysym": 5322, "unicode": 1341, "status": "u", "names": ["Armenian_KHE"]}, {"keysym": 5323, "unicode": 1389, "status": "u", "names": ["Armenian_khe"]}, {"keysym": 5324, "unicode": 1342, "status": "u", "names": ["Armenian_TSA"]}, {"keysym": 5325, "unicode": 1390, "status": "u", "names": ["Armenian_tsa"]}, {"keysym": 5326, "unicode": 1343, "status": "u", "names": ["Armenian_KEN"]}, {"keysym": 5327, "unicode": 1391, "status": "u", "names": ["Armenian_ken"]}, {"keysym": 5328, "unicode": 1344, "status": "u", "names": ["Armenian_HO"]}, {"keysym": 5329, "unicode": 1392, "status": "u", "names": ["Armenian_ho"]}, {"keysym": 5330, "unicode": 1345, "status": "u", "names": ["Armenian_DZA"]}, {"keysym": 5331, "unicode": 1393, "status": "u", "names": ["Armenian_dza"]}, {"keysym": 5332, "unicode": 1346, "status": "u", "names": ["Armenian_GHAT"]}, {"keysym": 5333, "unicode": 1394, "status": "u", "names": ["Armenian_ghat"]}, {"keysym": 5334, "unicode": 1347, "status": "u", "names": ["Armenian_TCHE"]}, {"keysym": 5335, "unicode": 1395, "status": "u", "names": ["Armenian_tche"]}, {"keysym": 5336, "unicode": 1348, "status": "u", "names": ["Armenian_MEN"]}, {"keysym": 5337, "unicode": 1396, "status": "u", "names": ["Armenian_men"]}, {"keysym": 5338, "unicode": 1349, "status": "u", "names": ["Armenian_HI"]}, {"keysym": 5339, "unicode": 1397, "status": "u", "names": ["Armenian_hi"]}, {"keysym": 5340, "unicode": 1350, "status": "u", "names": ["Armenian_NU"]}, {"keysym": 5341, "unicode": 1398, "status": "u", "names": ["Armenian_nu"]}, {"keysym": 5342, "unicode": 1351, "status": "u", "names": ["Armenian_SHA"]}, {"keysym": 5343, "unicode": 1399, "status": "u", "names": ["Armenian_sha"]}, {"keysym": 5344, "unicode": 1352, "status": "u", "names": ["Armenian_VO"]}, {"keysym": 5345, "unicode": 1400, "status": "u", "names": ["Armenian_vo"]}, {"keysym": 5346, "unicode": 1353, "status": "u", "names": ["Armenian_CHA"]}, {"keysym": 5347, "unicode": 1401, "status": "u", "names": ["Armenian_cha"]}, {"keysym": 5348, "unicode": 1354, "status": "u", "names": ["Armenian_PE"]}, {"keysym": 5349, "unicode": 1402, "status": "u", "names": ["Armenian_pe"]}, {"keysym": 5350, "unicode": 1355, "status": "u", "names": ["Armenian_JE"]}, {"keysym": 5351, "unicode": 1403, "status": "u", "names": ["Armenian_je"]}, {"keysym": 5352, "unicode": 1356, "status": "u", "names": ["Armenian_RA"]}, {"keysym": 5353, "unicode": 1404, "status": "u", "names": ["Armenian_ra"]}, {"keysym": 5354, "unicode": 1357, "status": "u", "names": ["Armenian_SE"]}, {"keysym": 5355, "unicode": 1405, "status": "u", "names": ["Armenian_se"]}, {"keysym": 5356, "unicode": 1358, "status": "u", "names": ["Armenian_VEV"]}, {"keysym": 5357, "unicode": 1406, "status": "u", "names": ["Armenian_vev"]}, {"keysym": 5358, "unicode": 1359, "status": "u", "names": ["Armenian_TYUN"]}, {"keysym": 5359, "unicode": 1407, "status": "u", "names": ["Armenian_tyun"]}, {"keysym": 5360, "unicode": 1360, "status": "u", "names": ["Armenian_RE"]}, {"keysym": 5361, "unicode": 1408, "status": "u", "names": ["Armenian_re"]}, {"keysym": 5362, "unicode": 1361, "status": "u", "names": ["Armenian_TSO"]}, {"keysym": 5363, "unicode": 1409, "status": "u", "names": ["Armenian_tso"]}, {"keysym": 5364, "unicode": 1362, "status": "u", "names": ["Armenian_VYUN"]}, {"keysym": 5365, "unicode": 1410, "status": "u", "names": ["Armenian_vyun"]}, {"keysym": 5366, "unicode": 1363, "status": "u", "names": ["Armenian_PYUR"]}, {"keysym": 5367, "unicode": 1411, "status": "u", "names": ["Armenian_pyur"]}, {"keysym": 5368, "unicode": 1364, "status": "u", "names": ["Armenian_KE"]}, {"keysym": 5369, "unicode": 1412, "status": "u", "names": ["Armenian_ke"]}, {"keysym": 5370, "unicode": 1365, "status": "u", "names": ["Armenian_O"]}, {"keysym": 5371, "unicode": 1413, "status": "u", "names": ["Armenian_o"]}, {"keysym": 5372, "unicode": 1366, "status": "u", "names": ["Armenian_FE"]}, {"keysym": 5373, "unicode": 1414, "status": "u", "names": ["Armenian_fe"]}, {"keysym": 5374, "unicode": 1370, "status": "u", "names": ["Armenian_apostrophe"]}, {"keysym": 5375, "unicode": 167, "status": "r", "names": ["Armenian_section_sign"]}, {"keysym": 5584, "unicode": 4304, "status": "u", "names": ["Georgian_an"]}, {"keysym": 5585, "unicode": 4305, "status": "u", "names": ["Georgian_ban"]}, {"keysym": 5586, "unicode": 4306, "status": "u", "names": ["Georgian_gan"]}, {"keysym": 5587, "unicode": 4307, "status": "u", "names": ["Georgian_don"]}, {"keysym": 5588, "unicode": 4308, "status": "u", "names": ["Georgian_en"]}, {"keysym": 5589, "unicode": 4309, "status": "u", "names": ["Georgian_vin"]}, {"keysym": 5590, "unicode": 4310, "status": "u", "names": ["Georgian_zen"]}, {"keysym": 5591, "unicode": 4311, "status": "u", "names": ["Georgian_tan"]}, {"keysym": 5592, "unicode": 4312, "status": "u", "names": ["Georgian_in"]}, {"keysym": 5593, "unicode": 4313, "status": "u", "names": ["Georgian_kan"]}, {"keysym": 5594, "unicode": 4314, "status": "u", "names": ["Georgian_las"]}, {"keysym": 5595, "unicode": 4315, "status": "u", "names": ["Georgian_man"]}, {"keysym": 5596, "unicode": 4316, "status": "u", "names": ["Georgian_nar"]}, {"keysym": 5597, "unicode": 4317, "status": "u", "names": ["Georgian_on"]}, {"keysym": 5598, "unicode": 4318, "status": "u", "names": ["Georgian_par"]}, {"keysym": 5599, "unicode": 4319, "status": "u", "names": ["Georgian_zhar"]}, {"keysym": 5600, "unicode": 4320, "status": "u", "names": ["Georgian_rae"]}, {"keysym": 5601, "unicode": 4321, "status": "u", "names": ["Georgian_san"]}, {"keysym": 5602, "unicode": 4322, "status": "u", "names": ["Georgian_tar"]}, {"keysym": 5603, "unicode": 4323, "status": "u", "names": ["Georgian_un"]}, {"keysym": 5604, "unicode": 4324, "status": "u", "names": ["Georgian_phar"]}, {"keysym": 5605, "unicode": 4325, "status": "u", "names": ["Georgian_khar"]}, {"keysym": 5606, "unicode": 4326, "status": "u", "names": ["Georgian_ghan"]}, {"keysym": 5607, "unicode": 4327, "status": "u", "names": ["Georgian_qar"]}, {"keysym": 5608, "unicode": 4328, "status": "u", "names": ["Georgian_shin"]}, {"keysym": 5609, "unicode": 4329, "status": "u", "names": ["Georgian_chin"]}, {"keysym": 5610, "unicode": 4330, "status": "u", "names": ["Georgian_can"]}, {"keysym": 5611, "unicode": 4331, "status": "u", "names": ["Georgian_jil"]}, {"keysym": 5612, "unicode": 4332, "status": "u", "names": ["Georgian_cil"]}, {"keysym": 5613, "unicode": 4333, "status": "u", "names": ["Georgian_char"]}, {"keysym": 5614, "unicode": 4334, "status": "u", "names": ["Georgian_xan"]}, {"keysym": 5615, "unicode": 4335, "status": "u", "names": ["Georgian_jhan"]}, {"keysym": 5616, "unicode": 4336, "status": "u", "names": ["Georgian_hae"]}, {"keysym": 5617, "unicode": 4337, "status": "u", "names": ["Georgian_he"]}, {"keysym": 5618, "unicode": 4338, "status": "u", "names": ["Georgian_hie"]}, {"keysym": 5619, "unicode": 4339, "status": "u", "names": ["Georgian_we"]}, {"keysym": 5620, "unicode": 4340, "status": "u", "names": ["Georgian_har"]}, {"keysym": 5621, "unicode": 4341, "status": "u", "names": ["Georgian_hoe"]}, {"keysym": 5622, "unicode": 4342, "status": "u", "names": ["Georgian_fi"]}, {"keysym": 4769, "unicode": 7682, "status": "u", "names": ["Babovedot"]}, {"keysym": 4770, "unicode": 7683, "status": "u", "names": ["babovedot"]}, {"keysym": 4774, "unicode": 7690, "status": "u", "names": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"keysym": 4776, "unicode": 7808, "status": "u", "names": ["<PERSON><PERSON>"]}, {"keysym": 4778, "unicode": 7810, "status": "u", "names": ["Wacute"]}, {"keysym": 4779, "unicode": 7691, "status": "u", "names": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"keysym": 4780, "unicode": 7922, "status": "u", "names": ["<PERSON><PERSON>"]}, {"keysym": 4784, "unicode": 7710, "status": "u", "names": ["Fabovedot"]}, {"keysym": 4785, "unicode": 7711, "status": "u", "names": ["fabovedot"]}, {"keysym": 4788, "unicode": 7744, "status": "u", "names": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"keysym": 4789, "unicode": 7745, "status": "u", "names": ["mabo<PERSON><PERSON>"]}, {"keysym": 4791, "unicode": 7766, "status": "u", "names": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"keysym": 4792, "unicode": 7809, "status": "u", "names": ["wgrave"]}, {"keysym": 4793, "unicode": 7767, "status": "u", "names": ["pabovedot"]}, {"keysym": 4794, "unicode": 7811, "status": "u", "names": ["wacute"]}, {"keysym": 4795, "unicode": 7776, "status": "u", "names": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"keysym": 4796, "unicode": 7923, "status": "u", "names": ["ygrave"]}, {"keysym": 4797, "unicode": 7812, "status": "u", "names": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"keysym": 4798, "unicode": 7813, "status": "u", "names": ["w<PERSON><PERSON><PERSON>"]}, {"keysym": 4799, "unicode": 7777, "status": "u", "names": ["sabo<PERSON><PERSON>"]}, {"keysym": 4816, "unicode": 372, "status": "u", "names": ["Wcircumflex"]}, {"keysym": 4823, "unicode": 7786, "status": "u", "names": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"keysym": 4830, "unicode": 374, "status": "u", "names": ["Ycircumflex"]}, {"keysym": 4848, "unicode": 373, "status": "u", "names": ["wcircumflex"]}, {"keysym": 4855, "unicode": 7787, "status": "u", "names": ["tabovedot"]}, {"keysym": 4862, "unicode": 375, "status": "u", "names": ["ycircumflex"]}, {"keysym": 1424, "unicode": 1776, "status": "u", "names": ["Farsi_0"]}, {"keysym": 1425, "unicode": 1777, "status": "u", "names": ["Farsi_1"]}, {"keysym": 1426, "unicode": 1778, "status": "u", "names": ["Farsi_2"]}, {"keysym": 1427, "unicode": 1779, "status": "u", "names": ["Farsi_3"]}, {"keysym": 1428, "unicode": 1780, "status": "u", "names": ["Farsi_4"]}, {"keysym": 1429, "unicode": 1781, "status": "u", "names": ["Farsi_5"]}, {"keysym": 1430, "unicode": 1782, "status": "u", "names": ["Farsi_6"]}, {"keysym": 1431, "unicode": 1783, "status": "u", "names": ["Farsi_7"]}, {"keysym": 1432, "unicode": 1784, "status": "u", "names": ["Farsi_8"]}, {"keysym": 1433, "unicode": 1785, "status": "u", "names": ["Farsi_9"]}, {"keysym": 1445, "unicode": 1642, "status": "u", "names": ["Arabic_percent"]}, {"keysym": 1446, "unicode": 1648, "status": "u", "names": ["Arabic_superscript_alef"]}, {"keysym": 1447, "unicode": 1657, "status": "u", "names": ["Arabic_tteh"]}, {"keysym": 1448, "unicode": 1662, "status": "u", "names": ["Arabic_peh"]}, {"keysym": 1449, "unicode": 1670, "status": "u", "names": ["Arabic_tcheh"]}, {"keysym": 1450, "unicode": 1672, "status": "u", "names": ["Arabic_ddal"]}, {"keysym": 1451, "unicode": 1681, "status": "u", "names": ["Arabic_rreh"]}, {"keysym": 1454, "unicode": 1748, "status": "u", "names": ["Arabic_fullstop"]}, {"keysym": 1456, "unicode": 1632, "status": "u", "names": ["Arabic_0"]}, {"keysym": 1457, "unicode": 1633, "status": "u", "names": ["Arabic_1"]}, {"keysym": 1458, "unicode": 1634, "status": "u", "names": ["Arabic_2"]}, {"keysym": 1459, "unicode": 1635, "status": "u", "names": ["Arabic_3"]}, {"keysym": 1460, "unicode": 1636, "status": "u", "names": ["Arabic_4"]}, {"keysym": 1461, "unicode": 1637, "status": "u", "names": ["Arabic_5"]}, {"keysym": 1462, "unicode": 1638, "status": "u", "names": ["Arabic_6"]}, {"keysym": 1463, "unicode": 1639, "status": "u", "names": ["Arabic_7"]}, {"keysym": 1464, "unicode": 1640, "status": "u", "names": ["Arabic_8"]}, {"keysym": 1465, "unicode": 1641, "status": "u", "names": ["Arabic_9"]}, {"keysym": 1523, "unicode": 1619, "status": "u", "names": ["Arabic_madda_above"]}, {"keysym": 1524, "unicode": 1620, "status": "u", "names": ["Arabic_hamza_above"]}, {"keysym": 1525, "unicode": 1621, "status": "u", "names": ["Arabic_hamza_below"]}, {"keysym": 1526, "unicode": 1688, "status": "u", "names": ["Arabic_jeh"]}, {"keysym": 1527, "unicode": 1700, "status": "u", "names": ["Arabic_veh"]}, {"keysym": 1528, "unicode": 1705, "status": "u", "names": ["Arabic_keheh"]}, {"keysym": 1529, "unicode": 1711, "status": "u", "names": ["Arabic_gaf"]}, {"keysym": 1530, "unicode": 1722, "status": "u", "names": ["Arabic_noon_ghunna"]}, {"keysym": 1531, "unicode": 1726, "status": "u", "names": ["<PERSON>_heh_do<PERSON><PERSON><PERSON>"]}, {"keysym": 1532, "unicode": 1740, "status": "u", "names": ["<PERSON>si_yeh"]}, {"keysym": 1533, "unicode": 1746, "status": "u", "names": ["Arabic_yeh_baree"]}, {"keysym": 1534, "unicode": 1729, "status": "u", "names": ["Arabic_heh_goal"]}, {"keysym": 1664, "unicode": 1170, "status": "u", "names": ["Cyrillic_GHE_bar"]}, {"keysym": 1665, "unicode": 1174, "status": "u", "names": ["Cyrillic_ZHE_descender"]}, {"keysym": 1666, "unicode": 1178, "status": "u", "names": ["Cyrillic_KA_descender"]}, {"keysym": 1667, "unicode": 1180, "status": "u", "names": ["Cyrillic_KA_vertstroke"]}, {"keysym": 1668, "unicode": 1186, "status": "u", "names": ["Cyrillic_EN_descender"]}, {"keysym": 1669, "unicode": 1198, "status": "u", "names": ["Cyrillic_U_straight"]}, {"keysym": 1670, "unicode": 1200, "status": "u", "names": ["Cyrillic_U_straight_bar"]}, {"keysym": 1671, "unicode": 1202, "status": "u", "names": ["Cyrillic_HA_descender"]}, {"keysym": 1672, "unicode": 1206, "status": "u", "names": ["Cyrillic_CHE_descender"]}, {"keysym": 1673, "unicode": 1208, "status": "u", "names": ["Cyrillic_CHE_vertstroke"]}, {"keysym": 1674, "unicode": 1210, "status": "u", "names": ["Cyrillic_SHHA"]}, {"keysym": 1676, "unicode": 1240, "status": "u", "names": ["Cyrillic_SCHWA"]}, {"keysym": 1677, "unicode": 1250, "status": "u", "names": ["Cyrillic_I_macron"]}, {"keysym": 1678, "unicode": 1256, "status": "u", "names": ["Cyrillic_O_bar"]}, {"keysym": 1679, "unicode": 1262, "status": "u", "names": ["Cyrillic_U_macron"]}, {"keysym": 1680, "unicode": 1171, "status": "u", "names": ["Cyrillic_ghe_bar"]}, {"keysym": 1681, "unicode": 1175, "status": "u", "names": ["Cyrillic_zhe_descender"]}, {"keysym": 1682, "unicode": 1179, "status": "u", "names": ["Cyrillic_ka_descender"]}, {"keysym": 1683, "unicode": 1181, "status": "u", "names": ["Cyrillic_ka_vertstroke"]}, {"keysym": 1684, "unicode": 1187, "status": "u", "names": ["Cyrillic_en_descender"]}, {"keysym": 1685, "unicode": 1199, "status": "u", "names": ["Cyrillic_u_straight"]}, {"keysym": 1686, "unicode": 1201, "status": "u", "names": ["Cyrillic_u_straight_bar"]}, {"keysym": 1687, "unicode": 1203, "status": "u", "names": ["Cyrillic_ha_descender"]}, {"keysym": 1688, "unicode": 1207, "status": "u", "names": ["Cyrillic_che_descender"]}, {"keysym": 1689, "unicode": 1209, "status": "u", "names": ["Cyrillic_che_vertstroke"]}, {"keysym": 1690, "unicode": 1211, "status": "u", "names": ["Cyrillic_shha"]}, {"keysym": 1692, "unicode": 1241, "status": "u", "names": ["Cyrillic_schwa"]}, {"keysym": 1693, "unicode": 1251, "status": "u", "names": ["Cyrillic_i_macron"]}, {"keysym": 1694, "unicode": 1257, "status": "u", "names": ["Cyrillic_o_bar"]}, {"keysym": 1695, "unicode": 1263, "status": "u", "names": ["Cyrillic_u_macron"]}, {"keysym": 5794, "unicode": 0, "status": "r", "names": ["Ccedillaabovedot"]}, {"keysym": 5795, "unicode": 7818, "status": "u", "names": ["Xabovedot"]}, {"keysym": 5797, "unicode": 0, "status": "r", "names": ["Qabovedot"]}, {"keysym": 5798, "unicode": 300, "status": "u", "names": ["Ibreve"]}, {"keysym": 5799, "unicode": 0, "status": "r", "names": ["IE"]}, {"keysym": 5800, "unicode": 0, "status": "r", "names": ["UO"]}, {"keysym": 5801, "unicode": 437, "status": "u", "names": ["Zstroke"]}, {"keysym": 5802, "unicode": 486, "status": "u", "names": ["<PERSON><PERSON><PERSON>"]}, {"keysym": 5807, "unicode": 415, "status": "u", "names": ["<PERSON><PERSON><PERSON>"]}, {"keysym": 5810, "unicode": 0, "status": "r", "names": ["ccedillaabovedot"]}, {"keysym": 5811, "unicode": 7819, "status": "u", "names": ["xabovedot"]}, {"keysym": 5812, "unicode": 0, "status": "r", "names": ["<PERSON><PERSON><PERSON>"]}, {"keysym": 5813, "unicode": 0, "status": "r", "names": ["qabovedot"]}, {"keysym": 5814, "unicode": 301, "status": "u", "names": ["i<PERSON>ve"]}, {"keysym": 5815, "unicode": 0, "status": "r", "names": ["ie"]}, {"keysym": 5816, "unicode": 0, "status": "r", "names": ["uo"]}, {"keysym": 5817, "unicode": 438, "status": "u", "names": ["zstroke"]}, {"keysym": 5818, "unicode": 487, "status": "u", "names": ["gcaron"]}, {"keysym": 5821, "unicode": 466, "status": "u", "names": ["ocaron"]}, {"keysym": 5823, "unicode": 629, "status": "u", "names": ["obarred"]}, {"keysym": 5830, "unicode": 399, "status": "u", "names": ["SCHWA"]}, {"keysym": 5878, "unicode": 601, "status": "u", "names": ["schwa"]}, {"keysym": 5841, "unicode": 7734, "status": "u", "names": ["Lbelowdot"]}, {"keysym": 5842, "unicode": 0, "status": "r", "names": ["Lstrokebelowdot"]}, {"keysym": 5843, "unicode": 0, "status": "r", "names": ["Gtilde"]}, {"keysym": 5857, "unicode": 7735, "status": "u", "names": ["lbelowdot"]}, {"keysym": 5858, "unicode": 0, "status": "r", "names": ["lstrokebelowdot"]}, {"keysym": 5859, "unicode": 0, "status": "r", "names": ["gtilde"]}, {"keysym": 7840, "unicode": 7840, "status": "u", "names": ["Abelowdot"]}, {"keysym": 7841, "unicode": 7841, "status": "u", "names": ["abelowdot"]}, {"keysym": 7842, "unicode": 7842, "status": "u", "names": ["<PERSON><PERSON>"]}, {"keysym": 7843, "unicode": 7843, "status": "u", "names": ["ahook"]}, {"keysym": 7844, "unicode": 7844, "status": "u", "names": ["Acircumflexacute"]}, {"keysym": 7845, "unicode": 7845, "status": "u", "names": ["acircumflexacute"]}, {"keysym": 7846, "unicode": 7846, "status": "u", "names": ["Acircumflexgrave"]}, {"keysym": 7847, "unicode": 7847, "status": "u", "names": ["acircumflexgrave"]}, {"keysym": 7848, "unicode": 7848, "status": "u", "names": ["Acircumflexhook"]}, {"keysym": 7849, "unicode": 7849, "status": "u", "names": ["acircumflexhook"]}, {"keysym": 7850, "unicode": 7850, "status": "u", "names": ["Acircumflextilde"]}, {"keysym": 7851, "unicode": 7851, "status": "u", "names": ["acircumflextilde"]}, {"keysym": 7852, "unicode": 7852, "status": "u", "names": ["Acircumflexbelowdot"]}, {"keysym": 7853, "unicode": 7853, "status": "u", "names": ["acircumflexbelowdot"]}, {"keysym": 7854, "unicode": 7854, "status": "u", "names": ["Abreveacute"]}, {"keysym": 7855, "unicode": 7855, "status": "u", "names": ["abreve<PERSON><PERSON>"]}, {"keysym": 7856, "unicode": 7856, "status": "u", "names": ["Abrevegrave"]}, {"keysym": 7857, "unicode": 7857, "status": "u", "names": ["abre<PERSON><PERSON>"]}, {"keysym": 7858, "unicode": 7858, "status": "u", "names": ["Abrevehook"]}, {"keysym": 7859, "unicode": 7859, "status": "u", "names": ["abrevehook"]}, {"keysym": 7860, "unicode": 7860, "status": "u", "names": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"keysym": 7861, "unicode": 7861, "status": "u", "names": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"keysym": 7862, "unicode": 7862, "status": "u", "names": ["Abrevebelowdot"]}, {"keysym": 7863, "unicode": 7863, "status": "u", "names": ["abrevebelowdot"]}, {"keysym": 7864, "unicode": 7864, "status": "u", "names": ["Ebelowdot"]}, {"keysym": 7865, "unicode": 7865, "status": "u", "names": ["ebelowdot"]}, {"keysym": 7866, "unicode": 7866, "status": "u", "names": ["Ehook"]}, {"keysym": 7867, "unicode": 7867, "status": "u", "names": ["ehook"]}, {"keysym": 7868, "unicode": 7868, "status": "u", "names": ["Etilde"]}, {"keysym": 7869, "unicode": 7869, "status": "u", "names": ["<PERSON><PERSON>"]}, {"keysym": 7870, "unicode": 7870, "status": "u", "names": ["Ecircumflexacute"]}, {"keysym": 7871, "unicode": 7871, "status": "u", "names": ["ecircumflexacute"]}, {"keysym": 7872, "unicode": 7872, "status": "u", "names": ["Ecircumflexgrave"]}, {"keysym": 7873, "unicode": 7873, "status": "u", "names": ["ecircumflexgrave"]}, {"keysym": 7874, "unicode": 7874, "status": "u", "names": ["Ecircumflexhook"]}, {"keysym": 7875, "unicode": 7875, "status": "u", "names": ["ecircumflexhook"]}, {"keysym": 7876, "unicode": 7876, "status": "u", "names": ["Ecircumflextilde"]}, {"keysym": 7877, "unicode": 7877, "status": "u", "names": ["ecircumflextilde"]}, {"keysym": 7878, "unicode": 7878, "status": "u", "names": ["Ecircumflexbelowdot"]}, {"keysym": 7879, "unicode": 7879, "status": "u", "names": ["ecircumflexbelowdot"]}, {"keysym": 7880, "unicode": 7880, "status": "u", "names": ["Ihook"]}, {"keysym": 7881, "unicode": 7881, "status": "u", "names": ["ihook"]}, {"keysym": 7882, "unicode": 7882, "status": "u", "names": ["Ibelowdot"]}, {"keysym": 7883, "unicode": 7883, "status": "u", "names": ["ibelowdot"]}, {"keysym": 7884, "unicode": 7884, "status": "u", "names": ["Obelowdot"]}, {"keysym": 7885, "unicode": 7885, "status": "u", "names": ["obelow<PERSON>t"]}, {"keysym": 7886, "unicode": 7886, "status": "u", "names": ["<PERSON><PERSON>"]}, {"keysym": 7887, "unicode": 7887, "status": "u", "names": ["ohook"]}, {"keysym": 7888, "unicode": 7888, "status": "u", "names": ["Ocircumflexacute"]}, {"keysym": 7889, "unicode": 7889, "status": "u", "names": ["ocircumflexacute"]}, {"keysym": 7890, "unicode": 7890, "status": "u", "names": ["Ocircumflexgrave"]}, {"keysym": 7891, "unicode": 7891, "status": "u", "names": ["ocircumflexgrave"]}, {"keysym": 7892, "unicode": 7892, "status": "u", "names": ["Ocircumflexhook"]}, {"keysym": 7893, "unicode": 7893, "status": "u", "names": ["ocircumflexhook"]}, {"keysym": 7894, "unicode": 7894, "status": "u", "names": ["Ocircumflextilde"]}, {"keysym": 7895, "unicode": 7895, "status": "u", "names": ["ocircumflextilde"]}, {"keysym": 7896, "unicode": 7896, "status": "u", "names": ["Ocircumflexbelowdot"]}, {"keysym": 7897, "unicode": 7897, "status": "u", "names": ["ocircumflexbelowdot"]}, {"keysym": 7898, "unicode": 7898, "status": "u", "names": ["Ohornacute"]}, {"keysym": 7899, "unicode": 7899, "status": "u", "names": ["ohornacute"]}, {"keysym": 7900, "unicode": 7900, "status": "u", "names": ["<PERSON><PERSON><PERSON>"]}, {"keysym": 7901, "unicode": 7901, "status": "u", "names": ["oh<PERSON><PERSON>"]}, {"keysym": 7902, "unicode": 7902, "status": "u", "names": ["Ohornhook"]}, {"keysym": 7903, "unicode": 7903, "status": "u", "names": ["ohornhook"]}, {"keysym": 7904, "unicode": 7904, "status": "u", "names": ["Ohorntilde"]}, {"keysym": 7905, "unicode": 7905, "status": "u", "names": ["ohorntilde"]}, {"keysym": 7906, "unicode": 7906, "status": "u", "names": ["Ohornbelowdot"]}, {"keysym": 7907, "unicode": 7907, "status": "u", "names": ["ohornbelowdot"]}, {"keysym": 7908, "unicode": 7908, "status": "u", "names": ["Ubelowdot"]}, {"keysym": 7909, "unicode": 7909, "status": "u", "names": ["ubelowdot"]}, {"keysym": 7910, "unicode": 7910, "status": "u", "names": ["<PERSON><PERSON>"]}, {"keysym": 7911, "unicode": 7911, "status": "u", "names": ["uhook"]}, {"keysym": 7912, "unicode": 7912, "status": "u", "names": ["Uhornacute"]}, {"keysym": 7913, "unicode": 7913, "status": "u", "names": ["uhornacute"]}, {"keysym": 7914, "unicode": 7914, "status": "u", "names": ["<PERSON><PERSON><PERSON>"]}, {"keysym": 7915, "unicode": 7915, "status": "u", "names": ["<PERSON><PERSON><PERSON>"]}, {"keysym": 7916, "unicode": 7916, "status": "u", "names": ["Uhornhook"]}, {"keysym": 7917, "unicode": 7917, "status": "u", "names": ["uhornhook"]}, {"keysym": 7918, "unicode": 7918, "status": "u", "names": ["<PERSON>orntil<PERSON>"]}, {"keysym": 7919, "unicode": 7919, "status": "u", "names": ["uhorntilde"]}, {"keysym": 7920, "unicode": 7920, "status": "u", "names": ["Uhornbelowdot"]}, {"keysym": 7921, "unicode": 7921, "status": "u", "names": ["uhornbelowdot"]}, {"keysym": 7924, "unicode": 7924, "status": "u", "names": ["Ybelowdot"]}, {"keysym": 7925, "unicode": 7925, "status": "u", "names": ["ybelowdot"]}, {"keysym": 7926, "unicode": 7926, "status": "u", "names": ["Yhook"]}, {"keysym": 7927, "unicode": 7927, "status": "u", "names": ["yhook"]}, {"keysym": 7928, "unicode": 7928, "status": "u", "names": ["Ytilde"]}, {"keysym": 7929, "unicode": 7929, "status": "u", "names": ["ytilde"]}, {"keysym": 7930, "unicode": 416, "status": "u", "names": ["<PERSON><PERSON>"]}, {"keysym": 7931, "unicode": 417, "status": "u", "names": ["ohorn"]}, {"keysym": 7932, "unicode": 431, "status": "u", "names": ["<PERSON><PERSON>"]}, {"keysym": 7933, "unicode": 432, "status": "u", "names": ["uhorn"]}, {"keysym": 7839, "unicode": 771, "status": "r", "names": ["combining_tilde"]}, {"keysym": 7922, "unicode": 768, "status": "r", "names": ["combining_grave"]}, {"keysym": 7923, "unicode": 769, "status": "r", "names": ["combining_acute"]}, {"keysym": 7934, "unicode": 777, "status": "r", "names": ["combining_hook"]}, {"keysym": 7935, "unicode": 803, "status": "r", "names": ["combining_belowdot"]}, {"keysym": 65120, "unicode": 803, "status": "f", "names": ["dead_belowdot"]}, {"keysym": 65121, "unicode": 777, "status": "f", "names": ["dead_hook"]}, {"keysym": 65122, "unicode": 795, "status": "f", "names": ["dead_horn"]}], "keysyms": {"32": 0, "33": 1, "34": 2, "35": 3, "36": 4, "37": 5, "38": 6, "39": 7, "40": 8, "41": 9, "42": 10, "43": 11, "44": 12, "45": 13, "46": 14, "47": 15, "48": 16, "49": 17, "50": 18, "51": 19, "52": 20, "53": 21, "54": 22, "55": 23, "56": 24, "57": 25, "58": 26, "59": 27, "60": 28, "61": 29, "62": 30, "63": 31, "64": 32, "65": 33, "66": 34, "67": 35, "68": 36, "69": 37, "70": 38, "71": 39, "72": 40, "73": 41, "74": 42, "75": 43, "76": 44, "77": 45, "78": 46, "79": 47, "80": 48, "81": 49, "82": 50, "83": 51, "84": 52, "85": 53, "86": 54, "87": 55, "88": 56, "89": 57, "90": 58, "91": 59, "92": 60, "93": 61, "94": 62, "95": 63, "96": 64, "97": 65, "98": 66, "99": 67, "100": 68, "101": 69, "102": 70, "103": 71, "104": 72, "105": 73, "106": 74, "107": 75, "108": 76, "109": 77, "110": 78, "111": 79, "112": 80, "113": 81, "114": 82, "115": 83, "116": 84, "117": 85, "118": 86, "119": 87, "120": 88, "121": 89, "122": 90, "123": 91, "124": 92, "125": 93, "126": 94, "160": 95, "161": 96, "162": 97, "163": 98, "164": 99, "165": 100, "166": 101, "167": 102, "168": 103, "169": 104, "170": 105, "171": 106, "172": 107, "173": 108, "174": 109, "175": 110, "176": 111, "177": 112, "178": 113, "179": 114, "180": 115, "181": 116, "182": 117, "183": 118, "184": 119, "185": 120, "186": 121, "187": 122, "188": 123, "189": 124, "190": 125, "191": 126, "192": 127, "193": 128, "194": 129, "195": 130, "196": 131, "197": 132, "198": 133, "199": 134, "200": 135, "201": 136, "202": 137, "203": 138, "204": 139, "205": 140, "206": 141, "207": 142, "208": 143, "209": 144, "210": 145, "211": 146, "212": 147, "213": 148, "214": 149, "215": 150, "216": 151, "217": 152, "218": 153, "219": 154, "220": 155, "221": 156, "222": 157, "223": 158, "224": 159, "225": 160, "226": 161, "227": 162, "228": 163, "229": 164, "230": 165, "231": 166, "232": 167, "233": 168, "234": 169, "235": 170, "236": 171, "237": 172, "238": 173, "239": 174, "240": 175, "241": 176, "242": 177, "243": 178, "244": 179, "245": 180, "246": 181, "247": 182, "248": 183, "249": 184, "250": 185, "251": 186, "252": 187, "253": 188, "254": 189, "255": 190, "417": 191, "418": 192, "419": 193, "421": 194, "422": 195, "425": 196, "426": 197, "427": 198, "428": 199, "430": 200, "431": 201, "433": 202, "434": 203, "435": 204, "437": 205, "438": 206, "439": 207, "441": 208, "442": 209, "443": 210, "444": 211, "445": 212, "446": 213, "447": 214, "448": 215, "451": 216, "453": 217, "454": 218, "456": 219, "458": 220, "460": 221, "463": 222, "464": 223, "465": 224, "466": 225, "469": 226, "472": 227, "473": 228, "475": 229, "478": 230, "480": 231, "483": 232, "485": 233, "486": 234, "488": 235, "490": 236, "492": 237, "495": 238, "496": 239, "497": 240, "498": 241, "501": 242, "504": 243, "505": 244, "507": 245, "510": 246, "511": 247, "673": 248, "678": 249, "681": 250, "683": 251, "684": 252, "689": 253, "694": 254, "697": 255, "699": 256, "700": 257, "709": 258, "710": 259, "725": 260, "728": 261, "733": 262, "734": 263, "741": 264, "742": 265, "757": 266, "760": 267, "765": 268, "766": 269, "930": 270, "931": 271, "933": 272, "934": 273, "938": 274, "939": 275, "940": 276, "947": 277, "949": 278, "950": 279, "954": 280, "955": 281, "956": 282, "957": 283, "959": 284, "960": 285, "967": 286, "972": 287, "975": 288, "977": 289, "978": 290, "979": 291, "985": 292, "989": 293, "990": 294, "992": 295, "999": 296, "1004": 297, "1007": 298, "1009": 299, "1010": 300, "1011": 301, "1017": 302, "1021": 303, "1022": 304, "1150": 305, "1185": 306, "1186": 307, "1187": 308, "1188": 309, "1189": 310, "1190": 311, "1191": 312, "1192": 313, "1193": 314, "1194": 315, "1195": 316, "1196": 317, "1197": 318, "1198": 319, "1199": 320, "1200": 321, "1201": 322, "1202": 323, "1203": 324, "1204": 325, "1205": 326, "1206": 327, "1207": 328, "1208": 329, "1209": 330, "1210": 331, "1211": 332, "1212": 333, "1213": 334, "1214": 335, "1215": 336, "1216": 337, "1217": 338, "1218": 339, "1219": 340, "1220": 341, "1221": 342, "1222": 343, "1223": 344, "1224": 345, "1225": 346, "1226": 347, "1227": 348, "1228": 349, "1229": 350, "1230": 351, "1231": 352, "1232": 353, "1233": 354, "1234": 355, "1235": 356, "1236": 357, "1237": 358, "1238": 359, "1239": 360, "1240": 361, "1241": 362, "1242": 363, "1243": 364, "1244": 365, "1245": 366, "1246": 367, "1247": 368, "1424": 1414, "1425": 1415, "1426": 1416, "1427": 1417, "1428": 1418, "1429": 1419, "1430": 1420, "1431": 1421, "1432": 1422, "1433": 1423, "1445": 1424, "1446": 1425, "1447": 1426, "1448": 1427, "1449": 1428, "1450": 1429, "1451": 1430, "1452": 369, "1454": 1431, "1456": 1432, "1457": 1433, "1458": 1434, "1459": 1435, "1460": 1436, "1461": 1437, "1462": 1438, "1463": 1439, "1464": 1440, "1465": 1441, "1467": 370, "1471": 371, "1473": 372, "1474": 373, "1475": 374, "1476": 375, "1477": 376, "1478": 377, "1479": 378, "1480": 379, "1481": 380, "1482": 381, "1483": 382, "1484": 383, "1485": 384, "1486": 385, "1487": 386, "1488": 387, "1489": 388, "1490": 389, "1491": 390, "1492": 391, "1493": 392, "1494": 393, "1495": 394, "1496": 395, "1497": 396, "1498": 397, "1504": 398, "1505": 399, "1506": 400, "1507": 401, "1508": 402, "1509": 403, "1510": 404, "1511": 405, "1512": 406, "1513": 407, "1514": 408, "1515": 409, "1516": 410, "1517": 411, "1518": 412, "1519": 413, "1520": 414, "1521": 415, "1522": 416, "1523": 1442, "1524": 1443, "1525": 1444, "1526": 1445, "1527": 1446, "1528": 1447, "1529": 1448, "1530": 1449, "1531": 1450, "1532": 1451, "1533": 1452, "1534": 1453, "1664": 1454, "1665": 1455, "1666": 1456, "1667": 1457, "1668": 1458, "1669": 1459, "1670": 1460, "1671": 1461, "1672": 1462, "1673": 1463, "1674": 1464, "1676": 1465, "1677": 1466, "1678": 1467, "1679": 1468, "1680": 1469, "1681": 1470, "1682": 1471, "1683": 1472, "1684": 1473, "1685": 1474, "1686": 1475, "1687": 1476, "1688": 1477, "1689": 1478, "1690": 1479, "1692": 1480, "1693": 1481, "1694": 1482, "1695": 1483, "1697": 417, "1698": 418, "1699": 419, "1700": 420, "1701": 421, "1702": 422, "1703": 423, "1704": 424, "1705": 425, "1706": 426, "1707": 427, "1708": 428, "1709": 1252, "1710": 429, "1711": 430, "1712": 431, "1713": 432, "1714": 433, "1715": 434, "1716": 435, "1717": 436, "1718": 437, "1719": 438, "1720": 439, "1721": 440, "1722": 441, "1723": 442, "1724": 443, "1725": 1253, "1726": 444, "1727": 445, "1728": 446, "1729": 447, "1730": 448, "1731": 449, "1732": 450, "1733": 451, "1734": 452, "1735": 453, "1736": 454, "1737": 455, "1738": 456, "1739": 457, "1740": 458, "1741": 459, "1742": 460, "1743": 461, "1744": 462, "1745": 463, "1746": 464, "1747": 465, "1748": 466, "1749": 467, "1750": 468, "1751": 469, "1752": 470, "1753": 471, "1754": 472, "1755": 473, "1756": 474, "1757": 475, "1758": 476, "1759": 477, "1760": 478, "1761": 479, "1762": 480, "1763": 481, "1764": 482, "1765": 483, "1766": 484, "1767": 485, "1768": 486, "1769": 487, "1770": 488, "1771": 489, "1772": 490, "1773": 491, "1774": 492, "1775": 493, "1776": 494, "1777": 495, "1778": 496, "1779": 497, "1780": 498, "1781": 499, "1782": 500, "1783": 501, "1784": 502, "1785": 503, "1786": 504, "1787": 505, "1788": 506, "1789": 507, "1790": 508, "1791": 509, "1953": 510, "1954": 511, "1955": 512, "1956": 513, "1957": 514, "1959": 515, "1960": 516, "1961": 517, "1963": 518, "1966": 519, "1967": 520, "1969": 521, "1970": 522, "1971": 523, "1972": 524, "1973": 525, "1974": 526, "1975": 527, "1976": 528, "1977": 529, "1978": 530, "1979": 531, "1985": 532, "1986": 533, "1987": 534, "1988": 535, "1989": 536, "1990": 537, "1991": 538, "1992": 539, "1993": 540, "1994": 541, "1995": 542, "1996": 543, "1997": 544, "1998": 545, "1999": 546, "2000": 547, "2001": 548, "2002": 549, "2004": 550, "2005": 551, "2006": 552, "2007": 553, "2008": 554, "2009": 555, "2017": 556, "2018": 557, "2019": 558, "2020": 559, "2021": 560, "2022": 561, "2023": 562, "2024": 563, "2025": 564, "2026": 565, "2027": 566, "2028": 567, "2029": 568, "2030": 569, "2031": 570, "2032": 571, "2033": 572, "2034": 573, "2035": 574, "2036": 575, "2037": 576, "2038": 577, "2039": 578, "2040": 579, "2041": 580, "2209": 581, "2210": 582, "2211": 583, "2212": 584, "2213": 585, "2214": 586, "2215": 587, "2216": 588, "2217": 589, "2218": 590, "2219": 591, "2220": 592, "2221": 593, "2222": 594, "2223": 595, "2224": 596, "2225": 597, "2226": 598, "2227": 599, "2228": 600, "2229": 601, "2230": 602, "2231": 603, "2236": 604, "2237": 605, "2238": 606, "2239": 607, "2240": 608, "2241": 609, "2242": 610, "2245": 611, "2248": 612, "2249": 613, "2253": 614, "2254": 615, "2255": 616, "2262": 617, "2266": 618, "2267": 619, "2268": 620, "2269": 621, "2270": 622, "2271": 623, "2287": 624, "2294": 625, "2299": 626, "2300": 627, "2301": 628, "2302": 629, "2527": 630, "2528": 631, "2529": 632, "2530": 633, "2531": 634, "2532": 635, "2533": 636, "2536": 637, "2537": 638, "2538": 639, "2539": 640, "2540": 641, "2541": 642, "2542": 643, "2543": 644, "2544": 645, "2545": 646, "2546": 647, "2547": 648, "2548": 649, "2549": 650, "2550": 651, "2551": 652, "2552": 653, "2721": 654, "2722": 655, "2723": 656, "2724": 657, "2725": 658, "2726": 659, "2727": 660, "2728": 661, "2729": 662, "2730": 663, "2732": 664, "2734": 665, "2735": 666, "2736": 667, "2737": 668, "2738": 669, "2739": 670, "2740": 671, "2741": 672, "2742": 673, "2743": 674, "2744": 675, "2747": 676, "2748": 677, "2749": 678, "2750": 679, "2751": 680, "2755": 681, "2756": 682, "2757": 683, "2758": 684, "2761": 685, "2762": 686, "2763": 687, "2764": 688, "2765": 689, "2766": 690, "2767": 691, "2768": 692, "2769": 693, "2770": 694, "2771": 695, "2772": 696, "2774": 697, "2775": 698, "2777": 699, "2778": 700, "2779": 701, "2780": 702, "2781": 703, "2782": 704, "2783": 705, "2784": 706, "2785": 707, "2786": 708, "2787": 709, "2788": 710, "2789": 711, "2790": 712, "2791": 713, "2792": 714, "2793": 715, "2794": 716, "2795": 717, "2796": 718, "2797": 719, "2798": 720, "2800": 721, "2801": 722, "2802": 723, "2803": 724, "2804": 725, "2805": 726, "2806": 727, "2807": 728, "2808": 729, "2809": 730, "2810": 731, "2811": 732, "2812": 733, "2813": 734, "2814": 735, "2815": 736, "2979": 737, "2982": 738, "2984": 739, "2985": 740, "3008": 741, "3010": 742, "3011": 743, "3012": 744, "3014": 745, "3018": 746, "3020": 747, "3022": 748, "3023": 749, "3027": 750, "3030": 751, "3032": 752, "3034": 753, "3036": 754, "3068": 755, "3295": 756, "3296": 757, "3297": 758, "3298": 759, "3299": 760, "3300": 761, "3301": 762, "3302": 763, "3303": 764, "3304": 765, "3305": 766, "3306": 767, "3307": 768, "3308": 769, "3309": 770, "3310": 771, "3311": 772, "3312": 773, "3313": 774, "3314": 775, "3315": 776, "3316": 777, "3317": 778, "3318": 779, "3319": 780, "3320": 781, "3321": 782, "3322": 783, "3489": 784, "3490": 785, "3491": 786, "3492": 787, "3493": 788, "3494": 789, "3495": 790, "3496": 791, "3497": 792, "3498": 793, "3499": 794, "3500": 795, "3501": 796, "3502": 797, "3503": 798, "3504": 799, "3505": 800, "3506": 801, "3507": 802, "3508": 803, "3509": 804, "3510": 805, "3511": 806, "3512": 807, "3513": 808, "3514": 809, "3515": 810, "3516": 811, "3517": 812, "3518": 813, "3519": 814, "3520": 815, "3521": 816, "3522": 817, "3523": 818, "3524": 819, "3525": 820, "3526": 821, "3527": 822, "3528": 823, "3529": 824, "3530": 825, "3531": 826, "3532": 827, "3533": 828, "3534": 829, "3535": 830, "3536": 831, "3537": 832, "3538": 833, "3539": 834, "3540": 835, "3541": 836, "3542": 837, "3543": 838, "3544": 839, "3545": 840, "3546": 841, "3550": 842, "3551": 843, "3552": 844, "3553": 845, "3554": 846, "3555": 847, "3556": 848, "3557": 849, "3558": 850, "3559": 851, "3560": 852, "3561": 853, "3562": 854, "3563": 855, "3564": 856, "3565": 857, "3568": 858, "3569": 859, "3570": 860, "3571": 861, "3572": 862, "3573": 863, "3574": 864, "3575": 865, "3576": 866, "3577": 867, "3745": 868, "3746": 869, "3747": 870, "3748": 871, "3749": 872, "3750": 873, "3751": 874, "3752": 875, "3753": 876, "3754": 877, "3755": 878, "3756": 879, "3757": 880, "3758": 881, "3759": 882, "3760": 883, "3761": 884, "3762": 885, "3763": 886, "3764": 887, "3765": 888, "3766": 889, "3767": 890, "3768": 891, "3769": 892, "3770": 893, "3771": 894, "3772": 895, "3773": 896, "3774": 897, "3775": 898, "3776": 899, "3777": 900, "3778": 901, "3779": 902, "3780": 903, "3781": 904, "3782": 905, "3783": 906, "3784": 907, "3785": 908, "3786": 909, "3787": 910, "3788": 911, "3789": 912, "3790": 913, "3791": 914, "3792": 915, "3793": 916, "3794": 917, "3795": 918, "3796": 919, "3797": 920, "3798": 921, "3799": 922, "3800": 923, "3801": 924, "3802": 925, "3803": 926, "3804": 927, "3805": 928, "3806": 929, "3807": 930, "3808": 931, "3809": 932, "3810": 933, "3811": 934, "3812": 935, "3813": 936, "3814": 937, "3815": 938, "3816": 939, "3817": 940, "3818": 941, "3819": 942, "3820": 943, "3821": 944, "3822": 945, "3823": 946, "3824": 947, "3825": 948, "3826": 949, "3827": 950, "3828": 951, "3829": 952, "3830": 953, "3831": 954, "3832": 955, "3833": 956, "3834": 957, "3839": 958, "4769": 1388, "4770": 1389, "4774": 1390, "4776": 1391, "4778": 1392, "4779": 1393, "4780": 1394, "4784": 1395, "4785": 1396, "4788": 1397, "4789": 1398, "4791": 1399, "4792": 1400, "4793": 1401, "4794": 1402, "4795": 1403, "4796": 1404, "4797": 1405, "4798": 1406, "4799": 1407, "4816": 1408, "4823": 1409, "4830": 1410, "4848": 1411, "4855": 1412, "4862": 1413, "5052": 959, "5053": 960, "5054": 961, "5281": 1254, "5282": 1255, "5283": 1256, "5284": 1257, "5285": 1258, "5286": 1259, "5287": 1260, "5288": 1261, "5289": 1262, "5290": 1263, "5291": 1264, "5292": 1265, "5293": 1266, "5294": 1267, "5295": 1268, "5296": 1269, "5297": 1270, "5298": 1271, "5299": 1272, "5300": 1273, "5301": 1274, "5302": 1275, "5303": 1276, "5304": 1277, "5305": 1278, "5306": 1279, "5307": 1280, "5308": 1281, "5309": 1282, "5310": 1283, "5311": 1284, "5312": 1285, "5313": 1286, "5314": 1287, "5315": 1288, "5316": 1289, "5317": 1290, "5318": 1291, "5319": 1292, "5320": 1293, "5321": 1294, "5322": 1295, "5323": 1296, "5324": 1297, "5325": 1298, "5326": 1299, "5327": 1300, "5328": 1301, "5329": 1302, "5330": 1303, "5331": 1304, "5332": 1305, "5333": 1306, "5334": 1307, "5335": 1308, "5336": 1309, "5337": 1310, "5338": 1311, "5339": 1312, "5340": 1313, "5341": 1314, "5342": 1315, "5343": 1316, "5344": 1317, "5345": 1318, "5346": 1319, "5347": 1320, "5348": 1321, "5349": 1322, "5350": 1323, "5351": 1324, "5352": 1325, "5353": 1326, "5354": 1327, "5355": 1328, "5356": 1329, "5357": 1330, "5358": 1331, "5359": 1332, "5360": 1333, "5361": 1334, "5362": 1335, "5363": 1336, "5364": 1337, "5365": 1338, "5366": 1339, "5367": 1340, "5368": 1341, "5369": 1342, "5370": 1343, "5371": 1344, "5372": 1345, "5373": 1346, "5374": 1347, "5375": 1348, "5584": 1349, "5585": 1350, "5586": 1351, "5587": 1352, "5588": 1353, "5589": 1354, "5590": 1355, "5591": 1356, "5592": 1357, "5593": 1358, "5594": 1359, "5595": 1360, "5596": 1361, "5597": 1362, "5598": 1363, "5599": 1364, "5600": 1365, "5601": 1366, "5602": 1367, "5603": 1368, "5604": 1369, "5605": 1370, "5606": 1371, "5607": 1372, "5608": 1373, "5609": 1374, "5610": 1375, "5611": 1376, "5612": 1377, "5613": 1378, "5614": 1379, "5615": 1380, "5616": 1381, "5617": 1382, "5618": 1383, "5619": 1384, "5620": 1385, "5621": 1386, "5622": 1387, "5794": 1484, "5795": 1485, "5797": 1486, "5798": 1487, "5799": 1488, "5800": 1489, "5801": 1490, "5802": 1491, "5807": 1492, "5810": 1493, "5811": 1494, "5812": 1495, "5813": 1496, "5814": 1497, "5815": 1498, "5816": 1499, "5817": 1500, "5818": 1501, "5821": 1502, "5823": 1503, "5830": 1504, "5841": 1506, "5842": 1507, "5843": 1508, "5857": 1509, "5858": 1510, "5859": 1511, "5878": 1505, "7839": 1604, "7840": 1512, "7841": 1513, "7842": 1514, "7843": 1515, "7844": 1516, "7845": 1517, "7846": 1518, "7847": 1519, "7848": 1520, "7849": 1521, "7850": 1522, "7851": 1523, "7852": 1524, "7853": 1525, "7854": 1526, "7855": 1527, "7856": 1528, "7857": 1529, "7858": 1530, "7859": 1531, "7860": 1532, "7861": 1533, "7862": 1534, "7863": 1535, "7864": 1536, "7865": 1537, "7866": 1538, "7867": 1539, "7868": 1540, "7869": 1541, "7870": 1542, "7871": 1543, "7872": 1544, "7873": 1545, "7874": 1546, "7875": 1547, "7876": 1548, "7877": 1549, "7878": 1550, "7879": 1551, "7880": 1552, "7881": 1553, "7882": 1554, "7883": 1555, "7884": 1556, "7885": 1557, "7886": 1558, "7887": 1559, "7888": 1560, "7889": 1561, "7890": 1562, "7891": 1563, "7892": 1564, "7893": 1565, "7894": 1566, "7895": 1567, "7896": 1568, "7897": 1569, "7898": 1570, "7899": 1571, "7900": 1572, "7901": 1573, "7902": 1574, "7903": 1575, "7904": 1576, "7905": 1577, "7906": 1578, "7907": 1579, "7908": 1580, "7909": 1581, "7910": 1582, "7911": 1583, "7912": 1584, "7913": 1585, "7914": 1586, "7915": 1587, "7916": 1588, "7917": 1589, "7918": 1590, "7919": 1591, "7920": 1592, "7921": 1593, "7922": 1605, "7923": 1606, "7924": 1594, "7925": 1595, "7926": 1596, "7927": 1597, "7928": 1598, "7929": 1599, "7930": 1600, "7931": 1601, "7932": 1602, "7933": 1603, "7934": 1607, "7935": 1608, "8352": 962, "8353": 963, "8354": 964, "8355": 965, "8356": 966, "8357": 967, "8358": 968, "8359": 969, "8360": 970, "8361": 971, "8362": 972, "8363": 973, "8364": 974, "64769": 975, "64770": 976, "64771": 977, "64772": 978, "64773": 979, "64774": 980, "64775": 981, "64776": 982, "64777": 983, "64778": 984, "64779": 985, "64780": 986, "64781": 987, "64782": 988, "64783": 989, "64784": 990, "64785": 991, "64786": 992, "64787": 993, "64788": 994, "64789": 995, "64790": 996, "64791": 997, "64792": 998, "64793": 999, "64794": 1000, "64795": 1001, "64796": 1002, "64797": 1003, "64798": 1004, "65025": 1005, "65026": 1006, "65027": 1007, "65028": 1008, "65029": 1009, "65030": 1010, "65031": 1011, "65032": 1012, "65033": 1013, "65034": 1014, "65035": 1015, "65036": 1016, "65037": 1017, "65038": 1018, "65039": 1019, "65056": 1020, "65057": 1021, "65058": 1022, "65059": 1023, "65060": 1024, "65061": 1025, "65062": 1026, "65063": 1027, "65064": 1028, "65065": 1029, "65066": 1030, "65067": 1031, "65068": 1032, "65069": 1033, "65070": 1034, "65071": 1035, "65072": 1036, "65073": 1037, "65074": 1038, "65075": 1039, "65076": 1040, "65104": 1041, "65105": 1042, "65106": 1043, "65107": 1044, "65108": 1045, "65109": 1046, "65110": 1047, "65111": 1048, "65112": 1049, "65113": 1050, "65114": 1051, "65115": 1052, "65116": 1053, "65117": 1054, "65118": 1055, "65119": 1056, "65120": 1609, "65121": 1610, "65122": 1611, "65136": 1057, "65137": 1058, "65138": 1059, "65139": 1060, "65140": 1061, "65141": 1062, "65142": 1063, "65143": 1064, "65144": 1065, "65145": 1066, "65146": 1067, "65232": 1068, "65233": 1069, "65234": 1070, "65236": 1071, "65237": 1072, "65248": 1073, "65249": 1074, "65250": 1075, "65251": 1076, "65252": 1077, "65253": 1078, "65254": 1079, "65255": 1080, "65256": 1081, "65257": 1082, "65258": 1083, "65259": 1084, "65260": 1085, "65261": 1086, "65262": 1087, "65263": 1088, "65264": 1089, "65265": 1090, "65266": 1091, "65267": 1092, "65268": 1093, "65269": 1094, "65270": 1095, "65271": 1096, "65272": 1097, "65273": 1098, "65274": 1099, "65275": 1100, "65276": 1101, "65277": 1102, "65288": 1103, "65289": 1104, "65290": 1105, "65291": 1106, "65293": 1107, "65299": 1108, "65300": 1109, "65301": 1110, "65307": 1111, "65312": 1112, "65313": 1113, "65314": 1114, "65315": 1115, "65316": 1116, "65317": 1117, "65318": 1118, "65319": 1119, "65320": 1120, "65321": 1121, "65322": 1122, "65323": 1123, "65324": 1124, "65325": 1125, "65326": 1126, "65327": 1127, "65328": 1128, "65329": 1129, "65330": 1130, "65331": 1131, "65332": 1132, "65333": 1133, "65334": 1134, "65335": 1135, "65336": 1136, "65337": 1137, "65338": 1138, "65339": 1139, "65340": 1140, "65341": 1141, "65342": 1142, "65343": 1143, "65360": 1144, "65361": 1145, "65362": 1146, "65363": 1147, "65364": 1148, "65365": 1149, "65366": 1150, "65367": 1151, "65368": 1152, "65376": 1153, "65377": 1154, "65378": 1155, "65379": 1156, "65381": 1157, "65382": 1158, "65383": 1159, "65384": 1160, "65385": 1161, "65386": 1162, "65387": 1163, "65406": 1164, "65407": 1165, "65408": 1166, "65417": 1167, "65421": 1168, "65425": 1169, "65426": 1170, "65427": 1171, "65428": 1172, "65429": 1173, "65430": 1174, "65431": 1175, "65432": 1176, "65433": 1177, "65434": 1178, "65435": 1179, "65436": 1180, "65437": 1181, "65438": 1182, "65439": 1183, "65450": 1184, "65451": 1185, "65452": 1186, "65453": 1187, "65454": 1188, "65455": 1189, "65456": 1190, "65457": 1191, "65458": 1192, "65459": 1193, "65460": 1194, "65461": 1195, "65462": 1196, "65463": 1197, "65464": 1198, "65465": 1199, "65469": 1200, "65470": 1201, "65471": 1202, "65472": 1203, "65473": 1204, "65474": 1205, "65475": 1206, "65476": 1207, "65477": 1208, "65478": 1209, "65479": 1210, "65480": 1211, "65481": 1212, "65482": 1213, "65483": 1214, "65484": 1215, "65485": 1216, "65486": 1217, "65487": 1218, "65488": 1219, "65489": 1220, "65490": 1221, "65491": 1222, "65492": 1223, "65493": 1224, "65494": 1225, "65495": 1226, "65496": 1227, "65497": 1228, "65498": 1229, "65499": 1230, "65500": 1231, "65501": 1232, "65502": 1233, "65503": 1234, "65504": 1235, "65505": 1236, "65506": 1237, "65507": 1238, "65508": 1239, "65509": 1240, "65510": 1241, "65511": 1242, "65512": 1243, "65513": 1244, "65514": 1245, "65515": 1246, "65516": 1247, "65517": 1248, "65518": 1249, "65535": 1250, "16777215": 1251}, "unicodes": {"0": [597, 598, 599, 600, 601, 602, 603, 630, 680, 687, 700, 736, 842, 975, 976, 977, 978, 979, 980, 981, 982, 983, 984, 985, 986, 987, 988, 989, 990, 991, 992, 993, 994, 995, 996, 997, 998, 999, 1000, 1001, 1002, 1003, 1004, 1005, 1006, 1007, 1008, 1009, 1010, 1011, 1012, 1013, 1014, 1015, 1016, 1017, 1018, 1019, 1020, 1021, 1022, 1023, 1024, 1025, 1026, 1027, 1028, 1029, 1030, 1031, 1032, 1033, 1034, 1035, 1036, 1037, 1038, 1039, 1040, 1057, 1058, 1059, 1060, 1061, 1062, 1063, 1064, 1065, 1066, 1067, 1068, 1069, 1070, 1071, 1072, 1073, 1074, 1075, 1076, 1077, 1078, 1079, 1080, 1081, 1082, 1083, 1084, 1085, 1086, 1087, 1088, 1089, 1090, 1091, 1092, 1093, 1094, 1095, 1096, 1097, 1098, 1099, 1100, 1101, 1102, 1112, 1113, 1114, 1115, 1116, 1117, 1118, 1119, 1120, 1121, 1122, 1123, 1124, 1125, 1126, 1127, 1128, 1129, 1130, 1131, 1132, 1133, 1134, 1135, 1136, 1137, 1138, 1139, 1140, 1141, 1142, 1143, 1144, 1145, 1146, 1147, 1148, 1149, 1150, 1151, 1152, 1153, 1154, 1155, 1156, 1157, 1158, 1159, 1160, 1161, 1162, 1163, 1164, 1165, 1169, 1170, 1171, 1172, 1173, 1174, 1175, 1176, 1177, 1178, 1179, 1180, 1181, 1182, 1183, 1201, 1202, 1203, 1204, 1205, 1206, 1207, 1208, 1209, 1210, 1211, 1212, 1213, 1214, 1215, 1216, 1217, 1218, 1219, 1220, 1221, 1222, 1223, 1224, 1225, 1226, 1227, 1228, 1229, 1230, 1231, 1232, 1233, 1234, 1235, 1236, 1237, 1238, 1239, 1240, 1241, 1242, 1243, 1244, 1245, 1246, 1247, 1248, 1249, 1250, 1251, 1254, 1484, 1486, 1488, 1489, 1493, 1495, 1496, 1498, 1499, 1507, 1508, 1510, 1511], "8": [1103], "9": [1104, 1167], "10": [1105], "11": [1106], "13": [1107, 1168], "19": [1108], "20": [1109], "21": [1110], "27": [1111], "32": [0, 1166], "33": [1], "34": [2], "35": [3], "36": [4], "37": [5], "38": [6], "39": [7], "40": [8, 1258], "41": [9, 1257], "42": [10, 1184], "43": [11, 1185], "44": [12, 1186, 1264], "45": [13, 1187], "46": [14, 678, 1188, 1262], "47": [15, 1189], "48": [16, 1190], "49": [17, 1191], "50": [18, 1192], "51": [19, 1193], "52": [20, 1194], "53": [21, 1195], "54": [22, 1196], "55": [23, 1197], "56": [24, 1198], "57": [25, 1199], "58": [26], "59": [27], "60": [28, 737], "61": [29, 1200], "62": [30, 738], "63": [31], "64": [32], "65": [33], "66": [34], "67": [35], "68": [36], "69": [37], "70": [38], "71": [39], "72": [40], "73": [41], "74": [42], "75": [43], "76": [44], "77": [45], "78": [46], "79": [47], "80": [48], "81": [49], "82": [50], "83": [51], "84": [52], "85": [53], "86": [54], "87": [55], "88": [56], "89": [57], "90": [58], "91": [59], "92": [60], "93": [61], "94": [62], "95": [63, 745], "96": [64], "97": [65], "98": [66], "99": [67], "100": [68], "101": [69], "102": [70], "103": [71], "104": [72], "105": [73], "106": [74], "107": [75], "108": [76], "109": [77], "110": [78], "111": [79], "112": [80], "113": [81], "114": [82], "115": [83], "116": [84], "117": [85], "118": [86], "119": [87], "120": [88], "121": [89], "122": [90], "123": [91], "124": [92], "125": [93], "126": [94], "160": [95], "161": [96], "162": [97], "163": [98], "164": [99], "165": [100], "166": [101], "167": [102, 1348], "168": [103], "169": [104], "170": [105], "171": [106, 1260], "172": [107], "173": [108], "174": [109], "175": [110, 741], "176": [111], "177": [112], "178": [113], "179": [114], "180": [115], "181": [116], "182": [117], "183": [118], "184": [119], "185": [120], "186": [121], "187": [122, 1259], "188": [123], "189": [124], "190": [125], "191": [126], "192": [127], "193": [128], "194": [129], "195": [130], "196": [131], "197": [132], "198": [133], "199": [134], "200": [135], "201": [136], "202": [137], "203": [138], "204": [139], "205": [140], "206": [141], "207": [142], "208": [143], "209": [144], "210": [145], "211": [146], "212": [147], "213": [148], "214": [149], "215": [150], "216": [151], "217": [152], "218": [153], "219": [154], "220": [155], "221": [156], "222": [157], "223": [158], "224": [159], "225": [160], "226": [161], "227": [162], "228": [163], "229": [164], "230": [165], "231": [166], "232": [167], "233": [168], "234": [169], "235": [170], "236": [171], "237": [172], "238": [173], "239": [174], "240": [175], "241": [176], "242": [177], "243": [178], "244": [179], "245": [180], "246": [181], "247": [182], "248": [183], "249": [184], "250": [185], "251": [186], "252": [187], "253": [188], "254": [189], "255": [190], "256": [285], "257": [295], "258": [216], "259": [232], "260": [191], "261": [202], "262": [218], "263": [234], "264": [259], "265": [265], "266": [258], "267": [264], "268": [219], "269": [235], "270": [222], "271": [238], "272": [223], "273": [239], "274": [274], "275": [280], "278": [287], "279": [297], "280": [220], "281": [236], "282": [221], "283": [237], "284": [261], "285": [267], "286": [251], "287": [256], "288": [260], "289": [266], "290": [275], "291": [281], "292": [249], "293": [254], "294": [248], "295": [253], "296": [272], "297": [278], "298": [288], "299": [298], "300": [1487], "301": [1497], "302": [286], "303": [296], "304": [250], "305": [255], "308": [252], "309": [257], "310": [291], "311": [301], "312": [270], "313": [217], "314": [233], "315": [273], "316": [279], "317": [194], "318": [205], "321": [193], "322": [204], "323": [224], "324": [240], "325": [289], "326": [299], "327": [225], "328": [241], "330": [283], "331": [284], "332": [290], "333": [300], "336": [226], "337": [242], "338": [959], "339": [960], "340": [215], "341": [231], "342": [271], "343": [277], "344": [227], "345": [243], "346": [195], "347": [206], "348": [263], "349": [269], "350": [197], "351": [209], "352": [196], "353": [208], "354": [230], "355": [246], "356": [198], "357": [210], "358": [276], "359": [282], "360": [293], "361": [303], "362": [294], "363": [304], "364": [262], "365": [268], "366": [228], "367": [244], "368": [229], "369": [245], "370": [292], "371": [302], "372": [1408], "373": [1411], "374": [1410], "375": [1413], "376": [961], "377": [199], "378": [211], "379": [201], "380": [214], "381": [200], "382": [213], "399": [1504], "402": [625], "415": [1492], "416": [1600], "417": [1601], "431": [1602], "432": [1603], "437": [1490], "438": [1500], "466": [1502], "486": [1491], "487": [1501], "601": [1505], "629": [1503], "711": [207], "728": [192], "729": [247], "731": [203], "733": [212], "768": [1041, 1605], "769": [1042, 1606], "770": [1043], "771": [1044, 1604], "772": [1045], "774": [1046], "775": [1047], "776": [1048], "777": [1607, 1610], "778": [1049], "779": [1050], "780": [1051], "795": [1611], "803": [1608, 1609], "807": [1052], "808": [1053], "837": [1054], "901": [519], "902": [510], "904": [511], "905": [512], "906": [513], "908": [515], "910": [516], "911": [518], "912": [526], "913": [532], "914": [533], "915": [534], "916": [535], "917": [536], "918": [537], "919": [538], "920": [539], "921": [540], "922": [541], "923": [542], "924": [543], "925": [544], "926": [545], "927": [546], "928": [547], "929": [548], "931": [549], "932": [550], "933": [551], "934": [552], "935": [553], "936": [554], "937": [555], "938": [514], "939": [517], "940": [521], "941": [522], "942": [523], "943": [524], "944": [530], "945": [556], "946": [557], "947": [558], "948": [559], "949": [560], "950": [561], "951": [562], "952": [563], "953": [564], "954": [565], "955": [566], "956": [567], "957": [568], "958": [569], "959": [570], "960": [571], "961": [572], "962": [574], "963": [573], "964": [575], "965": [576], "966": [577], "967": [578], "968": [579], "969": [580], "970": [525], "971": [529], "972": [527], "973": [528], "974": [531], "1025": [434], "1026": [432], "1027": [433], "1028": [435], "1029": [436], "1030": [437], "1031": [438], "1032": [439], "1033": [440], "1034": [441], "1035": [442], "1036": [443], "1038": [444], "1039": [445], "1040": [479], "1041": [480], "1042": [501], "1043": [485], "1044": [482], "1045": [483], "1046": [500], "1047": [504], "1048": [487], "1049": [488], "1050": [489], "1051": [490], "1052": [491], "1053": [492], "1054": [493], "1055": [494], "1056": [496], "1057": [497], "1058": [498], "1059": [499], "1060": [484], "1061": [486], "1062": [481], "1063": [508], "1064": [505], "1065": [507], "1066": [509], "1067": [503], "1068": [502], "1069": [506], "1070": [478], "1071": [495], "1072": [447], "1073": [448], "1074": [469], "1075": [453], "1076": [450], "1077": [451], "1078": [468], "1079": [472], "1080": [455], "1081": [456], "1082": [457], "1083": [458], "1084": [459], "1085": [460], "1086": [461], "1087": [462], "1088": [464], "1089": [465], "1090": [466], "1091": [467], "1092": [452], "1093": [454], "1094": [449], "1095": [476], "1096": [473], "1097": [475], "1098": [477], "1099": [471], "1100": [470], "1101": [474], "1102": [446], "1103": [463], "1105": [419], "1106": [417], "1107": [418], "1108": [420], "1109": [421], "1110": [422], "1111": [423], "1112": [424], "1113": [425], "1114": [426], "1115": [427], "1116": [428], "1118": [429], "1119": [430], "1168": [1253], "1169": [1252], "1170": [1454], "1171": [1469], "1174": [1455], "1175": [1470], "1178": [1456], "1179": [1471], "1180": [1457], "1181": [1472], "1186": [1458], "1187": [1473], "1198": [1459], "1199": [1474], "1200": [1460], "1201": [1475], "1202": [1461], "1203": [1476], "1206": [1462], "1207": [1477], "1208": [1463], "1209": [1478], "1210": [1464], "1211": [1479], "1240": [1465], "1241": [1480], "1250": [1466], "1251": [1481], "1256": [1467], "1257": [1482], "1262": [1468], "1263": [1483], "1329": [1271], "1330": [1273], "1331": [1275], "1332": [1277], "1333": [1279], "1334": [1281], "1335": [1283], "1336": [1285], "1337": [1287], "1338": [1289], "1339": [1291], "1340": [1293], "1341": [1295], "1342": [1297], "1343": [1299], "1344": [1301], "1345": [1303], "1346": [1305], "1347": [1307], "1348": [1309], "1349": [1311], "1350": [1313], "1351": [1315], "1352": [1317], "1353": [1319], "1354": [1321], "1355": [1323], "1356": [1325], "1357": [1327], "1358": [1329], "1359": [1331], "1360": [1333], "1361": [1335], "1362": [1337], "1363": [1339], "1364": [1341], "1365": [1343], "1366": [1345], "1370": [1347], "1371": [1269], "1372": [1268], "1373": [1263], "1374": [1270], "1377": [1272], "1378": [1274], "1379": [1276], "1380": [1278], "1381": [1280], "1382": [1282], "1383": [1284], "1384": [1286], "1385": [1288], "1386": [1290], "1387": [1292], "1388": [1294], "1389": [1296], "1390": [1298], "1391": [1300], "1392": [1302], "1393": [1304], "1394": [1306], "1395": [1308], "1396": [1310], "1397": [1312], "1398": [1314], "1399": [1316], "1400": [1318], "1401": [1320], "1402": [1322], "1403": [1324], "1404": [1326], "1405": [1328], "1406": [1330], "1407": [1332], "1408": [1334], "1409": [1336], "1410": [1338], "1411": [1340], "1412": [1342], "1413": [1344], "1414": [1346], "1415": [1255], "1417": [1256], "1418": [1266], "1488": [757], "1489": [758], "1490": [759], "1491": [760], "1492": [761], "1493": [762], "1494": [763], "1495": [764], "1496": [765], "1497": [766], "1498": [767], "1499": [768], "1500": [769], "1501": [770], "1502": [771], "1503": [772], "1504": [773], "1505": [774], "1506": [775], "1507": [776], "1508": [777], "1509": [778], "1510": [779], "1511": [780], "1512": [781], "1513": [782], "1514": [783], "1548": [369], "1563": [370], "1567": [371], "1569": [372], "1570": [373], "1571": [374], "1572": [375], "1573": [376], "1574": [377], "1575": [378], "1576": [379], "1577": [380], "1578": [381], "1579": [382], "1580": [383], "1581": [384], "1582": [385], "1583": [386], "1584": [387], "1585": [388], "1586": [389], "1587": [390], "1588": [391], "1589": [392], "1590": [393], "1591": [394], "1592": [395], "1593": [396], "1594": [397], "1600": [398], "1601": [399], "1602": [400], "1603": [401], "1604": [402], "1605": [403], "1606": [404], "1607": [405], "1608": [406], "1609": [407], "1610": [408], "1611": [409], "1612": [410], "1613": [411], "1614": [412], "1615": [413], "1616": [414], "1617": [415], "1618": [416], "1619": [1442], "1620": [1443], "1621": [1444], "1632": [1432], "1633": [1433], "1634": [1434], "1635": [1435], "1636": [1436], "1637": [1437], "1638": [1438], "1639": [1439], "1640": [1440], "1641": [1441], "1642": [1424], "1648": [1425], "1657": [1426], "1662": [1427], "1670": [1428], "1672": [1429], "1681": [1430], "1688": [1445], "1700": [1446], "1705": [1447], "1711": [1448], "1722": [1449], "1726": [1450], "1729": [1453], "1740": [1451], "1746": [1452], "1748": [1431], "1776": [1414], "1777": [1415], "1778": [1416], "1779": [1417], "1780": [1418], "1781": [1419], "1782": [1420], "1783": [1421], "1784": [1422], "1785": [1423], "3585": [784], "3586": [785], "3587": [786], "3588": [787], "3589": [788], "3590": [789], "3591": [790], "3592": [791], "3593": [792], "3594": [793], "3595": [794], "3596": [795], "3597": [796], "3598": [797], "3599": [798], "3600": [799], "3601": [800], "3602": [801], "3603": [802], "3604": [803], "3605": [804], "3606": [805], "3607": [806], "3608": [807], "3609": [808], "3610": [809], "3611": [810], "3612": [811], "3613": [812], "3614": [813], "3615": [814], "3616": [815], "3617": [816], "3618": [817], "3619": [818], "3620": [819], "3621": [820], "3622": [821], "3623": [822], "3624": [823], "3625": [824], "3626": [825], "3627": [826], "3628": [827], "3629": [828], "3630": [829], "3631": [830], "3632": [831], "3633": [832], "3634": [833], "3635": [834], "3636": [835], "3637": [836], "3638": [837], "3639": [838], "3640": [839], "3641": [840], "3642": [841], "3647": [843], "3648": [844], "3649": [845], "3650": [846], "3651": [847], "3652": [848], "3653": [849], "3654": [850], "3655": [851], "3656": [852], "3657": [853], "3658": [854], "3659": [855], "3660": [856], "3661": [857], "3664": [858], "3665": [859], "3666": [860], "3667": [861], "3668": [862], "3669": [863], "3670": [864], "3671": [865], "3672": [866], "3673": [867], "4304": [1349], "4305": [1350], "4306": [1351], "4307": [1352], "4308": [1353], "4309": [1354], "4310": [1355], "4311": [1356], "4312": [1357], "4313": [1358], "4314": [1359], "4315": [1360], "4316": [1361], "4317": [1362], "4318": [1363], "4319": [1364], "4320": [1365], "4321": [1366], "4322": [1367], "4323": [1368], "4324": [1369], "4325": [1370], "4326": [1371], "4327": [1372], "4328": [1373], "4329": [1374], "4330": [1375], "4331": [1376], "4332": [1377], "4333": [1378], "4334": [1379], "4335": [1380], "4336": [1381], "4337": [1382], "4338": [1383], "4339": [1384], "4340": [1385], "4341": [1386], "4342": [1387], "4520": [919], "4521": [920], "4522": [921], "4523": [922], "4524": [923], "4525": [924], "4526": [925], "4527": [926], "4528": [927], "4529": [928], "4530": [929], "4531": [930], "4532": [931], "4533": [932], "4534": [933], "4535": [934], "4536": [935], "4537": [936], "4538": [937], "4539": [938], "4540": [939], "4541": [940], "4542": [941], "4543": [942], "4544": [943], "4545": [944], "4546": [945], "4587": [955], "4592": [956], "4601": [957], "7682": [1388], "7683": [1389], "7690": [1390], "7691": [1393], "7710": [1395], "7711": [1396], "7734": [1506], "7735": [1509], "7744": [1397], "7745": [1398], "7766": [1399], "7767": [1401], "7776": [1403], "7777": [1407], "7786": [1409], "7787": [1412], "7808": [1391], "7809": [1400], "7810": [1392], "7811": [1402], "7812": [1405], "7813": [1406], "7818": [1485], "7819": [1494], "7840": [1512], "7841": [1513], "7842": [1514], "7843": [1515], "7844": [1516], "7845": [1517], "7846": [1518], "7847": [1519], "7848": [1520], "7849": [1521], "7850": [1522], "7851": [1523], "7852": [1524], "7853": [1525], "7854": [1526], "7855": [1527], "7856": [1528], "7857": [1529], "7858": [1530], "7859": [1531], "7860": [1532], "7861": [1533], "7862": [1534], "7863": [1535], "7864": [1536], "7865": [1537], "7866": [1538], "7867": [1539], "7868": [1540], "7869": [1541], "7870": [1542], "7871": [1543], "7872": [1544], "7873": [1545], "7874": [1546], "7875": [1547], "7876": [1548], "7877": [1549], "7878": [1550], "7879": [1551], "7880": [1552], "7881": [1553], "7882": [1554], "7883": [1555], "7884": [1556], "7885": [1557], "7886": [1558], "7887": [1559], "7888": [1560], "7889": [1561], "7890": [1562], "7891": [1563], "7892": [1564], "7893": [1565], "7894": [1566], "7895": [1567], "7896": [1568], "7897": [1569], "7898": [1570], "7899": [1571], "7900": [1572], "7901": [1573], "7902": [1574], "7903": [1575], "7904": [1576], "7905": [1577], "7906": [1578], "7907": [1579], "7908": [1580], "7909": [1581], "7910": [1582], "7911": [1583], "7912": [1584], "7913": [1585], "7914": [1586], "7915": [1587], "7916": [1588], "7917": [1589], "7918": [1590], "7919": [1591], "7920": [1592], "7921": [1593], "7922": [1394], "7923": [1404], "7924": [1594], "7925": [1595], "7926": [1596], "7927": [1597], "7928": [1598], "7929": [1599], "8194": [655], "8195": [654], "8196": [656], "8197": [657], "8199": [658], "8200": [659], "8201": [660], "8202": [661], "8210": [676], "8211": [663, 1265], "8212": [662, 1261], "8213": [520], "8215": [756], "8216": [692], "8217": [693], "8218": [734], "8220": [694], "8221": [695], "8222": [735], "8224": [722], "8225": [723], "8226": [712], "8229": [666], "8230": [665, 1267], "8242": [697], "8243": [698], "8248": [733], "8254": [305], "8352": [962], "8353": [963], "8354": [964], "8355": [965], "8356": [966], "8357": [967], "8358": [968], "8359": [969], "8360": [970], "8361": [958, 971], "8362": [972], "8363": [973], "8364": [974], "8453": [675], "8470": [431], "8471": [732], "8478": [696], "8482": [685], "8531": [667], "8532": [668], "8533": [669], "8534": [670], "8535": [671], "8536": [672], "8537": [673], "8538": [674], "8539": [681], "8540": [682], "8541": [683], "8542": [684], "8592": [626], "8593": [627], "8594": [628], "8595": [629], "8658": [615], "8660": [614], "8706": [624], "8711": [611], "8728": [746], "8730": [617], "8733": [609], "8734": [610], "8743": [622, 740], "8744": [623, 739], "8745": [620, 743], "8746": [621, 751], "8747": [607], "8756": [608], "8764": [612], "8771": [613], "8800": [605], "8801": [616], "8804": [604], "8805": [606], "8834": [618, 753], "8835": [619, 752], "8866": [754], "8867": [755], "8868": [748], "8869": [742], "8968": [750], "8970": [744], "8981": [731], "8992": [584], "8993": [585], "9109": [747], "9115": [591], "9117": [592], "9118": [593], "9120": [594], "9121": [587], "9123": [588], "9124": [589], "9126": [590], "9128": [595], "9132": [596], "9143": [581], "9146": [644], "9147": [645], "9148": [647], "9149": [648], "9225": [633], "9226": [636], "9227": [638], "9228": [634], "9229": [635], "9251": [664], "9252": [637], "9472": [583, 646], "9474": [586, 653], "9484": [582, 641], "9488": [640], "9492": [642], "9496": [639], "9500": [649], "9508": [650], "9516": [652], "9524": [651], "9532": [643], "9618": [632], "9642": [713], "9643": [707], "9644": [701], "9645": [708], "9646": [705], "9647": [691], "9650": [714], "9651": [709], "9654": [703], "9655": [689], "9660": [715], "9661": [710], "9664": [702], "9665": [688], "9670": [631], "9675": [690, 749], "9679": [704], "9702": [706], "9734": [711], "9742": [730], "9747": [686], "9756": [716], "9758": [717], "9792": [729], "9794": [728], "9827": [718], "9829": [720], "9830": [719], "9837": [727], "9839": [726], "10003": [724], "10007": [725], "10013": [699], "10016": [721], "10216": [677], "10217": [679], "12289": [309], "12290": [306], "12300": [307], "12301": [308], "12441": [1055], "12442": [1056], "12443": [367], "12444": [368], "12449": [312], "12450": [322], "12451": [313], "12452": [323], "12453": [314], "12454": [324], "12455": [315], "12456": [325], "12457": [316], "12458": [326], "12459": [327], "12461": [328], "12463": [329], "12465": [330], "12467": [331], "12469": [332], "12471": [333], "12473": [334], "12475": [335], "12477": [336], "12479": [337], "12481": [338], "12483": [320], "12484": [339], "12486": [340], "12488": [341], "12490": [342], "12491": [343], "12492": [344], "12493": [345], "12494": [346], "12495": [347], "12498": [348], "12501": [349], "12504": [350], "12507": [351], "12510": [352], "12511": [353], "12512": [354], "12513": [355], "12514": [356], "12515": [317], "12516": [357], "12517": [318], "12518": [358], "12519": [319], "12520": [359], "12521": [360], "12522": [361], "12523": [362], "12524": [363], "12525": [364], "12527": [365], "12530": [311], "12531": [366], "12539": [310], "12540": [321], "12593": [868], "12594": [869], "12595": [870], "12596": [871], "12597": [872], "12598": [873], "12599": [874], "12600": [875], "12601": [876], "12602": [877], "12603": [878], "12604": [879], "12605": [880], "12606": [881], "12607": [882], "12608": [883], "12609": [884], "12610": [885], "12611": [886], "12612": [887], "12613": [888], "12614": [889], "12615": [890], "12616": [891], "12617": [892], "12618": [893], "12619": [894], "12620": [895], "12621": [896], "12622": [897], "12623": [898], "12624": [899], "12625": [900], "12626": [901], "12627": [902], "12628": [903], "12629": [904], "12630": [905], "12631": [906], "12632": [907], "12633": [908], "12634": [909], "12635": [910], "12636": [911], "12637": [912], "12638": [913], "12639": [914], "12640": [915], "12641": [916], "12642": [917], "12643": [918], "12653": [946], "12657": [947], "12664": [948], "12671": [949], "12673": [950], "12676": [951], "12678": [952], "12685": [953], "12686": [954]}, "names": {"0": 16, "1": 17, "2": 18, "3": 19, "4": 20, "5": 21, "6": 22, "7": 23, "8": 24, "9": 25, "space": 0, "exclam": 1, "quotedbl": 2, "numbersign": 3, "dollar": 4, "percent": 5, "ampersand": 6, "apostrophe": 7, "quoteright": 7, "parenleft": 8, "parenright": 9, "asterisk": 10, "plus": 11, "comma": 12, "minus": 13, "period": 14, "slash": 15, "colon": 26, "semicolon": 27, "less": 28, "equal": 29, "greater": 30, "question": 31, "at": 32, "A": 33, "B": 34, "C": 35, "D": 36, "E": 37, "F": 38, "G": 39, "H": 40, "I": 41, "J": 42, "K": 43, "L": 44, "M": 45, "N": 46, "O": 47, "P": 48, "Q": 49, "R": 50, "S": 51, "T": 52, "U": 53, "V": 54, "W": 55, "X": 56, "Y": 57, "Z": 58, "bracketleft": 59, "backslash": 60, "bracketright": 61, "asciicircum": 62, "underscore": 63, "grave": 64, "quoteleft": 64, "a": 65, "b": 66, "c": 67, "d": 68, "e": 69, "f": 70, "g": 71, "h": 72, "i": 73, "j": 74, "k": 75, "l": 76, "m": 77, "n": 78, "o": 79, "p": 80, "q": 81, "r": 82, "s": 83, "t": 84, "u": 85, "v": 86, "w": 87, "x": 88, "y": 89, "z": 90, "braceleft": 91, "bar": 92, "braceright": 93, "asciitilde": 94, "nobreakspace": 95, "exclamdown": 96, "cent": 97, "sterling": 98, "currency": 99, "yen": 100, "brokenbar": 101, "section": 102, "diaeresis": 103, "copyright": 104, "ordfeminine": 105, "guillemotleft": 106, "notsign": 107, "hyphen": 108, "registered": 109, "macron": 110, "degree": 111, "plusminus": 112, "twosuperior": 113, "threesuperior": 114, "acute": 115, "mu": 116, "paragraph": 117, "periodcentered": 118, "cedilla": 119, "onesuperior": 120, "masculine": 121, "guillemotright": 122, "onequarter": 123, "onehalf": 124, "threequarters": 125, "questiondown": 126, "Agrave": 127, "Aacute": 128, "Acircumflex": 129, "Atilde": 130, "Adiaeresis": 131, "Aring": 132, "AE": 133, "Ccedilla": 134, "Egrave": 135, "Eacute": 136, "Ecircumflex": 137, "Ediaeresis": 138, "Igrave": 139, "Iacute": 140, "Icircumflex": 141, "Idiaeresis": 142, "ETH": 143, "Eth": 143, "Ntilde": 144, "Ograve": 145, "Oacute": 146, "Ocircumflex": 147, "Otilde": 148, "Odiaeresis": 149, "multiply": 150, "Ooblique": 151, "Ugrave": 152, "Uacute": 153, "Ucircumflex": 154, "Udiaeresis": 155, "Yacute": 156, "THORN": 157, "Thorn": 157, "ssharp": 158, "agrave": 159, "aacute": 160, "acircumflex": 161, "atilde": 162, "adiaeresis": 163, "aring": 164, "ae": 165, "ccedilla": 166, "egrave": 167, "eacute": 168, "ecircumflex": 169, "ediaeresis": 170, "igrave": 171, "iacute": 172, "icircumflex": 173, "idiaeresis": 174, "eth": 175, "ntilde": 176, "ograve": 177, "oacute": 178, "ocircumflex": 179, "otilde": 180, "odiaeresis": 181, "division": 182, "oslash": 183, "ugrave": 184, "uacute": 185, "ucircumflex": 186, "udiaeresis": 187, "yacute": 188, "thorn": 189, "ydiaeresis": 190, "Aogonek": 191, "breve": 192, "Lstroke": 193, "Lcaron": 194, "Sacute": 195, "Scaron": 196, "Scedilla": 197, "Tcaron": 198, "Zacute": 199, "Zcaron": 200, "Zabovedot": 201, "aogonek": 202, "ogonek": 203, "lstroke": 204, "lcaron": 205, "sacute": 206, "caron": 207, "scaron": 208, "scedilla": 209, "tcaron": 210, "zacute": 211, "doubleacute": 212, "zcaron": 213, "zabovedot": 214, "Racute": 215, "Abreve": 216, "Lacute": 217, "Cacute": 218, "Ccaron": 219, "Eogonek": 220, "Ecaron": 221, "Dcaron": 222, "Dstroke": 223, "Nacute": 224, "Ncaron": 225, "Odoubleacute": 226, "Rcaron": 227, "Uring": 228, "Udoubleacute": 229, "Tcedilla": 230, "racute": 231, "abreve": 232, "lacute": 233, "cacute": 234, "ccaron": 235, "eogonek": 236, "ecaron": 237, "dcaron": 238, "dstroke": 239, "nacute": 240, "ncaron": 241, "odoubleacute": 242, "rcaron": 243, "uring": 244, "udoubleacute": 245, "tcedilla": 246, "abovedot": 247, "Hstroke": 248, "Hcircumflex": 249, "Iabovedot": 250, "Gbreve": 251, "Jcircumflex": 252, "hstroke": 253, "hcircumflex": 254, "idotless": 255, "gbreve": 256, "jcircumflex": 257, "Cabovedot": 258, "Ccircumflex": 259, "Gabovedot": 260, "Gcircumflex": 261, "Ubreve": 262, "Scircumflex": 263, "cabovedot": 264, "ccircumflex": 265, "gabovedot": 266, "gcircumflex": 267, "ubreve": 268, "scircumflex": 269, "kra": 270, "Rcedilla": 271, "Itilde": 272, "Lcedilla": 273, "Emacron": 274, "Gcedilla": 275, "Tslash": 276, "rcedilla": 277, "itilde": 278, "lcedilla": 279, "emacron": 280, "gcedilla": 281, "tslash": 282, "ENG": 283, "eng": 284, "Amacron": 285, "Iogonek": 286, "Eabovedot": 287, "Imacron": 288, "Ncedilla": 289, "Omacron": 290, "Kcedilla": 291, "Uogonek": 292, "Utilde": 293, "Umacron": 294, "amacron": 295, "iogonek": 296, "eabovedot": 297, "imacron": 298, "ncedilla": 299, "omacron": 300, "kcedilla": 301, "uogonek": 302, "utilde": 303, "umacron": 304, "overline": 305, "kana_fullstop": 306, "kana_openingbracket": 307, "kana_closingbracket": 308, "kana_comma": 309, "kana_conjunctive": 310, "kana_WO": 311, "kana_a": 312, "kana_i": 313, "kana_u": 314, "kana_e": 315, "kana_o": 316, "kana_ya": 317, "kana_yu": 318, "kana_yo": 319, "kana_tsu": 320, "prolongedsound": 321, "kana_A": 322, "kana_I": 323, "kana_U": 324, "kana_E": 325, "kana_O": 326, "kana_KA": 327, "kana_KI": 328, "kana_KU": 329, "kana_KE": 330, "kana_KO": 331, "kana_SA": 332, "kana_SHI": 333, "kana_SU": 334, "kana_SE": 335, "kana_SO": 336, "kana_TA": 337, "kana_CHI": 338, "kana_TSU": 339, "kana_TE": 340, "kana_TO": 341, "kana_NA": 342, "kana_NI": 343, "kana_NU": 344, "kana_NE": 345, "kana_NO": 346, "kana_HA": 347, "kana_HI": 348, "kana_FU": 349, "kana_HE": 350, "kana_HO": 351, "kana_MA": 352, "kana_MI": 353, "kana_MU": 354, "kana_ME": 355, "kana_MO": 356, "kana_YA": 357, "kana_YU": 358, "kana_YO": 359, "kana_RA": 360, "kana_RI": 361, "kana_RU": 362, "kana_RE": 363, "kana_RO": 364, "kana_WA": 365, "kana_N": 366, "voicedsound": 367, "semivoicedsound": 368, "Arabic_comma": 369, "Arabic_semicolon": 370, "Arabic_question_mark": 371, "Arabic_hamza": 372, "Arabic_maddaonalef": 373, "Arabic_hamzaonalef": 374, "Arabic_hamzaonwaw": 375, "Arabic_hamzaunderalef": 376, "Arabic_hamzaonyeh": 377, "Arabic_alef": 378, "Arabic_beh": 379, "Arabic_tehmarbuta": 380, "Arabic_teh": 381, "Arabic_theh": 382, "Arabic_jeem": 383, "Arabic_hah": 384, "Arabic_khah": 385, "Arabic_dal": 386, "Arabic_thal": 387, "Arabic_ra": 388, "Arabic_zain": 389, "Arabic_seen": 390, "Arabic_sheen": 391, "Arabic_sad": 392, "Arabic_dad": 393, "Arabic_tah": 394, "Arabic_zah": 395, "Arabic_ain": 396, "Arabic_ghain": 397, "Arabic_tatweel": 398, "Arabic_feh": 399, "Arabic_qaf": 400, "Arabic_kaf": 401, "Arabic_lam": 402, "Arabic_meem": 403, "Arabic_noon": 404, "Arabic_ha": 405, "Arabic_waw": 406, "Arabic_alefmaksura": 407, "Arabic_yeh": 408, "Arabic_fathatan": 409, "Arabic_dammatan": 410, "Arabic_kasratan": 411, "Arabic_fatha": 412, "Arabic_damma": 413, "Arabic_kasra": 414, "Arabic_shadda": 415, "Arabic_sukun": 416, "Serbian_dje": 417, "Macedonia_gje": 418, "Cyrillic_io": 419, "Ukrainian_ie": 420, "Macedonia_dse": 421, "Ukrainian_i": 422, "Ukrainian_yi": 423, "Cyrillic_je": 424, "Cyrillic_lje": 425, "Cyrillic_nje": 426, "Serbian_tshe": 427, "Macedonia_kje": 428, "Byelorussian_shortu": 429, "Cyrillic_dzhe": 430, "numerosign": 431, "Serbian_DJE": 432, "Macedonia_GJE": 433, "Cyrillic_IO": 434, "Ukrainian_IE": 435, "Macedonia_DSE": 436, "Ukrainian_I": 437, "Ukrainian_YI": 438, "Cyrillic_JE": 439, "Cyrillic_LJE": 440, "Cyrillic_NJE": 441, "Serbian_TSHE": 442, "Macedonia_KJE": 443, "Byelorussian_SHORTU": 444, "Cyrillic_DZHE": 445, "Cyrillic_yu": 446, "Cyrillic_a": 447, "Cyrillic_be": 448, "Cyrillic_tse": 449, "Cyrillic_de": 450, "Cyrillic_ie": 451, "Cyrillic_ef": 452, "Cyrillic_ghe": 453, "Cyrillic_ha": 454, "Cyrillic_i": 455, "Cyrillic_shorti": 456, "Cyrillic_ka": 457, "Cyrillic_el": 458, "Cyrillic_em": 459, "Cyrillic_en": 460, "Cyrillic_o": 461, "Cyrillic_pe": 462, "Cyrillic_ya": 463, "Cyrillic_er": 464, "Cyrillic_es": 465, "Cyrillic_te": 466, "Cyrillic_u": 467, "Cyrillic_zhe": 468, "Cyrillic_ve": 469, "Cyrillic_softsign": 470, "Cyrillic_yeru": 471, "Cyrillic_ze": 472, "Cyrillic_sha": 473, "Cyrillic_e": 474, "Cyrillic_shcha": 475, "Cyrillic_che": 476, "Cyrillic_hardsign": 477, "Cyrillic_YU": 478, "Cyrillic_A": 479, "Cyrillic_BE": 480, "Cyrillic_TSE": 481, "Cyrillic_DE": 482, "Cyrillic_IE": 483, "Cyrillic_EF": 484, "Cyrillic_GHE": 485, "Cyrillic_HA": 486, "Cyrillic_I": 487, "Cyrillic_SHORTI": 488, "Cyrillic_KA": 489, "Cyrillic_EL": 490, "Cyrillic_EM": 491, "Cyrillic_EN": 492, "Cyrillic_O": 493, "Cyrillic_PE": 494, "Cyrillic_YA": 495, "Cyrillic_ER": 496, "Cyrillic_ES": 497, "Cyrillic_TE": 498, "Cyrillic_U": 499, "Cyrillic_ZHE": 500, "Cyrillic_VE": 501, "Cyrillic_SOFTSIGN": 502, "Cyrillic_YERU": 503, "Cyrillic_ZE": 504, "Cyrillic_SHA": 505, "Cyrillic_E": 506, "Cyrillic_SHCHA": 507, "Cyrillic_CHE": 508, "Cyrillic_HARDSIGN": 509, "Greek_ALPHAaccent": 510, "Greek_EPSILONaccent": 511, "Greek_ETAaccent": 512, "Greek_IOTAaccent": 513, "Greek_IOTAdiaeresis": 514, "Greek_OMICRONaccent": 515, "Greek_UPSILONaccent": 516, "Greek_UPSILONdieresis": 517, "Greek_OMEGAaccent": 518, "Greek_accentdieresis": 519, "Greek_horizbar": 520, "Greek_alphaaccent": 521, "Greek_epsilonaccent": 522, "Greek_etaaccent": 523, "Greek_iotaaccent": 524, "Greek_iotadieresis": 525, "Greek_iotaaccentdieresis": 526, "Greek_omicronaccent": 527, "Greek_upsilonaccent": 528, "Greek_upsilondieresis": 529, "Greek_upsilonaccentdieresis": 530, "Greek_omegaaccent": 531, "Greek_ALPHA": 532, "Greek_BETA": 533, "Greek_GAMMA": 534, "Greek_DELTA": 535, "Greek_EPSILON": 536, "Greek_ZETA": 537, "Greek_ETA": 538, "Greek_THETA": 539, "Greek_IOTA": 540, "Greek_KAPPA": 541, "Greek_LAMBDA": 542, "Greek_LAMDA": 542, "Greek_MU": 543, "Greek_NU": 544, "Greek_XI": 545, "Greek_OMICRON": 546, "Greek_PI": 547, "Greek_RHO": 548, "Greek_SIGMA": 549, "Greek_TAU": 550, "Greek_UPSILON": 551, "Greek_PHI": 552, "Greek_CHI": 553, "Greek_PSI": 554, "Greek_OMEGA": 555, "Greek_alpha": 556, "Greek_beta": 557, "Greek_gamma": 558, "Greek_delta": 559, "Greek_epsilon": 560, "Greek_zeta": 561, "Greek_eta": 562, "Greek_theta": 563, "Greek_iota": 564, "Greek_kappa": 565, "Greek_lambda": 566, "Greek_mu": 567, "Greek_nu": 568, "Greek_xi": 569, "Greek_omicron": 570, "Greek_pi": 571, "Greek_rho": 572, "Greek_sigma": 573, "Greek_finalsmallsigma": 574, "Greek_tau": 575, "Greek_upsilon": 576, "Greek_phi": 577, "Greek_chi": 578, "Greek_psi": 579, "Greek_omega": 580, "leftradical": 581, "topleftradical": 582, "horizconnector": 583, "topintegral": 584, "botintegral": 585, "vertconnector": 586, "topleftsqbracket": 587, "botleftsqbracket": 588, "toprightsqbracket": 589, "botrightsqbracket": 590, "topleftparens": 591, "botleftparens": 592, "toprightparens": 593, "botrightparens": 594, "leftmiddlecurlybrace": 595, "rightmiddlecurlybrace": 596, "topleftsummation": 597, "botleftsummation": 598, "topvertsummationconnector": 599, "botvertsummationconnector": 600, "toprightsummation": 601, "botrightsummation": 602, "rightmiddlesummation": 603, "lessthanequal": 604, "notequal": 605, "greaterthanequal": 606, "integral": 607, "therefore": 608, "variation": 609, "infinity": 610, "nabla": 611, "approximate": 612, "similarequal": 613, "ifonlyif": 614, "implies": 615, "identical": 616, "radical": 617, "includedin": 618, "includes": 619, "intersection": 620, "union": 621, "logicaland": 622, "logicalor": 623, "partialderivative": 624, "function": 625, "leftarrow": 626, "uparrow": 627, "rightarrow": 628, "downarrow": 629, "blank": 630, "soliddiamond": 631, "checkerboard": 632, "ht": 633, "ff": 634, "cr": 635, "lf": 636, "nl": 637, "vt": 638, "lowrightcorner": 639, "uprightcorner": 640, "upleftcorner": 641, "lowleftcorner": 642, "crossinglines": 643, "horizlinescan1": 644, "horizlinescan3": 645, "horizlinescan5": 646, "horizlinescan7": 647, "horizlinescan9": 648, "leftt": 649, "rightt": 650, "bott": 651, "topt": 652, "vertbar": 653, "emspace": 654, "enspace": 655, "em3space": 656, "em4space": 657, "digitspace": 658, "punctspace": 659, "thinspace": 660, "hairspace": 661, "emdash": 662, "endash": 663, "signifblank": 664, "ellipsis": 665, "doubbaselinedot": 666, "onethird": 667, "twothirds": 668, "onefifth": 669, "twofifths": 670, "threefifths": 671, "fourfifths": 672, "onesixth": 673, "fivesixths": 674, "careof": 675, "figdash": 676, "leftanglebracket": 677, "decimalpoint": 678, "rightanglebracket": 679, "marker": 680, "oneeighth": 681, "threeeighths": 682, "fiveeighths": 683, "seveneighths": 684, "trademark": 685, "signaturemark": 686, "trademarkincircle": 687, "leftopentriangle": 688, "rightopentriangle": 689, "emopencircle": 690, "emopenrectangle": 691, "leftsinglequotemark": 692, "rightsinglequotemark": 693, "leftdoublequotemark": 694, "rightdoublequotemark": 695, "prescription": 696, "minutes": 697, "seconds": 698, "latincross": 699, "hexagram": 700, "filledrectbullet": 701, "filledlefttribullet": 702, "filledrighttribullet": 703, "emfilledcircle": 704, "emfilledrect": 705, "enopencircbullet": 706, "enopensquarebullet": 707, "openrectbullet": 708, "opentribulletup": 709, "opentribulletdown": 710, "openstar": 711, "enfilledcircbullet": 712, "enfilledsqbullet": 713, "filledtribulletup": 714, "filledtribulletdown": 715, "leftpointer": 716, "rightpointer": 717, "club": 718, "diamond": 719, "heart": 720, "maltesecross": 721, "dagger": 722, "doubledagger": 723, "checkmark": 724, "ballotcross": 725, "musicalsharp": 726, "musicalflat": 727, "malesymbol": 728, "femalesymbol": 729, "telephone": 730, "telephonerecorder": 731, "phonographcopyright": 732, "caret": 733, "singlelowquotemark": 734, "doublelowquotemark": 735, "cursor": 736, "leftcaret": 737, "rightcaret": 738, "downcaret": 739, "upcaret": 740, "overbar": 741, "downtack": 742, "upshoe": 743, "downstile": 744, "underbar": 745, "jot": 746, "quad": 747, "uptack": 748, "circle": 749, "upstile": 750, "downshoe": 751, "rightshoe": 752, "leftshoe": 753, "lefttack": 754, "righttack": 755, "hebrew_doublelowline": 756, "hebrew_aleph": 757, "hebrew_bet": 758, "hebrew_beth": 758, "hebrew_gimel": 759, "hebrew_gimmel": 759, "hebrew_dalet": 760, "hebrew_daleth": 760, "hebrew_he": 761, "hebrew_waw": 762, "hebrew_zain": 763, "hebrew_zayin": 763, "hebrew_chet": 764, "hebrew_het": 764, "hebrew_tet": 765, "hebrew_teth": 765, "hebrew_yod": 766, "hebrew_finalkaph": 767, "hebrew_kaph": 768, "hebrew_lamed": 769, "hebrew_finalmem": 770, "hebrew_mem": 771, "hebrew_finalnun": 772, "hebrew_nun": 773, "hebrew_samech": 774, "hebrew_samekh": 774, "hebrew_ayin": 775, "hebrew_finalpe": 776, "hebrew_pe": 777, "hebrew_finalzade": 778, "hebrew_finalzadi": 778, "hebrew_zade": 779, "hebrew_zadi": 779, "hebrew_kuf": 780, "hebrew_qoph": 780, "hebrew_resh": 781, "hebrew_shin": 782, "hebrew_taf": 783, "hebrew_taw": 783, "Thai_kokai": 784, "Thai_khokhai": 785, "Thai_khokhuat": 786, "Thai_khokhwai": 787, "Thai_khokhon": 788, "Thai_khorakhang": 789, "Thai_ngongu": 790, "Thai_chochan": 791, "Thai_choching": 792, "Thai_chochang": 793, "Thai_soso": 794, "Thai_chochoe": 795, "Thai_yoying": 796, "Thai_dochada": 797, "Thai_topatak": 798, "Thai_thothan": 799, "Thai_thonangmontho": 800, "Thai_thophuthao": 801, "Thai_nonen": 802, "Thai_dodek": 803, "Thai_totao": 804, "Thai_thothung": 805, "Thai_thothahan": 806, "Thai_thothong": 807, "Thai_nonu": 808, "Thai_bobaimai": 809, "Thai_popla": 810, "Thai_phophung": 811, "Thai_fofa": 812, "Thai_phophan": 813, "Thai_fofan": 814, "Thai_phosamphao": 815, "Thai_moma": 816, "Thai_yoyak": 817, "Thai_rorua": 818, "Thai_ru": 819, "Thai_loling": 820, "Thai_lu": 821, "Thai_wowaen": 822, "Thai_sosala": 823, "Thai_sorusi": 824, "Thai_sosua": 825, "Thai_hohip": 826, "Thai_lochula": 827, "Thai_oang": 828, "Thai_honokhuk": 829, "Thai_paiyannoi": 830, "Thai_saraa": 831, "Thai_maihanakat": 832, "Thai_saraaa": 833, "Thai_saraam": 834, "Thai_sarai": 835, "Thai_saraii": 836, "Thai_saraue": 837, "Thai_sarauee": 838, "Thai_sarau": 839, "Thai_sarauu": 840, "Thai_phinthu": 841, "Thai_maihanakat_maitho": 842, "Thai_baht": 843, "Thai_sarae": 844, "Thai_saraae": 845, "Thai_sarao": 846, "Thai_saraaimaimuan": 847, "Thai_saraaimaimalai": 848, "Thai_lakkhangyao": 849, "Thai_maiyamok": 850, "Thai_maitaikhu": 851, "Thai_maiek": 852, "Thai_maitho": 853, "Thai_maitri": 854, "Thai_maichattawa": 855, "Thai_thanthakhat": 856, "Thai_nikhahit": 857, "Thai_leksun": 858, "Thai_leknung": 859, "Thai_leksong": 860, "Thai_leksam": 861, "Thai_leksi": 862, "Thai_lekha": 863, "Thai_lekhok": 864, "Thai_lekchet": 865, "Thai_lekpaet": 866, "Thai_lekkao": 867, "Hangul_Kiyeog": 868, "Hangul_SsangKiyeog": 869, "Hangul_KiyeogSios": 870, "Hangul_Nieun": 871, "Hangul_NieunJieuj": 872, "Hangul_NieunHieuh": 873, "Hangul_Dikeud": 874, "Hangul_SsangDikeud": 875, "Hangul_Rieul": 876, "Hangul_RieulKiyeog": 877, "Hangul_RieulMieum": 878, "Hangul_RieulPieub": 879, "Hangul_RieulSios": 880, "Hangul_RieulTieut": 881, "Hangul_RieulPhieuf": 882, "Hangul_RieulHieuh": 883, "Hangul_Mieum": 884, "Hangul_Pieub": 885, "Hangul_SsangPieub": 886, "Hangul_PieubSios": 887, "Hangul_Sios": 888, "Hangul_SsangSios": 889, "Hangul_Ieung": 890, "Hangul_Jieuj": 891, "Hangul_SsangJieuj": 892, "Hangul_Cieuc": 893, "Hangul_Khieuq": 894, "Hangul_Tieut": 895, "Hangul_Phieuf": 896, "Hangul_Hieuh": 897, "Hangul_A": 898, "Hangul_AE": 899, "Hangul_YA": 900, "Hangul_YAE": 901, "Hangul_EO": 902, "Hangul_E": 903, "Hangul_YEO": 904, "Hangul_YE": 905, "Hangul_O": 906, "Hangul_WA": 907, "Hangul_WAE": 908, "Hangul_OE": 909, "Hangul_YO": 910, "Hangul_U": 911, "Hangul_WEO": 912, "Hangul_WE": 913, "Hangul_WI": 914, "Hangul_YU": 915, "Hangul_EU": 916, "Hangul_YI": 917, "Hangul_I": 918, "Hangul_J_Kiyeog": 919, "Hangul_J_SsangKiyeog": 920, "Hangul_J_KiyeogSios": 921, "Hangul_J_Nieun": 922, "Hangul_J_NieunJieuj": 923, "Hangul_J_NieunHieuh": 924, "Hangul_J_Dikeud": 925, "Hangul_J_Rieul": 926, "Hangul_J_RieulKiyeog": 927, "Hangul_J_RieulMieum": 928, "Hangul_J_RieulPieub": 929, "Hangul_J_RieulSios": 930, "Hangul_J_RieulTieut": 931, "Hangul_J_RieulPhieuf": 932, "Hangul_J_RieulHieuh": 933, "Hangul_J_Mieum": 934, "Hangul_J_Pieub": 935, "Hangul_J_PieubSios": 936, "Hangul_J_Sios": 937, "Hangul_J_SsangSios": 938, "Hangul_J_Ieung": 939, "Hangul_J_Jieuj": 940, "Hangul_J_Cieuc": 941, "Hangul_J_Khieuq": 942, "Hangul_J_Tieut": 943, "Hangul_J_Phieuf": 944, "Hangul_J_Hieuh": 945, "Hangul_RieulYeorinHieuh": 946, "Hangul_SunkyeongeumMieum": 947, "Hangul_SunkyeongeumPieub": 948, "Hangul_PanSios": 949, "Hangul_KkogjiDalrinIeung": 950, "Hangul_SunkyeongeumPhieuf": 951, "Hangul_YeorinHieuh": 952, "Hangul_AraeA": 953, "Hangul_AraeAE": 954, "Hangul_J_PanSios": 955, "Hangul_J_KkogjiDalrinIeung": 956, "Hangul_J_YeorinHieuh": 957, "Korean_Won": 958, "OE": 959, "oe": 960, "Ydiaeresis": 961, "EcuSign": 962, "ColonSign": 963, "CruzeiroSign": 964, "FFrancSign": 965, "LiraSign": 966, "MillSign": 967, "NairaSign": 968, "PesetaSign": 969, "RupeeSign": 970, "WonSign": 971, "NewSheqelSign": 972, "DongSign": 973, "EuroSign": 974, "3270_Duplicate": 975, "3270_FieldMark": 976, "3270_Right2": 977, "3270_Left2": 978, "3270_BackTab": 979, "3270_EraseEOF": 980, "3270_EraseInput": 981, "3270_Reset": 982, "3270_Quit": 983, "3270_PA1": 984, "3270_PA2": 985, "3270_PA3": 986, "3270_Test": 987, "3270_Attn": 988, "3270_CursorBlink": 989, "3270_AltCursor": 990, "3270_KeyClick": 991, "3270_Jump": 992, "3270_Ident": 993, "3270_Rule": 994, "3270_Copy": 995, "3270_Play": 996, "3270_Setup": 997, "3270_Record": 998, "3270_ChangeScreen": 999, "3270_DeleteWord": 1000, "3270_ExSelect": 1001, "3270_CursorSelect": 1002, "3270_PrintScreen": 1003, "3270_Enter": 1004, "ISO_Lock": 1005, "ISO_Level2_Latch": 1006, "ISO_Level3_Shift": 1007, "ISO_Level3_Latch": 1008, "ISO_Level3_Lock": 1009, "ISO_Group_Latch": 1010, "ISO_Group_Lock": 1011, "ISO_Next_Group": 1012, "ISO_Next_Group_Lock": 1013, "ISO_Prev_Group": 1014, "ISO_Prev_Group_Lock": 1015, "ISO_First_Group": 1016, "ISO_First_Group_Lock": 1017, "ISO_Last_Group": 1018, "ISO_Last_Group_Lock": 1019, "ISO_Left_Tab": 1020, "ISO_Move_Line_Up": 1021, "ISO_Move_Line_Down": 1022, "ISO_Partial_Line_Up": 1023, "ISO_Partial_Line_Down": 1024, "ISO_Partial_Space_Left": 1025, "ISO_Partial_Space_Right": 1026, "ISO_Set_Margin_Left": 1027, "ISO_Set_Margin_Right": 1028, "ISO_Release_Margin_Left": 1029, "ISO_Release_Margin_Right": 1030, "ISO_Release_Both_Margins": 1031, "ISO_Fast_Cursor_Left": 1032, "ISO_Fast_Cursor_Right": 1033, "ISO_Fast_Cursor_Up": 1034, "ISO_Fast_Cursor_Down": 1035, "ISO_Continuous_Underline": 1036, "ISO_Discontinuous_Underline": 1037, "ISO_Emphasize": 1038, "ISO_Center_Object": 1039, "ISO_Enter": 1040, "dead_grave": 1041, "dead_acute": 1042, "dead_circumflex": 1043, "dead_tilde": 1044, "dead_macron": 1045, "dead_breve": 1046, "dead_abovedot": 1047, "dead_diaeresis": 1048, "dead_abovering": 1049, "dead_doubleacute": 1050, "dead_caron": 1051, "dead_cedilla": 1052, "dead_ogonek": 1053, "dead_iota": 1054, "dead_voiced_sound": 1055, "dead_semivoiced_sound": 1056, "AccessX_Enable": 1057, "AccessX_Feedback_Enable": 1058, "RepeatKeys_Enable": 1059, "SlowKeys_Enable": 1060, "BounceKeys_Enable": 1061, "StickyKeys_Enable": 1062, "MouseKeys_Enable": 1063, "MouseKeys_Accel_Enable": 1064, "Overlay1_Enable": 1065, "Overlay2_Enable": 1066, "AudibleBell_Enable": 1067, "First_Virtual_Screen": 1068, "Prev_Virtual_Screen": 1069, "Next_Virtual_Screen": 1070, "Last_Virtual_Screen": 1071, "Terminate_Server": 1072, "Pointer_Left": 1073, "Pointer_Right": 1074, "Pointer_Up": 1075, "Pointer_Down": 1076, "Pointer_UpLeft": 1077, "Pointer_UpRight": 1078, "Pointer_DownLeft": 1079, "Pointer_DownRight": 1080, "Pointer_Button_Dflt": 1081, "Pointer_Button1": 1082, "Pointer_Button2": 1083, "Pointer_Button3": 1084, "Pointer_Button4": 1085, "Pointer_Button5": 1086, "Pointer_DblClick_Dflt": 1087, "Pointer_DblClick1": 1088, "Pointer_DblClick2": 1089, "Pointer_DblClick3": 1090, "Pointer_DblClick4": 1091, "Pointer_DblClick5": 1092, "Pointer_Drag_Dflt": 1093, "Pointer_Drag1": 1094, "Pointer_Drag2": 1095, "Pointer_Drag3": 1096, "Pointer_Drag4": 1097, "Pointer_EnableKeys": 1098, "Pointer_Accelerate": 1099, "Pointer_DfltBtnNext": 1100, "Pointer_DfltBtnPrev": 1101, "Pointer_Drag5": 1102, "BackSpace": 1103, "Tab": 1104, "Linefeed": 1105, "Clear": 1106, "Return": 1107, "Pause": 1108, "Scroll_Lock": 1109, "Sys_Req": 1110, "Escape": 1111, "Multi_key": 1112, "Kanji": 1113, "Muhenkan": 1114, "Henkan_Mode": 1115, "Romaji": 1116, "Hiragana": 1117, "Katakana": 1118, "Hiragana_Katakana": 1119, "Zenkaku": 1120, "Hankaku": 1121, "Zenkaku_Hankaku": 1122, "Touroku": 1123, "Massyo": 1124, "Kana_Lock": 1125, "Kana_Shift": 1126, "Eisu_Shift": 1127, "Eisu_toggle": 1128, "Hangul": 1129, "Hangul_Start": 1130, "Hangul_End": 1131, "Hangul_Hanja": 1132, "Hangul_Jamo": 1133, "Hangul_Romaja": 1134, "Codeinput": 1135, "Hangul_Jeonja": 1136, "Hangul_Banja": 1137, "Hangul_PreHanja": 1138, "Hangul_PostHanja": 1139, "SingleCandidate": 1140, "MultipleCandidate": 1141, "PreviousCandidate": 1142, "Hangul_Special": 1143, "Home": 1144, "Left": 1145, "Up": 1146, "Right": 1147, "Down": 1148, "Prior": 1149, "Next": 1150, "End": 1151, "Begin": 1152, "Select": 1153, "Print": 1154, "Execute": 1155, "Insert": 1156, "Undo": 1157, "Redo": 1158, "Menu": 1159, "Find": 1160, "Cancel": 1161, "Help": 1162, "Break": 1163, "Mode_switch": 1164, "Num_Lock": 1165, "KP_Space": 1166, "KP_Tab": 1167, "KP_Enter": 1168, "KP_F1": 1169, "KP_F2": 1170, "KP_F3": 1171, "KP_F4": 1172, "KP_Home": 1173, "KP_Left": 1174, "KP_Up": 1175, "KP_Right": 1176, "KP_Down": 1177, "KP_Prior": 1178, "KP_Next": 1179, "KP_End": 1180, "KP_Begin": 1181, "KP_Insert": 1182, "KP_Delete": 1183, "KP_Multiply": 1184, "KP_Add": 1185, "KP_Separator": 1186, "KP_Subtract": 1187, "KP_Decimal": 1188, "KP_Divide": 1189, "KP_0": 1190, "KP_1": 1191, "KP_2": 1192, "KP_3": 1193, "KP_4": 1194, "KP_5": 1195, "KP_6": 1196, "KP_7": 1197, "KP_8": 1198, "KP_9": 1199, "KP_Equal": 1200, "F1": 1201, "F2": 1202, "F3": 1203, "F4": 1204, "F5": 1205, "F6": 1206, "F7": 1207, "F8": 1208, "F9": 1209, "F10": 1210, "F11": 1211, "F12": 1212, "F13": 1213, "F14": 1214, "F15": 1215, "F16": 1216, "F17": 1217, "F18": 1218, "F19": 1219, "F20": 1220, "F21": 1221, "F22": 1222, "F23": 1223, "F24": 1224, "F25": 1225, "F26": 1226, "F27": 1227, "F28": 1228, "F29": 1229, "F30": 1230, "F31": 1231, "F32": 1232, "F33": 1233, "F34": 1234, "F35": 1235, "Shift_L": 1236, "Shift_R": 1237, "Control_L": 1238, "Control_R": 1239, "Caps_Lock": 1240, "Shift_Lock": 1241, "Meta_L": 1242, "Meta_R": 1243, "Alt_L": 1244, "Alt_R": 1245, "Super_L": 1246, "Super_R": 1247, "Hyper_L": 1248, "Hyper_R": 1249, "Delete": 1250, "VoidSymbol": 1251, "Ukrainian_ghe_with_upturn": 1252, "Ukrainian_GHE_WITH_UPTURN": 1253, "Armenian_eternity": 1254, "Armenian_ligature_ew": 1255, "Armenian_verjaket": 1256, "Armenian_parenright": 1257, "Armenian_parenleft": 1258, "Armenian_guillemotright": 1259, "Armenian_guillemotleft": 1260, "Armenian_em_dash": 1261, "Armenian_mijaket": 1262, "Armenian_but": 1263, "Armenian_comma": 1264, "Armenian_en_dash": 1265, "Armenian_yentamna": 1266, "Armenian_ellipsis": 1267, "Armenian_amanak": 1268, "Armenian_shesht": 1269, "Armenian_paruyk": 1270, "Armenian_AYB": 1271, "Armenian_ayb": 1272, "Armenian_BEN": 1273, "Armenian_ben": 1274, "Armenian_GIM": 1275, "Armenian_gim": 1276, "Armenian_DA": 1277, "Armenian_da": 1278, "Armenian_YECH": 1279, "Armenian_yech": 1280, "Armenian_ZA": 1281, "Armenian_za": 1282, "Armenian_E": 1283, "Armenian_e": 1284, "Armenian_AT": 1285, "Armenian_at": 1286, "Armenian_TO": 1287, "Armenian_to": 1288, "Armenian_ZHE": 1289, "Armenian_zhe": 1290, "Armenian_INI": 1291, "Armenian_ini": 1292, "Armenian_LYUN": 1293, "Armenian_lyun": 1294, "Armenian_KHE": 1295, "Armenian_khe": 1296, "Armenian_TSA": 1297, "Armenian_tsa": 1298, "Armenian_KEN": 1299, "Armenian_ken": 1300, "Armenian_HO": 1301, "Armenian_ho": 1302, "Armenian_DZA": 1303, "Armenian_dza": 1304, "Armenian_GHAT": 1305, "Armenian_ghat": 1306, "Armenian_TCHE": 1307, "Armenian_tche": 1308, "Armenian_MEN": 1309, "Armenian_men": 1310, "Armenian_HI": 1311, "Armenian_hi": 1312, "Armenian_NU": 1313, "Armenian_nu": 1314, "Armenian_SHA": 1315, "Armenian_sha": 1316, "Armenian_VO": 1317, "Armenian_vo": 1318, "Armenian_CHA": 1319, "Armenian_cha": 1320, "Armenian_PE": 1321, "Armenian_pe": 1322, "Armenian_JE": 1323, "Armenian_je": 1324, "Armenian_RA": 1325, "Armenian_ra": 1326, "Armenian_SE": 1327, "Armenian_se": 1328, "Armenian_VEV": 1329, "Armenian_vev": 1330, "Armenian_TYUN": 1331, "Armenian_tyun": 1332, "Armenian_RE": 1333, "Armenian_re": 1334, "Armenian_TSO": 1335, "Armenian_tso": 1336, "Armenian_VYUN": 1337, "Armenian_vyun": 1338, "Armenian_PYUR": 1339, "Armenian_pyur": 1340, "Armenian_KE": 1341, "Armenian_ke": 1342, "Armenian_O": 1343, "Armenian_o": 1344, "Armenian_FE": 1345, "Armenian_fe": 1346, "Armenian_apostrophe": 1347, "Armenian_section_sign": 1348, "Georgian_an": 1349, "Georgian_ban": 1350, "Georgian_gan": 1351, "Georgian_don": 1352, "Georgian_en": 1353, "Georgian_vin": 1354, "Georgian_zen": 1355, "Georgian_tan": 1356, "Georgian_in": 1357, "Georgian_kan": 1358, "Georgian_las": 1359, "Georgian_man": 1360, "Georgian_nar": 1361, "Georgian_on": 1362, "Georgian_par": 1363, "Georgian_zhar": 1364, "Georgian_rae": 1365, "Georgian_san": 1366, "Georgian_tar": 1367, "Georgian_un": 1368, "Georgian_phar": 1369, "Georgian_khar": 1370, "Georgian_ghan": 1371, "Georgian_qar": 1372, "Georgian_shin": 1373, "Georgian_chin": 1374, "Georgian_can": 1375, "Georgian_jil": 1376, "Georgian_cil": 1377, "Georgian_char": 1378, "Georgian_xan": 1379, "Georgian_jhan": 1380, "Georgian_hae": 1381, "Georgian_he": 1382, "Georgian_hie": 1383, "Georgian_we": 1384, "Georgian_har": 1385, "Georgian_hoe": 1386, "Georgian_fi": 1387, "Babovedot": 1388, "babovedot": 1389, "Dabovedot": 1390, "Wgrave": 1391, "Wacute": 1392, "dabovedot": 1393, "Ygrave": 1394, "Fabovedot": 1395, "fabovedot": 1396, "Mabovedot": 1397, "mabovedot": 1398, "Pabovedot": 1399, "wgrave": 1400, "pabovedot": 1401, "wacute": 1402, "Sabovedot": 1403, "ygrave": 1404, "Wdiaeresis": 1405, "wdiaeresis": 1406, "sabovedot": 1407, "Wcircumflex": 1408, "Tabovedot": 1409, "Ycircumflex": 1410, "wcircumflex": 1411, "tabovedot": 1412, "ycircumflex": 1413, "Farsi_0": 1414, "Farsi_1": 1415, "Farsi_2": 1416, "Farsi_3": 1417, "Farsi_4": 1418, "Farsi_5": 1419, "Farsi_6": 1420, "Farsi_7": 1421, "Farsi_8": 1422, "Farsi_9": 1423, "Arabic_percent": 1424, "Arabic_superscript_alef": 1425, "Arabic_tteh": 1426, "Arabic_peh": 1427, "Arabic_tcheh": 1428, "Arabic_ddal": 1429, "Arabic_rreh": 1430, "Arabic_fullstop": 1431, "Arabic_0": 1432, "Arabic_1": 1433, "Arabic_2": 1434, "Arabic_3": 1435, "Arabic_4": 1436, "Arabic_5": 1437, "Arabic_6": 1438, "Arabic_7": 1439, "Arabic_8": 1440, "Arabic_9": 1441, "Arabic_madda_above": 1442, "Arabic_hamza_above": 1443, "Arabic_hamza_below": 1444, "Arabic_jeh": 1445, "Arabic_veh": 1446, "Arabic_keheh": 1447, "Arabic_gaf": 1448, "Arabic_noon_ghunna": 1449, "Arabic_heh_doachashmee": 1450, "Farsi_yeh": 1451, "Arabic_yeh_baree": 1452, "Arabic_heh_goal": 1453, "Cyrillic_GHE_bar": 1454, "Cyrillic_ZHE_descender": 1455, "Cyrillic_KA_descender": 1456, "Cyrillic_KA_vertstroke": 1457, "Cyrillic_EN_descender": 1458, "Cyrillic_U_straight": 1459, "Cyrillic_U_straight_bar": 1460, "Cyrillic_HA_descender": 1461, "Cyrillic_CHE_descender": 1462, "Cyrillic_CHE_vertstroke": 1463, "Cyrillic_SHHA": 1464, "Cyrillic_SCHWA": 1465, "Cyrillic_I_macron": 1466, "Cyrillic_O_bar": 1467, "Cyrillic_U_macron": 1468, "Cyrillic_ghe_bar": 1469, "Cyrillic_zhe_descender": 1470, "Cyrillic_ka_descender": 1471, "Cyrillic_ka_vertstroke": 1472, "Cyrillic_en_descender": 1473, "Cyrillic_u_straight": 1474, "Cyrillic_u_straight_bar": 1475, "Cyrillic_ha_descender": 1476, "Cyrillic_che_descender": 1477, "Cyrillic_che_vertstroke": 1478, "Cyrillic_shha": 1479, "Cyrillic_schwa": 1480, "Cyrillic_i_macron": 1481, "Cyrillic_o_bar": 1482, "Cyrillic_u_macron": 1483, "Ccedillaabovedot": 1484, "Xabovedot": 1485, "Qabovedot": 1486, "Ibreve": 1487, "IE": 1488, "UO": 1489, "Zstroke": 1490, "Gcaron": 1491, "Obarred": 1492, "ccedillaabovedot": 1493, "xabovedot": 1494, "Ocaron": 1495, "qabovedot": 1496, "ibreve": 1497, "ie": 1498, "uo": 1499, "zstroke": 1500, "gcaron": 1501, "ocaron": 1502, "obarred": 1503, "SCHWA": 1504, "schwa": 1505, "Lbelowdot": 1506, "Lstrokebelowdot": 1507, "Gtilde": 1508, "lbelowdot": 1509, "lstrokebelowdot": 1510, "gtilde": 1511, "Abelowdot": 1512, "abelowdot": 1513, "Ahook": 1514, "ahook": 1515, "Acircumflexacute": 1516, "acircumflexacute": 1517, "Acircumflexgrave": 1518, "acircumflexgrave": 1519, "Acircumflexhook": 1520, "acircumflexhook": 1521, "Acircumflextilde": 1522, "acircumflextilde": 1523, "Acircumflexbelowdot": 1524, "acircumflexbelowdot": 1525, "Abreveacute": 1526, "abreveacute": 1527, "Abrevegrave": 1528, "abrevegrave": 1529, "Abrevehook": 1530, "abrevehook": 1531, "Abrevetilde": 1532, "abrevetilde": 1533, "Abrevebelowdot": 1534, "abrevebelowdot": 1535, "Ebelowdot": 1536, "ebelowdot": 1537, "Ehook": 1538, "ehook": 1539, "Etilde": 1540, "etilde": 1541, "Ecircumflexacute": 1542, "ecircumflexacute": 1543, "Ecircumflexgrave": 1544, "ecircumflexgrave": 1545, "Ecircumflexhook": 1546, "ecircumflexhook": 1547, "Ecircumflextilde": 1548, "ecircumflextilde": 1549, "Ecircumflexbelowdot": 1550, "ecircumflexbelowdot": 1551, "Ihook": 1552, "ihook": 1553, "Ibelowdot": 1554, "ibelowdot": 1555, "Obelowdot": 1556, "obelowdot": 1557, "Ohook": 1558, "ohook": 1559, "Ocircumflexacute": 1560, "ocircumflexacute": 1561, "Ocircumflexgrave": 1562, "ocircumflexgrave": 1563, "Ocircumflexhook": 1564, "ocircumflexhook": 1565, "Ocircumflextilde": 1566, "ocircumflextilde": 1567, "Ocircumflexbelowdot": 1568, "ocircumflexbelowdot": 1569, "Ohornacute": 1570, "ohornacute": 1571, "Ohorngrave": 1572, "ohorngrave": 1573, "Ohornhook": 1574, "ohornhook": 1575, "Ohorntilde": 1576, "ohorntilde": 1577, "Ohornbelowdot": 1578, "ohornbelowdot": 1579, "Ubelowdot": 1580, "ubelowdot": 1581, "Uhook": 1582, "uhook": 1583, "Uhornacute": 1584, "uhornacute": 1585, "Uhorngrave": 1586, "uhorngrave": 1587, "Uhornhook": 1588, "uhornhook": 1589, "Uhorntilde": 1590, "uhorntilde": 1591, "Uhornbelowdot": 1592, "uhornbelowdot": 1593, "Ybelowdot": 1594, "ybelowdot": 1595, "Yhook": 1596, "yhook": 1597, "Ytilde": 1598, "ytilde": 1599, "Ohorn": 1600, "ohorn": 1601, "Uhorn": 1602, "uhorn": 1603, "combining_tilde": 1604, "combining_grave": 1605, "combining_acute": 1606, "combining_hook": 1607, "combining_belowdot": 1608, "dead_belowdot": 1609, "dead_hook": 1610, "dead_horn": 1611}}