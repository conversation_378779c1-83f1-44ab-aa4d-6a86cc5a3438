# http://www.appveyor.com/docs/appveyor-yml

# Don't actually build
build: off

# Skip tag builds
skip_tags: true

# Set build version format
version: "{build}"

# Set up build environment
environment:
  # Test against these versions of Node.js
  matrix:
    - nodejs_version: "14.2.0"
    - nodejs_version: "12"
    - nodejs_version: "10"
    - nodejs_version: "8"
    - nodejs_version: "6"

# Build on both platforms
platform:
  - x86
  - x64

# Install scripts (runs after repo cloning)
install:
  # Get the latest version of $env:nodejs_version
  - ps: Update-NodeJsInstallation (Get-NodeJsLatestBuild $env:nodejs_version) $env:platform
  # Output useful info for debugging
  - node --version
  - npm --version
  # Install modules
  - npm install

# Post-install test scripts
test_script:
  # Run module tests
  - npm test
