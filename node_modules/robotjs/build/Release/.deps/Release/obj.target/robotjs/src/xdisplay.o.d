cmd_Release/obj.target/robotjs/src/xdisplay.o := cc -o Release/obj.target/robotjs/src/xdisplay.o ../src/xdisplay.c '-DNODE_GYP_MODULE_NAME=robotjs' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_FILE_OFFSET_BITS=64' '-D_LARGEFILE_SOURCE' '-D__STDC_FORMAT_MACROS' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DBUILDING_NODE_EXTENSION' -I/home/<USER>/.cache/node-gyp/24.3.0/include/node -I/home/<USER>/.cache/node-gyp/24.3.0/src -I/home/<USER>/.cache/node-gyp/24.3.0/deps/openssl/config -I/home/<USER>/.cache/node-gyp/24.3.0/deps/openssl/openssl/include -I/home/<USER>/.cache/node-gyp/24.3.0/deps/uv/include -I/home/<USER>/.cache/node-gyp/24.3.0/deps/zlib -I/home/<USER>/.cache/node-gyp/24.3.0/deps/v8/include -I../../nan  -fPIC -pthread -Wall -Wextra -Wno-unused-parameter -Wall -Wparentheses -Winline -Wbad-function-cast -Wdisabled-optimization -m64 -O3 -fno-omit-frame-pointer  -MMD -MF ./Release/.deps/Release/obj.target/robotjs/src/xdisplay.o.d.raw   -c
Release/obj.target/robotjs/src/xdisplay.o: ../src/xdisplay.c \
 ../src/xdisplay.h
../src/xdisplay.c:
../src/xdisplay.h:
