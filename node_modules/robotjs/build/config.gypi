# Do not edit. File was generated by node-gyp's "configure" step
{
  "target_defaults": {
    "cflags": [],
    "defines": [],
    "include_dirs": [],
    "libraries": [],
    "default_configuration": "Release"
  },
  "variables": {
    "v8_enable_i8n_support": 1,
    "enable_lto": "false",
    "node_module_version": 137,
    "napi_build_version": 10,
    "node_builtin_shareable_builtins": [],
    "node_byteorder": "little",
    "clang": 0,
    "asan": 0,
    "control_flow_guard": "false",
    "coverage": "false",
    "dcheck_always_on": 0,
    "debug_nghttp2": "false",
    "debug_node": "false",
    "enable_pgo_generate": "false",
    "enable_pgo_use": "false",
    "error_on_warn": "false",
    "force_dynamic_crt": 0,
    "napi_build": "0.0",
    "host_arch": "x64",
    "target_arch": "x64",
    "nodedir": "/home/<USER>/.cache/node-gyp/24.3.0",
    "python": "/usr/bin/python3",
    "standalone_static_library": 1,
    "user_agent": "bun/1.2.18 npm/? node/v24.3.0 linux x64",
    "local_prefix": "/home/<USER>/Documents/Projects/desktop-pet"
  }
}
