{"name": "<PERSON><PERSON>s", "version": "0.6.0", "description": "Node.js Desktop Automation.", "main": "index.js", "typings": "index.d.ts", "scripts": {"test": "run-script-os", "test:darwin:linux": "jasmine 'test/**/*.js'", "test:win32": "jasmine test/**/*.js", "install": "prebuild-install || node-gyp rebuild", "prebuild": "prebuild -t 10.0.0 -t 11.0.0 -t 12.0.0 -t 13.0.0"}, "repository": {"type": "git", "url": "https://github.com/octalmage/robotjs.git"}, "keywords": ["Automation", "GUI", "mouse", "keyboard", "screenshot", "image", "pixel", "desktop", "<PERSON><PERSON>s", "screen", "recognition", "autohotkey", "machine", "learning", "color"], "author": "<PERSON>", "license": "MIT", "gypfile": true, "bugs": {"url": "https://github.com/octalmage/robotjs/issues"}, "homepage": "https://github.com/octalmage/robotjs", "dependencies": {"nan": "^2.14.0", "node-abi": "^2.13.0", "prebuild-install": "^5.3.3"}, "devDependencies": {"jasmine": "^2.99.0", "prebuild": "^9.1.1", "run-script-os": "^1.0.3", "targetpractice": "0.0.7"}}