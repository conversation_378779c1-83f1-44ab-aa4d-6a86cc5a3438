sudo: false
language: cpp

env:
  matrix:
    - TRAVIS_NODE_VERSION="8"
    - TRAVIS_NODE_VERSION="10"
    - TRAVIS_NODE_VERSION="12"
    - TRAVIS_NODE_VERSION="stable"

os:
  - linux
  - osx

cache:
  directories:
    - node_modules

git:
  depth: 5

addons:
  apt:
    sources:
      - ubuntu-toolchain-r-test
    packages:
      - libx11-dev
      - zlib1g-dev
      - libpng12-dev
      - libxtst-dev
      - g++-4.8
      - gcc-4.8

before_install:
  - rm -rf ~/.nvm && git clone https://github.com/creationix/nvm.git ~/.nvm && (cd ~/.nvm && git checkout `git describe --abbrev=0 --tags`) && source ~/.nvm/nvm.sh
  - nvm install $TRAVIS_NODE_VERSION
  - PATH=$PATH:`pwd`/node_modules/.bin
  # print versions
  - node --version
  - npm --version

  # use g++-4.8 on Linux
  - if [[ $TRAVIS_OS_NAME == "linux" ]]; then export CXX=g++-4.8; fi
  - $CXX --version

install:
  - npm install

script:
  - if [ "$TRAVIS_OS_NAME" = "osx" ]; then npm test; fi
  - if [ "$TRAVIS_OS_NAME" = "linux" ]; then xvfb-run npm test; fi

after_success:
  - if [[ $TRAVIS_TAG != "" ]]; then npm run prebuild -- -u $GITHUB_TOKEN; fi

notifications:
  webhooks:
    urls:
      - https://webhooks.gitter.im/e/e737dd5170be50cdba95
    on_success: change
    on_failure: always
    on_start: never
