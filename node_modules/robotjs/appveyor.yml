# http://www.appveyor.com/docs/appveyor-yml

# Test against these versions of Io.js and Node.js.
environment:
  matrix:
  # node.js
    - nodejs_version: "8"
    - nodejs_version: "10"
    - nodejs_version: "12"
    - nodejs_version: "Stable"

cache:
  - node_modules

clone_depth: 5

platform:
  - x86
  - x64

# Install scripts. (runs after repo cloning)
install:
  # Get the latest stable version of Node 0.STABLE.latest
  - ps: Install-Product node $env:nodejs_version
  - npm -g install npm
  - set PATH=%APPDATA%\npm;%PATH%
  # Typical npm stuff.
  - npm install

test_script:
  # Output useful info for debugging.
  - node --version
  - npm --version
  # run tests
  - npm test

on_success:
  - IF defined APPVEYOR_REPO_TAG_NAME npm run prebuild -- -u %GITHUB_TOKEN%

build: off
version: "{build}"
