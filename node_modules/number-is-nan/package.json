{"name": "number-is-nan", "version": "1.0.1", "description": "ES2015 Number.isNaN() ponyfill", "license": "MIT", "repository": "sindresorhus/number-is-nan", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "ava"}, "files": ["index.js"], "keywords": ["es2015", "ecmascript", "ponyfill", "polyfill", "shim", "number", "is", "nan", "not"], "devDependencies": {"ava": "*"}}