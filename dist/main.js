"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path = __importStar(require("path"));
const child_process_1 = require("child_process");
let mainWindow = null;
let tray = null;
// Window detection functions
function getWindowList() {
    return new Promise((resolve, reject) => {
        (0, child_process_1.exec)('wmctrl -lG', (error, stdout, stderr) => {
            if (error) {
                console.error('Error getting window list:', error);
                resolve([]);
                return;
            }
            const windows = [];
            const lines = stdout.trim().split('\n');
            for (const line of lines) {
                const parts = line.trim().split(/\s+/);
                if (parts.length >= 7) {
                    const id = parts[0];
                    const x = parseInt(parts[2]);
                    const y = parseInt(parts[3]);
                    const width = parseInt(parts[4]);
                    const height = parseInt(parts[5]);
                    const title = parts.slice(7).join(' ');
                    // Filter out our own window and system windows
                    if (!title.includes('Desktop Pet') &&
                        !title.includes('Desktop') &&
                        width > 50 && height > 50) {
                        windows.push({ id, x, y, width, height, title });
                    }
                }
            }
            resolve(windows);
        });
    });
}
function getTaskbarInfo() {
    return new Promise((resolve) => {
        const primaryDisplay = electron_1.screen.getPrimaryDisplay();
        const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize;
        const { width: fullWidth, height: fullHeight } = primaryDisplay.bounds;
        // Linux Mint taskbar is typically at the bottom
        const taskbarHeight = fullHeight - screenHeight;
        const taskbarY = screenHeight;
        resolve({
            x: 0,
            y: taskbarY,
            width: fullWidth,
            height: taskbarHeight
        });
    });
}
function createWindow() {
    // Get the primary display dimensions
    const primaryDisplay = electron_1.screen.getPrimaryDisplay();
    const { width, height } = primaryDisplay.workAreaSize;
    // Create the browser window with full screen transparency
    mainWindow = new electron_1.BrowserWindow({
        width: width,
        height: height,
        x: 0,
        y: 0,
        transparent: true,
        frame: false,
        alwaysOnTop: true,
        skipTaskbar: true,
        resizable: false,
        movable: false,
        minimizable: false,
        maximizable: false,
        closable: false,
        focusable: false,
        show: false,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            webSecurity: false
        }
    });
    // Initially set window to ignore mouse events (click-through)
    mainWindow.setIgnoreMouseEvents(true, { forward: true });
    // Load the HTML file
    mainWindow.loadFile(path.join(__dirname, 'renderer.html'));
    // Show window when ready
    mainWindow.once('ready-to-show', () => {
        mainWindow?.show();
    });
    // Prevent window from being closed
    mainWindow.on('close', (event) => {
        event.preventDefault();
    });
}
// IPC handlers for window detection and mouse events
function setupIPC() {
    // Handle window list requests
    electron_1.ipcMain.handle('get-windows', async () => {
        return await getWindowList();
    });
    // Handle taskbar info requests
    electron_1.ipcMain.handle('get-taskbar', async () => {
        return await getTaskbarInfo();
    });
    // Handle mouse event toggling for dragging
    electron_1.ipcMain.on('set-mouse-events', (event, enabled) => {
        if (mainWindow) {
            mainWindow.setIgnoreMouseEvents(!enabled, { forward: true });
        }
    });
}
function createTray() {
    // Create a simple tray icon (we'll use the new tray icon)
    const trayIcon = electron_1.nativeImage.createFromPath(path.join(__dirname, 'assets', 'tray-icon.jpg'));
    tray = new electron_1.Tray(trayIcon.resize({ width: 16, height: 16 }));
    // Create context menu for tray
    const contextMenu = electron_1.Menu.buildFromTemplate([
        {
            label: 'Desktop Pet',
            enabled: false
        },
        {
            type: 'separator'
        },
        {
            label: 'Quit',
            click: () => {
                electron_1.app.quit();
            }
        }
    ]);
    tray.setToolTip('Desktop Pet');
    tray.setContextMenu(contextMenu);
}
// This method will be called when Electron has finished initialization
electron_1.app.whenReady().then(() => {
    createWindow();
    createTray();
    setupIPC();
    electron_1.app.on('activate', () => {
        // On macOS it's common to re-create a window in the app when the
        // dock icon is clicked and there are no other windows open.
        if (electron_1.BrowserWindow.getAllWindows().length === 0)
            createWindow();
    });
});
// Quit when all windows are closed, except on macOS
electron_1.app.on('window-all-closed', () => {
    if (process.platform !== 'darwin')
        electron_1.app.quit();
});
// Prevent the app from quitting when all windows are closed
electron_1.app.on('before-quit', () => {
    if (mainWindow) {
        mainWindow.removeAllListeners('close');
        mainWindow.close();
    }
});
// Hide dock icon on macOS (though this is for Linux, keeping for compatibility)
if (process.platform === 'darwin') {
    electron_1.app.dock?.hide();
}
//# sourceMappingURL=main.js.map