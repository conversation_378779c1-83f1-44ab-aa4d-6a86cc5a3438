{"name": "desktop-pet", "version": "1.0.0", "description": "A desktop pet application for Linux", "main": "dist/main.js", "scripts": {"build": "tsc && cp src/renderer.html dist/ && cp -r src/assets dist/", "dev": "bun run build && electron dist/main.js", "start": "electron dist/main.js", "clean": "rm -rf dist"}, "keywords": ["electron", "desktop-pet", "linux"], "author": "Desktop Pet Developer", "license": "MIT", "devDependencies": {"@types/node": "^20.10.0", "electron": "^28.0.0", "typescript": "^5.3.0"}, "dependencies": {"child_process": "^1.0.2"}}