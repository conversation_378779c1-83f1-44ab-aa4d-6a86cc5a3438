# Desktop Pet for Linux

A cute desktop pet application built with Electron and TypeScript that runs on Linux. The pet moves around your screen in a transparent, click-through window.

## Features

- 🐱 **Advanced Pet AI**: Intelligent pet with multiple states (idle, roaming, falling, dragging)
- 🏠 **Window Detection**: Pet detects open windows and can walk on window tops as floors
- 🎯 **Linux Mint Taskbar Integration**: Pet recognizes and walks on the taskbar
- 🖱️ **Drag & Drop**: Click and drag the pet to place it anywhere on your screen
- 🌊 **Physics Engine**: Realistic gravity, bouncing, and collision detection
- 📏 **Scalable**: Automatically adjusts size based on screen resolution
- 🔍 **Smart Click-through**: Transparent window that only captures mouse when dragging
- 📌 **Always on Top**: Pet stays visible above all windows
- 🚫 **No Taskbar Icon**: Clean taskbar, no clutter
- 🔧 **System Tray**: Right-click tray icon to quit
- ⚡ **Built with**: Electron, TypeScript, and Bun

## Requirements

- Node.js (for Electron)
- Bun package manager
- Linux desktop environment
- ImageMagick (for asset conversion, optional)

## Installation

1. Clone or download this repository
2. Install dependencies:
   ```bash
   bun install
   ```

## Usage

### Development Mode
```bash
bun run dev
```

### Build and Run
```bash
bun run build
bun run start
```

### Clean Build Files
```bash
bun run clean
```

## How it Works

- The application creates a transparent, full-screen window that covers your entire desktop
- The window is set to ignore mouse events (click-through) so it won't interfere with your normal desktop usage
- A cute pet image moves around the screen at random intervals with smooth animations
- The pet occasionally performs random animations like bouncing or wiggling
- You can quit the application by right-clicking the system tray icon and selecting "Quit"

## Customization

### Changing the Pet Image
Replace `src/assets/pet.png` with your own pet image (64x64 pixels recommended).

### Adjusting Movement Behavior
Edit the `DesktopPet` class in `src/renderer.html`:
- Change `speed` property for movement speed
- Modify the interval in `startMovement()` for movement frequency
- Add new animations in the CSS and `addRandomAnimation()` method

### Tray Icon
Replace `src/assets/tray-icon.png` with your own 16x16 pixel icon.

## Troubleshooting

- If the pet doesn't appear, check that the assets are properly copied to the `dist/` folder
- If the tray icon doesn't show, ensure your desktop environment supports system tray icons
- For permission issues, make sure the application has the necessary permissions to create windows

## Technical Details

- **Main Process**: `src/main.ts` - Handles window creation, system tray, and app lifecycle
- **Renderer Process**: `src/renderer.html` - Contains the pet animation logic and UI
- **Assets**: `src/assets/` - Pet image and tray icon
- **Build Output**: `dist/` - Compiled JavaScript and copied assets

## License

MIT License - Feel free to modify and distribute as needed.
